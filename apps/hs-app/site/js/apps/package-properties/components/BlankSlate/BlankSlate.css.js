import PageLayout from '../PageLayout'
import Button from 'hsds/components/button'
import Icon from 'hsds/components/icon'
import Image from 'hsds/components/image'
import Link from 'hsds/components/link'
import { getColor } from 'hsds/utils/color'
import styled from 'styled-components'

export const CreatePropertyButtonUI = styled(Button)`
  margin-right: 20px;
`

export const ExamplePropertiesImageUI = styled(Image)`
  position: absolute;
  bottom: 0;
  right: 0;
  width: 405px;
  border-bottom-right-radius: 4px;
`

export const LayoutPageUI = styled(PageLayout)`
  &&&.is-fullPage {
    --hsds-page-max-width: 900px;
  }

  .c-PageCard {
    padding: 4px;
    position: relative;
    margin-top: 0 !important;
  }

  .c-PageSection {
    background-image: url(${hsGlobal.imagePath}blank-slate/properties/brush-stroke.png);
    background-position: right top;
    background-repeat: no-repeat;
    padding: 88px 50px 88px 72px;
    background-size: 66%;
  }

  header {
    border-bottom: 0 !important;
  }

  .c-Heading.is-md {
    font-size: 22px;
  }

  .c-PageHeader.is-withBottomMargin {
    margin-bottom: 0;
  }
`

export const LinkUI = styled(Link)`
  font-size: 14px;
`

export const ListUI = styled('ul')`
  font-size: 14px;
  list-style: none;
  margin-bottom: 40px;
  margin-left: 0;
  margin-top: 28px;
`

export const ListItemUI = styled('li')`
  line-height: 14px;
  margin: 1px 0;
  padding: 0;
`

export const ListIconUI = styled(Icon)`
  color: ${getColor('green.500')};
  display: inline-block;
  left: -7px;
  position: relative;
  top: 8px;
  height: 26px !important;
  width: 26px !important;

  &:before {
    content: '';
  }
`
