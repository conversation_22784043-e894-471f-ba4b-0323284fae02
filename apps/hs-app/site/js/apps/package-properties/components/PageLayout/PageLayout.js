import Page from 'hsds/components/page'
import PropTypes from 'prop-types'
import React from 'react'

const PageLayout = ({
  children,
  className,
  subtitle = '',
  title = '',
  withHeaderBorder = true,
}) => {
  const content = children ? (
    <Page.Content data-testid="PageLayout.Content">{children}</Page.Content>
  ) : null

  const header =
    subtitle || title ? (
      <Page.Header
        data-testid="PageLayout.Header"
        render={({ Subtitle, Title }) => (
          <div>
            {title && <Title>{title}</Title>}
            {subtitle && <Subtitle>{subtitle}</Subtitle>}
          </div>
        )}
        withBorder={withHeaderBorder}
      />
    ) : null

  return (
    <Page className={className} data-testid="PageLayout" fullPage>
      <Page.Card>
        <Page.Section>
          {header}
          {content}
        </Page.Section>
      </Page.Card>
    </Page>
  )
}

PageLayout.propTypes = {
  children: PropTypes.oneOfType([
    PropTypes.arrayOf(PropTypes.node),
    PropTypes.node,
  ]),
  className: PropTypes.string,
  subtitle: PropTypes.oneOfType([
    PropTypes.arrayOf(PropTypes.node),
    PropTypes.node,
  ]),
  title: PropTypes.oneOfType([
    PropTypes.arrayOf(PropTypes.node),
    PropTypes.node,
  ]),
  withHeaderBorder: PropTypes.bool,
}

export default PageLayout
