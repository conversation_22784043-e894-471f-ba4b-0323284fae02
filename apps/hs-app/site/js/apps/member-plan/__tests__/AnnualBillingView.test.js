import AnnualBillingView from '../views/AnnualBillingView'
import DetailsModel from '../models/DetailsModel'
import PaymentModel from '../models/PaymentModel'
import SubscriptionModel from '../models/SubscriptionModel'
import CardModel from '../models/CardModel'
import PricingPlanModel from '../models/PricingPlanModel'

describe('AnnualBillingView', () => {
  const App = (window.App = new Marionette.Application())
  let view, defaultOptions

  window.hsGlobal = window.hsGlobal || {}
  window.hsGlobal.memberPermissions = { manageAccount: true }

  beforeEach(() => {
    defaultOptions = {
      payments: {
        annual: new PaymentModel({
          lineItems: [
            {
              name: 'null',
              total: 10,
              quantity: 2,
            },
            {
              name: 'null 2',
              total: 20,
              quantity: 3,
            },
          ],
          promos: [],
          grandTotal: 100,
        }),
        monthly: new PaymentModel({
          lineItems: [
            {
              name: 'null',
              total: 10,
              quantity: 2,
            },
            {
              name: 'null 2',
              total: 20,
              quantity: 3,
            },
          ],
        }),
      },
      model: new SubscriptionModel(),
      card: new CardModel({ id: 123 }),
      details: new DetailsModel(),
      memberId: 1,
      hasPendingAnnualPayment: false,
      hasExistingAnnualPayment: false,
      plan: new PricingPlanModel(),
      features: { isFreePlan: false },
      isEligibleForRefund: true,
    }
  })

  it('should render annual billing without an annual payment', () => {
    view = new AnnualBillingView(defaultOptions).render()
    let modalTrigger = view.$el.find('.switch-to-annual-modal')
    expect(modalTrigger.length).toEqual(1)
    expect(modalTrigger.text().includes('Switch to Annual')).toBeTruthy()
  })

  it('should render auto-renew with existing annual payment', () => {
    view = new AnnualBillingView({
      ...defaultOptions,
      hasExistingAnnualPayment: true,
    }).render()
    let headers = view.$el.find('h3.js-annual-payment-title')
    expect(headers.length).toEqual(2)
    expect(headers.text().includes('Credit Remaining')).toBeTruthy()
    expect(headers.text().includes('Auto-renew')).toBeTruthy()
  })

  it('should have auto-renew toggled to active when enabled on subscription', () => {
    view = new AnnualBillingView({
      ...defaultOptions,
      hasExistingAnnualPayment: true,
      model: new SubscriptionModel({ annualAutoRenewEnabled: true }),
    }).render()
    let toggle = view.$el.find('span.js-auto-renew-toggle')
    expect(toggle).toHaveClass('active')
  })

  it('renders $0 promo message when annualPaymentIsZero', () => {
    const options = {
      ...defaultOptions,
      hasExistingAnnualPayment: true,
      payments: {
        ...defaultOptions.payments,
        annual: new PaymentModel({
          ...defaultOptions.payments.annual.attributes,
          grandTotal: 0,
        }),
      },
    }
    view = new AnnualBillingView(options).render()
    const message = view.$el.find('[data-cy="annualBilling.Credit.description"]')
    expect(message.text()).toContain(
      'Good news! You have a promotion that reduces your next bill to $0. There’s no need to add credit for now.'
    )
  })
})
