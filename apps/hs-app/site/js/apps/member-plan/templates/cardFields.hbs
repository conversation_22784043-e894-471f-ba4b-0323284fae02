
<div class="control-group">
    <label for="cardNum" class="control-label">Card Number</label>
    <div class="controls">
        <div id="braintree-card-number" class="hosted-field input-large"></div>
        <section id="logos" class="ccLogos">
            <span class="visa">Visa</span>
            <span class="mc">MasterCard</span>
            <span class="amex">AMEX</span>
            <span class="disc">Discover</span>
        </section>
    </div>
</div>
<div class="control-group">
    <label for="expDate" class="control-label">Expiration Date</label>
    <div class="controls">
        <div id="braintree-expiration-date" class="hosted-field input-large" ></div>
    </div>
</div>
<div class="control-group">
    <label for="cvv" class="control-label">CVV Code</label>
    <div class="controls">
        <div id="braintree-cvv" class="hosted-field input-small"></div>
    </div>
</div>

<div class="control-group">
    <label for="country" class="control-label">Country</label>
    <div class="controls">
        <select data-cy="country" id="country" name="country" class="input-large">
        {{#each countries}}
            {{#ifCond code '==' 'dis'}}
                <option disabled="disabled">{{name}}</option>
            {{else}}
                <option value="{{code}}"{{#ifCond code '==' ../country}} selected="selected"{{/ifCond}}>{{name}}</option>
            {{/ifCond}}
        {{/each}}
        </select>
        <p id="additionalTax" data-cy="additionalTax">You may be charged additional Sales Tax on top of your subscription based on State Law. See your bill for details.</p>
    </div>
</div>
{{#if shouldShowVat}}
<div class="control-group" id="vatContainer">
    <label for="vat" class="control-label">VAT</label>
    <div class="controls">
        <input data-cy="Vat" name="vat" id="vat" type="text" class="input-large formHelper" maxlength="20" value="{{vat}}">
    </div>
</div>
{{/if}}
<div class="control-group">
  <label for="street1" class=" control-label">Address 1</label>
  <div class="controls">
    <input data-cy="Address1" name="street1" type="text" id="street1" class="input-large formHelper" value="{{street1}}">
  </div>
</div>
<div class="control-group">
  <label for="street2" class=" control-label">Address 2</label>
  <div class="controls">
    <input data-cy="Address2" name="street2" type="text" id="street2" class="input-large formHelper" value="{{street2}}">
  </div>
</div>

<div class="control-group">
  <label for="city" class=" control-label">City</label>
  <div class="controls">
    <input data-cy="City" name="city" type="text" id="city" class="input-large formHelper" value="{{city}}">
  </div>
</div>
<div class="control-group">
    <label for="state" class="control-label" id="stateLabel">{{stateLabel}}</label>
    <div class="controls" id="stateContainer">
        {{#if isUnitedStates}}
            <select id="state" name="state" class="input-large" data-cy="StateProvince">
                <option value="">Please select</option>
                {{#each usStates}}
                    <option value="{{code}}"{{#ifCond code '==' ../state}} selected="selected"{{/ifCond}}>{{name}}</option>
                {{/each}}
            </select>
        {{else}}
            <input data-cy="StateProvince" name="state" id="state" class="input-large formHelper" type="text" maxlength="120" value="{{state}}">
        {{/if}}
    </div>
</div>
<div class="control-group">
  <label for="zip" class="control-label" id="zipLabel" >{{zipLabel}}</label>
  <div class="controls">
    <input data-cy="PostalZipCode" data-cy="zipCode" name="zip" type="text" class="input-small formHelper" id="zip" value="{{zip}}">
  </div>
</div>
