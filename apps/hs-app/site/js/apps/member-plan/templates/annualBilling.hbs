{{#if showAnnualBillingSection}}
    {{#if showAutoRenewSection}}
        <div class="u-mrg-t-10">
            <div class="o-flexy o-flexy--middle">
                <div class="o-flexy__item u-width-10 u-pad-r-8">
                    <header class="c-PageHeader u-mrg-b-3">
                        <h3 class="c-PageHeader__section-title js-annual-payment-title" data-cy="annualBilling.Credit.title">Credit Remaining</h3>
                    </header>
                    <p class="c-PageHeader__description" data-cy="annualBilling.Credit.description">
                        {{#if annualPaymentIsZero}}
                            Good news! You have a promotion that reduces your next bill to $0. There’s no need to add credit for now.
                        {{else if showSwitchToMonthlyMessage}}
                            Your annual credits are almost out! Unless you
                            <span class="switch-to-annual-modal switch-to-annual-modal--inline"></span> by
                            <span class="u-d-inline-block">{{nextPaymentDateAnnual}}</span>
                            you’ll switch back to monthly payments. You can also turn on auto-renew below!
                        {{else}}
                            {{#if isSelfServiceAllowed}}
                                You’re saving {{formatMoney annualDiscountDollars}} by paying annually, so nice work! Your next payment of
                                <span class="u-d-inline-block">{{formatMoney annualPayment.grandTotal false}}</span>
                                {{#if annualPayment.tax.isCharged}} (plus sales tax){{/if}} is estimated to be on
                                <span class="u-d-inline-block">{{nextPaymentDateAnnual}}.</span>
                                You can also <span class="switch-to-annual-modal switch-to-annual-modal--inline"></span> any time.
                            {{else}}
                                Your next payment of <span class="u-d-inline-block">{{formatMoney annualPayment.grandTotal false}}</span>
                                {{#if annualPayment.tax.isCharged}} (plus sales tax){{/if}} is estimated to be on
                                <span class="u-d-inline-block">{{nextPaymentDateAnnual}}.</span>
                            {{/if}}
                        {{/if}}
                    </p>
                </div>
                <div id="js-react-credit-remaining-circle-chart" class="o-flexy__item"></div>
            </div>
        </div>

        {{#if isSelfServiceAllowed}}
            <header class="c-PageHeader c-PageHeader u-mrg-t-10 u-mrg-b-3">
                <h3 class="c-PageHeader__section-title js-annual-payment-title" data-cy="annualBilling.AutoRenew.title">Auto-renew annual payment
                    <i id="auto-renew-popover" class="icon-info" rel="popover" data-cy="annualBilling.AutoRenew.popoverTrigger" data-toggle="popover" data-trigger="click" data-html="true" data-container="body" data-content="If auto-renew is disabled, you will return to monthly billing when your annual credits run out."></i>
                </h3>
            </header>
            <div class="o-flexy o-flexy--middle">
                <div class="o-flexy__item u-width-10">
                    <p class="c-PageHeader__description u-pad-r-4 u-mrg-t-0 auto-renew-label js-auto-renew-label{{#if subscription.annualAutoRenewEnabled}} active{{/if}}">
                        <span class="autoRenewEnabledLabel" data-cy="annualBilling.AutoRenew.enableTitle">
                            This means we'll charge the card on file when your credits run low and it's time to make another annual payment.
                        </span>
                        <span class="autoRenewDisabledLabel" data-cy="annualBilling.AutoRenew.disableTitle">
                            Enable auto-renew and you won't have to worry about remembering to make a payment when your credits run low.
                            It also ensures that you keep the {{formatMoney annualDiscountDollars}} annual discount.
                        </span>
                    </p>
                </div>
                <div class="o-flexy__item">
                    <label class="c-switch c-switch--sm">
                        <span class="c-switch-toggle js-auto-renew-toggle{{#if subscription.annualAutoRenewEnabled}} active{{/if}}" data-cy="annualBilling.AutoRenew.switch"></span>
                    </label>
                </div>
            </div>
        {{/if}}
    {{else}}
    <header class="c-PageHeader">
        <div class="o-flexy">
            <div class="o-flexy__block u-width-10 u-pad-r-5">
                <h3 class="c-PageHeader__section-title" data-cy="annualBilling.Discount.title">Save {{formatMoney annualDiscountDollars}}/yr by switching to annual billing</h3>
                <p class="c-PageHeader__description" data-cy="annualBilling.Discount.subtitle">
                    Your price will drop to
                    {{#if isContactsBasedBillingType}}
                      {{formatMoney monthlyDiscountPrice}}/month.
                    {{else}}
                      {{#if usesBasePriceWithAnnualDiscount}}
                        {{formatMoney monthlyDiscountPrice}}/month.
                      {{else}}
                        {{formatMoney annualPricePerUser}}/User/month.
                      {{/if}}
                    {{/if}}
                    Huzzah!
                </p>
            </div>
            <div class="o-flexy__item">
                <div class="switch-to-annual-modal"></div>
            </div>
        </div>
    </header>
    {{/if}}
{{/if}}
