/* global App */
import HS from 'hs-core'
import React from 'react'
import { ReactMounter } from '@helpscout/brigade'

import createSalesTaxStore from '@member-plan/react/SalesTaxLineItem/createStore'

import template from '@member-plan/templates/annualBilling.hbs'

import AnnualAutoRenewToggleModalView from '@member-plan/views/AnnualAutoRenewToggleModalView'
import ContactModalView from '@member-plan/views/ContactModalView'

import AnnualBillSwitchModal from '@member-plan/react/components/AnnualBillSwitchModal'
import CreditRemainingCircleChart from '@member-plan/react/screens/PlanOverviewScreen/CreditRemainingCircleChart'

function noop() {}

const AnnualBillingView = Marionette.ItemView.extend({
  className: 'hsds-react',
  components() {
    return {
      '#js-react-credit-remaining-circle-chart': (
        <CreditRemainingCircleChart
          totalCreditsRemaining={this.totalCreditsRemaining}
          totalForNextAnnualPayment={this.payments.annual.get('grandTotal')}
        />
      ),
      '.switch-to-annual-modal': {
        component: AnnualBillSwitchModal,
        initialState: {
          card: this.card,
          details: this.details,
          features: this.features,
          member: this.member,
          payments: {
            monthly: this.payments.monthly ? this.payments.monthly.toJSON() : {},
            annual: this.payments.annual ? this.payments.annual.toJSON() : {},
          },
          plan: this.plan,
          taxStore: this.taxStore,
          showAutoRenewSection:
            this.model.get('annualAutoRenewEnabled') ||
            this.hasExistingAnnualPayment,
          subId: this.subId,
          subscription: this.model,
          totalCreditsRemaining: this.totalCreditsRemaining,
        },
        externalActions: {
          updateContactInfo: this.updateContactInfo.bind(this),
        },
      },
    }
  },

  template: template,

  ui: {
    autoRenewToggle: '.js-auto-renew-toggle',
  },

  events: {
    'click @ui.autoRenewToggle': function (event) {
      this.showAutoRenewToggleModal(
        event,
        !this.ui.autoRenewToggle.hasClass('active')
      )
    },
  },

  initialize() {
    this.payments = this.options.payments
    this.model = this.options.model
    this.card = this.options.card
    this.details = this.options.details
    this.member = this.options.member
    this.features = this.options.features
    this.hasPendingAnnualPayment = this.options.hasPendingAnnualPayment
    this.hasExistingAnnualPayment = this.options.hasExistingAnnualPayment
    this.plan = this.options.plan
    this.company = this.options.company
    this.nextPaymentDateAnnual = this.options.nextPaymentDateAnnual
    this.totalCreditsRemaining = this.options.totalCreditsRemaining
    this.isEligibleForRefund = this.options.isEligibleForRefund

    const {
      payments: { annual: payment },
    } = this

    this.taxStore = createSalesTaxStore({
      details: this.details,
      payment: payment,
    })

    this.subId = HS.Controller.PubSub.subscribe('Ajax:Error', noop, this)

    this.listenTo(App, 'annual-auto-renew-toggled', this.handleAutoRenewChange)
    this.listenTo(App, 'annual-payment-failed', this.annualPaymentFailed)
    this.listenTo(this.card, 'change', this.render)
    this.listenTo(payment, 'change:amount', this.render)
  },

  onShow() {
    HS.Utils.Main.initPopovers()
  },

  handleAutoRenewChange(enableAutoRenew) {
    // re-render to easily switch between "no annual payment", "no auto-renew" and "with auto-renew" states
    this.model.set({ annualAutoRenewEnabled: enableAutoRenew })
    this.render()
  },

  showAutoRenewToggleModal(event, enableAutoRenew) {
    event && event.preventDefault()

    App.modalRegion.show(
      new AnnualAutoRenewToggleModalView({
        memberId: this.member.id,
        card: this.card,
        details: this.details,
        enableAutoRenew,
        isEligibleForRefund: this.isEligibleForRefund,
      })
    )
  },

  // total monthly discount on Contact-based plans = (full tier price) - (discounted tier price)
  getMonthlyDiscountDollarsOnContactsBasedPricing() {
    const annualLineItem = this.payments.annual.get('lineItems').first()
    const monthlyLineItem = this.payments.monthly.get('lineItems').first()
    const monthlyPrice = Number.parseInt(monthlyLineItem.get('tierPrice'))
    const monthlyDiscountedPrice = Number.parseInt(annualLineItem.get('price'))

    return monthlyPrice - monthlyDiscountedPrice
  },

  // total monthly discount on User-based plans = (plan discount) * (no of users)
  getMonthlyDiscountDollarsOnUsersBasedPricing() {
    const annualDiscountPerUser = this.plan.get('annualDiscount')
    const usesBasePriceWithAnnualDiscount = this.plan.get(
      'usesBasePriceWithAnnualDiscount'
    )
    const userQuantityKey = usesBasePriceWithAnnualDiscount
      ? 'totalUsers'
      : 'quantity'
    const totalUsers = this.payments.monthly
      .get('lineItems')
      .first()
      .get(userQuantityKey)

    return totalUsers * annualDiscountPerUser
  },

  calculateDiscountDollars() {
    const monthlyDiscountDollars = this.plan.get('isContactsBasedBillingType')
        ? this.getMonthlyDiscountDollarsOnContactsBasedPricing()
        : this.getMonthlyDiscountDollarsOnUsersBasedPricing()

    return {
      monthlyDiscountDollars,
      annualDiscountDollars: monthlyDiscountDollars * 12,
    }
  },

  serializeData() {
    const {
      annualDiscountDollars,
      monthlyDiscountDollars,
    } = this.calculateDiscountDollars()
    const annualGrandTotal = this.payments.annual.get('grandTotal')
    const annualPaymentIsZero = annualGrandTotal === 0

    const annualDiscountPerUser = this.plan.get('annualDiscount')
    const monthlyPricePerUser = this.plan.get('price')
    const annualPricePerUser = Math.abs(
      monthlyPricePerUser - annualDiscountPerUser
    )
    const monthlyDiscountPrice = annualGrandTotal / 12
    const usesBasePriceWithAnnualDiscount = this.plan.get(
      'usesBasePriceWithAnnualDiscount'
    )
    const isSelfServiceAllowed = this.plan.get('isSelfServiceAllowed')

    let showSwitchToMonthlyMessage = false

    // we'll show a message about credits running out to all customers whose
    // credit balance is lower than the amount they would have to pay without
    // the annual discount
    const monthlySubtotal = this.payments.monthly.get('subtotal')
    const monthlySubtotalWithoutAnnualDiscount =
      monthlySubtotal + monthlyDiscountDollars
    if (
      isSelfServiceAllowed &&
      !this.model.get('annualAutoRenewEnabled') &&
      monthlySubtotalWithoutAnnualDiscount > this.totalCreditsRemaining
    ) {
      showSwitchToMonthlyMessage = true
    }

    // we want to show the auto-renew section when there is an annual payment with any credits
    // or if the credits run out, but the auto-renew flag is still enabled
    const hasAnnualPaymentOrAutoRenew =
      this.hasExistingAnnualPayment || this.model.get('annualAutoRenewEnabled')

    // we advertise paying annually to users with an annual payment that costs
    // something (i.e. Free plan and 100% promos excluded), who have a card,
    // don't have a pending/existing annual payment and can self service
    const showAnnualPaymentAd =
      !annualPaymentIsZero &&
      !this.features.isFreePlan &&
      this.card.get('id') !== null &&
      !this.hasPendingAnnualPayment &&
      !this.hasExistingAnnualPayment &&
      isSelfServiceAllowed

    return {
      subscription: this.model.toJSON(),
      annualPayment: this.payments.annual.toJSON(),
      annualDiscountDollars,
      annualPricePerUser,
      monthlyDiscountPrice,
      showAnnualBillingSection:
        showAnnualPaymentAd || hasAnnualPaymentOrAutoRenew,
      showAutoRenewSection: hasAnnualPaymentOrAutoRenew,
      isContactsBasedBillingType: this.plan.get('isContactsBasedBillingType'),
      isSelfServiceAllowed,
      nextPaymentDateAnnual: this.nextPaymentDateAnnual,
      usesBasePriceWithAnnualDiscount,
      showSwitchToMonthlyMessage,
      annualPaymentIsZero
    }
  },

  updateContactInfo() {
    App.modalRegion.showSlim(
      new ContactModalView({
        model: this.options.details,
        member: this.options.member,
      }),
      'Billing Contact Information'
    )
  },

  annualPaymentFailed() {
    HS.Utils.Main.error('There was an error processing this payment.')
  },
})

export default ReactMounter(AnnualBillingView)
