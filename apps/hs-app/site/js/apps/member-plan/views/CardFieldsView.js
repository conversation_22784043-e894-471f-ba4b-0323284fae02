import App from '../App'
import template from '../templates/cardFields.hbs'
import CardUtils from '../utils/cc'
import Constants from '../utils/defs'

export default Marionette.ItemView.extend({
  // model is member-plan/models/CardModel.js
  template,
  className: 'CardFieldsView',
  events: {
    'change @ui.country': 'onCountryChange',
  },
  ui: {
    country: '#country',
    vat: '#vat',
    street1: '#street1',
    street2: '#street2',
    city: '#city',
    state: '#state',
    zip: '#zip',
    stateContainer: '#stateContainer',
    stateLabel: '#stateLabel',
    zipLabel: '#zipLabel',
    vatContainer: '#vatContainer',
    additionalTax: '#additionalTax',
  },

  initialize: function (opt) {
    this.card = opt.card.toJSON()
  },

  setCardLogo: function () {
    CardUtils.setCardLogo(this)
  },
  onCountryChange() {
    const country = this.ui.country.val()

    this.updateLabels(country)

    const shouldShowVat = this.shouldShowVat(country)

    if (shouldShowVat) {
      this.ui.vatContainer.show()
    } else {
      this.ui.vatContainer.hide()
    }

    this.setCardLogo()
  },
  onShow: function () {
    this.setCardLogo()
    this.updateLabels(this.ui.country.val())
  },

  updateLabels: function (country) {
    const isUnitedStates = country === 'US' || !country
    const stateLabel = isUnitedStates ? 'State' : 'State/Province'
    const zipLabel = isUnitedStates ? 'Zip Code' : 'Postal Code'

    if (isUnitedStates) {
      this.ui.additionalTax.show()
    } else {
      this.ui.additionalTax.hide()
    }

    this.ui.stateLabel.text(stateLabel)
    this.ui.zipLabel.text(zipLabel)

    this.isUnitedStates = isUnitedStates

    this.updateStateField(isUnitedStates)
  },
  getStateDropdown: function () {
    const usStates = App.usStates.toJSON()
    const options = usStates
      .map(state => `<option value="${state.code}">${state.name}</option>`)
      .join('')
    return `<select id="state" name="state" class="input-large" data-cy="StateProvince">${options}</select>`
  },
  getStateTextInput: function () {
    return `<input data-cy="StateProvince" name="state" id="state" class="input-large formHelper" type="text" maxlength="120">`
  },
  updateStateField: function (isUnitedStates) {
    const newField = isUnitedStates
      ? this.getStateDropdown()
      : this.getStateTextInput()

    // Replace the state field without re-rendering
    this.ui.stateContainer.html(newField)
    this.ui.state = this.ui.stateContainer.find('#state')
  },
  shouldShowVat(country) {
    return App.vatCountries
      .toJSON()
      .some(vatCountry => vatCountry.code === country)
  },
  serializeData: function () {
    const details = this.options.details

    const data = this.model.toJSON()

    // These fields are currently stored on the member details table
    const { zip, country, vat, street1, street2, city, state } =
      details.toJSON()

    data.countries = App.countries.toJSON()
    data.vatCountries = App.vatCountries.toJSON()
    data.usStates = App.usStates.toJSON()

    // because we pre-select US when no address provided yet, then treat empty as US
    const isUnitedStates = country === 'US' || !country
    const shouldShowVat = this.shouldShowVat(country)
    const stateLabel = isUnitedStates ? 'State' : 'State/Province'
    const zipLabel = isUnitedStates ? 'Zip Code' : 'Postal Code'

    return {
      ...data,
      ...this.card,
      zip,
      country,
      vat,
      street1,
      street2,
      city,
      state,
      isUnitedStates,
      shouldShowVat,
      stateLabel,
      zipLabel,
    }
  },
  getFormValues: function () {
    return {
      country: this.ui.country.val(),
      vat: this.ui.vat.val(),
      street1: this.ui.street1.val(),
      street2: this.ui.street2.val(),
      city: this.ui.city.val(),
      state: this.ui.state.val(),
      zip: this.ui.zip.val(),
      paymentType: Constants.CREDIT_CARD_PAYMENT_TYPE,
    }
  },
})
