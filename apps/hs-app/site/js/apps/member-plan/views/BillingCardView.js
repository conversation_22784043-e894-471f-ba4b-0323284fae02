import template from '../templates/billingCard.hbs'
import CardUtils from '../utils/cc'
import { ANNUAL_CYCLE } from '../utils/defs'
import CardFieldsView from '../views/CardFieldsView'
import { openBeaconArticle } from '@common/utils/beaconHelper'
import braintree from 'braintree-web'
import HS from 'hs-core'

let prevAlertBox = null

module.exports = Marionette.Layout.extend({
  // model is member-plan/models/CardModel.js
  template: template,
  className: 's-billing-card',
  events: {
    'click @ui.beaconTrigger': 'openBeacon',
    'click .form-actions .btn-submit': 'submitForm',
  },
  ui: {
    annualPaymentCallout: '.js-annual-payment-callout',
    beaconTrigger: '#beacon-trigger',
    button: 'button.btn-submit',
    cardNumber: '#cardNumber',
    expMonth: '#expMonth',
    expYear: '#expYear',
    securityCode: '#securityCode',
    zip: '#zip',
    country: '#country',
    changePlanLink: 'a.js-changePlan',
  },

  initializeHostedFields: function (parent) {
    braintree.client.create(
      { authorization: appData.braintreeTokenizationKey },
      (clientErr, clientInstance) => {
        if (clientErr) {
          HS.Plugins.Flash.error('Error creating payment client')
          return
        }

        braintree.hostedFields.create(
          {
            client: clientInstance,
            styles: {
              input: {
                'font-size': '14px',
                color: '#314351',
                padding: '4px 6px 4px 8px',
              },
              '.invalid': { color: 'rgb(184, 53, 37)' },
            },
            fields: {
              number: {
                selector: '#braintree-card-number',
                placeholder: '•••• •••• •••• ••••',
              },
              cvv: {
                selector: '#braintree-cvv',
              },
              expirationDate: {
                selector: '#braintree-expiration-date',
                placeholder: 'MM/YY',
              },
            },
          },
          (hostedFieldsErr, hostedFieldsInstance) => {
            if (hostedFieldsErr) {
              HS.Plugins.Flash.error('Error creating payment fields')
              return
            }

            hostedFieldsInstance.on('cardTypeChange', function (event) {
              let cardType = ''

              if (event.cards.length === 1) {
                cardType = event.cards[0].type
              } else {
                cardType = ''
              }
              CardUtils.setHostedFieldsCardLogo(parent, cardType)
            })
            this.hostedFieldsInstance = hostedFieldsInstance

            this.hostedFieldsInstance.focus('number')
          }
        )
      }
    )
  },

  regions: {
    cardFields: '#cardFields',
    alertBox: '#cardErrors',
  },
  setButton: function (state) {
    if (this.ui.button && typeof this.ui.button.button == 'function') {
      this.ui.button.button(state)
    }
  },
  resetButton: function () {
    this.setButton('reset')
  },
  initialize: function () {
    prevAlertBox = App.alertBoxRegion

    App.alertBoxRegion = this.alertBox

    // inactive-payment-failed could be called if there is an error
    // processing a payment.
    // See: ChangePlanView.submitPayment()
    this.listenTo(App, 'billing-change-plan', this.doChangePlanView)
    this.listenTo(App, 'inactive-payment-failed', this.resetButton)
    this.listenTo(App, 'payment-cycle-changed', this.toggleAnnualPaymentCallout)
  },
  onClose: function () {
    App.alertBoxRegion = prevAlertBox
    App.trigger('billing-card-view-closed')
    if (this.promise) {
      HS.Utils.Main.abort(this.promise)
    }
  },
  onShow: function () {
    let paymentType = false
    if ('paymentType' in this.options) {
      paymentType = this.options.paymentType
    }

    this.childView = new CardFieldsView({
      model: this.model,
      card: this.options.card,
      details: this.options.details,
      paymentType: paymentType,
    })

    this.cardFields.show(this.childView)

    if (this.options.error) {
      HS.Utils.Ajax.fail(
        this.options.error,
        this.options.error.status,
        this.options.error.statusText
      )
    }
    this.toggleAnnualPaymentCallout()
  },
  onRender: function () {
    _.delay(() => {
      this.$('#braintree-card-number').focus()
    }, 100)

    this.initializeHostedFields(this)
  },
  serializeData: function () {
    const data = {
      isEligibleForRefund: this.options.isEligibleForRefund,
      isRoadblock: appData.base === 'roadblock',
    }

    if ('saveButton' in this.options) {
      data.buttonText = this.options.saveButton
    } else {
      data.buttonText = "Let's do this!"
    }

    return data
  },
  doChangePlanView: function () {
    App.trigger('showHelpdeskPlans')
  },
  submitForm: function (event) {
    if (event) {
      event.preventDefault()
    }

    this.setButton('loading')

    if (!this.hostedFieldsInstance) {
      HS.Plugins.Flash.error('Error submitting payment method')
      this.resetButton()
      return
    }

    this.hostedFieldsInstance.tokenize((err, payload) => {
      if (err) {
        if (err.code === 'HOSTED_FIELDS_FIELDS_INVALID') {
          HS.Utils.Main.error('Some payment input fields are invalid.')
        } else if (err.code !== 'HOSTED_FIELDS_FIELDS_EMPTY') {
          HS.Utils.Main.error(
            'There was an error processing your payment. Please try again.'
          )
        }
        this.resetButton()
        return
      }

      // Collect additional form values
      const formValues = this.childView.getFormValues()
      formValues.cycle = App.controller.billingFrequency
      formValues.nonce = payload.nonce

      delete formValues['ptype']

      if (this.options.handleSaveCard) {
        this.promise = App.controller.doUpdateCard(
          this.model,
          formValues,
          _.bind(this.close, this),
          _.bind(this.resetButton, this)
        )
      } else {
        App.trigger('billing-model-updated', formValues)
      }
    })
  },
  openBeacon(event) {
    openBeaconArticle('5f08b88804286306f8068585')
  },
  toggleAnnualPaymentCallout() {
    if (App.controller.billingFrequency === ANNUAL_CYCLE) {
      this.ui.annualPaymentCallout.show()
      return
    }

    this.ui.annualPaymentCallout.hide()
  },
})
