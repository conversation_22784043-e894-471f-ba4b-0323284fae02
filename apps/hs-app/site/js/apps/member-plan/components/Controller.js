import HSController from '../../common/controllers/HSController'
import App from '../App'
import CountryCollection from '../collections/CountryCollection'
import DiscountCollection from '../collections/DiscountCollection'
import InvoiceCollection from '../collections/InvoiceCollection'
import PricingPlanCollection from '../collections/PricingPlanCollection'
import PromosCollection from '../collections/PromoCollection'
import UsStateCollection from '../collections/UsStateCollection'
import Api from '../components/Api'
import BillingCardModel from '../models/CardModel'
import DetailsModel from '../models/DetailsModel'
import PaymentModel from '../models/PaymentModel'
import PricingPlanModel from '../models/PricingPlanModel'
import SubscriptionModel from '../models/SubscriptionModel'
import Constants from '../utils/defs.js'
import AddOnsView from '../views/AddOnsView'
import AvailablePlansView from '../views/AvailablePlansView'
import ChangePlanView from '../views/ChangePlanView'
import InvoiceOverviewView from '../views/InvoiceOverviewView'
import PlanMigrationView from '../views/PlanMigrationView'
import PlanOverviewView from '../views/PlanOverviewView'
import RoadblockView from '../views/RoadblockView'
import SideNavView from '../views/SideNavView'
import AiDraftsBillingView from '@member-plan/views/AiDraftsBillingView'
import MessagesBillingView from '@member-plan/views/MessagesBillingView'
import HS from 'hs-core'
import $ from 'jquery'
import moment from 'moment'

let isFirstRun = true
let prepayCount = 0

module.exports = HSController.extend({
  hasFailedAnnualPayment: false,
  initialize: function (options) {
    HSController.prototype.initialize.call(this)
    App.controller = this

    this.listenTo(App, 'paymentMade', this.paymentMade)
    this.listenTo(App, 'uninstall-docs', this.uninstallDocs)
    this.listenTo(App, 'billing-card-saved', this.billingCardSaved)
    this.listenTo(App, 'payment-cycle-changed', this.cycleChanged)

    if (appData.base !== 'roadblock') {
      this.init()
    } else {
      this.initRoadblock()
    }

    // Set billingFrequency *after* init/loadFirstRunData
    this.billingFrequency = getBillingFrequency()
  },
  cycleChanged: function (cycle) {
    this.billingFrequency = cycle
  },
  initRoadblock: function () {
    if (hsGlobal.memberPermissions.manageAccount) {
      loadFirstRunData()
    } else {
      loadNonOwnerRoadblockData()
    }

    if (appData.error) {
      HS.Plugins.Flash.error(appData.error)
      appData.error = null
    }
  },
  init: function () {
    loadFirstRunData()
    this.loadSideNav()
    triggerSideNav('plan')
    if (appData.error) {
      HS.Plugins.Flash.error(appData.error)
      appData.error = null
    }
    toggleTrialNav()
  },
  loadSideNav: function () {
    App.sideNav.show(
      new SideNavView({
        invoices: App.data.invoices,
        isOwner: App.data.member.isOwner,
        plan: App.data.plan,
        selfServiceAddOns: App.data.selfServiceAddOns,
        subscription: App.data.subscription,
        aiDraftsSpendingStatus: App.data.aiDrafts.aiDraftsSpendingStatus || 0,
      })
    )
  },
  showChangePlan: function () {
    if (redirectIfWithoutManageAccountPermission()) {
      return
    }

    showChangePlan()
  },
  billingCardSaved: function (updatedModel) {
    App.data.card = new BillingCardModel(updatedModel)
  },
  uninstallDocs: function () {
    const url =
      appData.base === 'roadblock'
        ? `${hsGlobal.path}dashboard/`
        : `${hsGlobal.path}members/plan/`
    // todo append docs company app id to the url somehow to fix https://helpscout.atlassian.net/browse/CTRIAGE-442
    HS.Utils.Ajax.del(`${hsGlobal.apiPath}apps/docs/id-here`, () => {
      HS.Utils.Main.redirectToUrl(url)
    })
  },
  checkPrepays: function () {
    let doTheAction = true
    if (prepayCount > 0) {
      doTheAction = confirm(
        "Looks like you've already made an annual payment today. Not that we're complaining, but are you sure you'd like to make another one?"
      )
    }
    return doTheAction
  },
  paymentMade: function (method, cycle) {
    if (cycle == 'annual' || cycle == 'annual-prepay') {
      prepayCount++
    }
  },
  invoicesView: function () {
    if (
      redirectIfWithoutManageAccountPermission() ||
      redirectIfPartnerSourced()
    ) {
      return
    }

    const options = {
      collection: App.data.invoices,
      invoicesTotalCount: App.data.invoicesTotalCount,
      model: App.data.subscription,
      features: App.data.features,
      plan: App.data.plan,
      details: App.data.details,
      member: App.data.member,
      card: App.data.card,
      payments: App.data.payments,
      company: App.data.company,
      totals: App.data.totals,
      hasPendingAnnualPayment: App.data.hasPendingAnnualPayment,
      hasExistingAnnualPayment: App.data.hasExistingAnnualPayment,
      shouldVerifyPassword: appData.verifyPassCancelAccount,
    }
    App.planOverview.show(new InvoiceOverviewView(options))
    triggerSideNav('invoices')
  },
  addOnsView: function () {
    if (
      redirectIfWithoutManageAccountPermission() ||
      redirectIfPartnerSourced()
    ) {
      return
    }
    if (App.data.selfServiceAddOns.length == 0) {
      App.appRouter.navigate(`/plan/`, { replace: true, trigger: true })
      return
    }

    const options = {
      selfServiceAddOns: App.data.selfServiceAddOns,
      plan: {
        name: App.data.plan.get('name'),
        isPlus: App.data.plan.get('isPlusPlan'),
        includedLightUsers: App.data.features.includedLightUsers,
        lightUserCount: App.data.company.lightUserCount,
        remainingLightUserCount: App.data.company.remainingLightUserCount,
      },
    }
    App.planOverview.show(new AddOnsView(options))
    triggerSideNav('add-ons')
  },
  planView: function (section) {
    showPlanPage(section)
    triggerSideNav('plan')
  },
  availablePlansView: function () {
    if (redirectIfPartnerSourced()) {
      return
    }

    const tab = 'available-plans'
    if (redirectIfWithoutManageAccountPermission()) {
      return
    }
    if (!App.data.plan.get('isSelfServiceAllowed')) {
      safelyNavigateToPlanPage()
      return
    }

    const availablePlansView = new AvailablePlansView(getViewData(tab))
    App.planOverview.show(availablePlansView)
    triggerSideNav(tab)
  },
  messagesBillingView: function () {
    if (App.data.messages.tiers === undefined) {
      showPlanPage()
      return
    }

    const options = {
      messages: App.data.messages,
      isTrial: App.data.subscription.get('isTrial'),
    }

    App.planOverview.show(new MessagesBillingView(options))
    triggerSideNav('messages-billing')
  },
  aiDraftsBillingView: function () {
    const options = {
      aiDrafts: App.data.aiDrafts,
      isTrial: App.data.subscription.get('isTrial'),
    }
    if (
      hsGlobal.features.isAiDraftsUsageBasedBillingEnabled &&
      hsGlobal.memberPermissions.manageAccount
    ) {
      App.planOverview.show(new AiDraftsBillingView(options))
      triggerSideNav('drafts-billing')
    } else {
      showPlanPage()
    }
  },
  planMigrationView: function () {
    if (redirectIfWithoutManageAccountPermission()) {
      return
    }
    if (App.data.legacyPlanMigration.newPlan == null) {
      App.appRouter.navigate(`/plan/`, { replace: true, trigger: true })
      return
    }

    const options = {
      payments: App.data.legacyPlanMigration.payments,
      planChangeDate: App.data.legacyPlanMigration.planChangeDate,
      monthsToMigration: App.data.legacyPlanMigration.monthsToMigration,
      currentPlan: App.data.plan,
      newPlan: App.data.legacyPlanMigration.newPlan,
      isOwner: App.data.member.isOwner,
      isPayingAnnually: isPayingAnnually(),
      subscriptionAge: App.data.subscription.get('age'),
      postMigrationNextPaymentDateAnnual:
        App.data.legacyPlanMigration.postMigrationNextPaymentDateAnnual,
      legacyPlanMigration: App.data.legacyPlanMigration,
    }
    App.planOverview.show(new PlanMigrationView(options))
    triggerSideNav('plan-migration')
  },
  roadblock: function () {
    showRoadblockPage()
  },
  showBilling: function () {
    if (redirectIfWithoutManageAccountPermission()) {
      return
    }

    showRoadblockPage()
  },
  getChangePlanInfo: function (planId, setGlobal = true) {
    return $.when(Api.getChangePlanInfo(planId)).then(json => {
      const changePlan = getChangePlanInfoFromJson(planId, json)

      if (setGlobal) {
        App.data.changePlan = changePlan
      }

      return changePlan
    })
  },
  doRetryPayment: function (params, callback, errorCallback) {
    Api.retryPayment(params, callback, errorCallback)
  },
  doUpdateCard: function (
    model,
    params,
    callback,
    errorCallback,
    broadcastEvent
  ) {
    return Api.updateCard(
      model,
      params,
      callback,
      errorCallback,
      broadcastEvent
    )
  },
  doRemoveCard: function (model, callback, errorCallback) {
    Api.removeCard(model, callback, errorCallback)
  },
  doSetMessagesSpendingLimit: function (params, callback, errorCallback) {
    Api.setMessagesSpendingLimit(params, callback, errorCallback)
  },
  doSetAiDraftsSpendingLimit: function (params, callback, errorCallback) {
    Api.setAiDraftsSpendingLimit(params, callback, errorCallback)
  },
  doEnableAIDrafts: function (data, callback, errorCallback) {
    Api.enableAIDrafts(data, callback, errorCallback)
  },
  displayUpdatedPlan() {
    refreshPlanData().then(() => {
      toggleTrialNav()
      // reload the side nav in case we made an annual payment and we now have an invoice
      this.loadSideNav()
      showPlanPage()
    })
  },
  displayUpdatedAddOns() {
    refreshPlanData().then(() => {
      this.addOnsView()
    })
  },
  refreshAiDrafts() {
    return refreshPlanData()
  },
  refreshPlanData(sideNavTab = null) {
    refreshPlanData().then(() => {
      toggleTrialNav()
      // reload the side nav in case we made an annual payment and we now have an invoice
      this.loadSideNav()
      // re-activate the correct sidenav link
      sideNavTab && triggerSideNav(sideNavTab)
    })
  },
  doChangePlan: function (planId, data, callback, errorCallback) {
    return Api.doChangePlan(planId, data, callback, errorCallback)
  },
  makePayment: function (memberId, data) {
    const reqParams = {
      url: `${hsGlobal.apiPath}members/payment?storedCard=0`,
      params: data,
    }
    return HS.Utils.Ajax.post(reqParams)
  },
  makePaymentWithStoredCard: function (memberId, data) {
    const reqParams = {
      url: `${hsGlobal.apiPath}members/payment?storedCard=1`,
      params: data,
    }
    return HS.Utils.Ajax.post(reqParams)
  },
  toggleAnnualAutoRenew: function (memberId, enableAutoRenewal) {
    const reqParams = {
      url: `${hsGlobal.apiPath}members/toggle-annual-auto-renew`,
      params: { enableAutoRenewal },
    }
    return HS.Utils.Ajax.post(reqParams)
  },
})

function redirectIfWithoutManageAccountPermission() {
  if (!hsGlobal.memberPermissions.manageAccount) {
    App.appRouter.navigate(`/plan/`, { replace: true, trigger: true })
    return true
  }

  return false
}

function redirectIfPartnerSourced() {
  if (hsGlobal.isPartnerSourced) {
    safelyNavigateToPlanPage()
    return true
  }

  return false
}

function getBillingFrequency() {
  // Billing frequency is set to annual if annual payment exists (or is pending)
  // OR if subscription is in trial
  return isPayingAnnually() || App.data.subscription.get('isTrial')
    ? Constants.ANNUAL_CYCLE
    : Constants.MONTHLY_CYCLE
}

function isPayingAnnually() {
  return App.data.hasPendingAnnualPayment || App.data.hasExistingAnnualPayment
}

function getViewData(tab) {
  return {
    model: App.data.subscription,
    features: App.data.features,
    plan: App.data.plan,
    details: App.data.details,
    member: App.data.member,
    card: App.data.card,
    payments: App.data.payments,
    promos: App.data.promos,
    discounts: App.data.discounts,
    plans: App.data.plans,
    totals: App.data.totals,
    countries: App.countries,
    usStates: App.usStates,
    company: App.data.company,
    promoBannerPlanIdForStandardUpgrade:
      App.data.promoBannerPlanIdForStandardUpgrade,
    promoBannerPlanIdForPlusUpgrade: App.data.promoBannerPlanIdForPlusUpgrade,
    invoices: App.data.invoices,
    invoicesTotalCount: App.data.invoicesTotalCount,
    totalCreditsRemaining: App.data.totalCreditsRemaining,
    hasPendingAnnualPayment: App.data.hasPendingAnnualPayment,
    hasExistingAnnualPayment: App.data.hasExistingAnnualPayment,
    nextPaymentDateAnnual: App.data.nextPaymentDateAnnual,
    subscription: App.data.subscription,
    shouldVerifyPassword: appData.verifyPassCancelAccount,
    marketing: App.data.marketing,
    contacts: App.data.contacts,
    messages: App.data.messages,
    aiDrafts: App.data.aiDrafts,
    contactsServed: App.data.contactsServed,
    tab,
    cycle: getBillingFrequency(),
    canManageAccount: hsGlobal.memberPermissions.manageAccount,
    isEligibleForRefund: isEligibleForRefund(),
  }
}

function showPlanPage(section) {
  const tab = 'plan'
  const initialData = _.extend(getViewData(tab), { section: section })
  App.sideNav.show(
    new SideNavView({
      invoices: App.data.invoices,
      isOwner: App.data.member.isOwner,
      plan: App.data.plan,
      selfServiceAddOns: App.data.selfServiceAddOns,
      subscription: App.data.subscription,
      aiDraftsSpendingStatus: App.data.aiDrafts.aiDraftsSpendingStatus || 0,
    })
  )
  triggerSideNav(tab)
  App.appRouter.navigate(`/plan/`, { replace: true, trigger: false })
  App.planOverview.show(new PlanOverviewView(initialData))
}

function showChangePlan() {
  App.data.changePlan = {
    newPlan: new PricingPlanModel(appData.changePlan.newPlan),
    payments: {
      monthly: new PaymentModel(appData.changePlan.payments.monthly),
      annual: new PaymentModel(appData.changePlan.payments.annual),
    },
    change: appData.changePlan.change,
  }

  App.changePlan.show(
    new ChangePlanView({
      model: App.data.card,
      card: App.data.card,
      payments: {
        monthly: new PaymentModel(App.data.changePlan.payments.monthly),
        annual: new PaymentModel(App.data.changePlan.payments.annual),
      },
      contactsServed: App.data.contactsServed,
      isRoadblock: false,
      details: App.data.details,
      oldPlan: App.data.plan,
      newPlan: App.data.changePlan.newPlan,
      subscription: App.data.subscription,
      features: App.data.features,
      change: App.data.changePlan,
      company: App.data.company,
      invoices: App.data.invoices,
      hasPendingAnnualPayment: App.data.hasPendingAnnualPayment,
      hasExistingAnnualPayment: App.data.hasExistingAnnualPayment,
      saveButton: 'Update',
      isEligibleForRefund: isEligibleForRefund(),
    }),
    'Upgrade your Plan'
  )
}

function showRoadblockPage() {
  const initialData = getViewData()
  const data = _.extend({}, initialData, {
    isExpiredDocs: appData.isExpiredDocs,
    isInactive: appData.isInactive,
  })

  App.roadblock.show(new RoadblockView(data))
}

function refreshPlanData() {
  return HS.Utils.Ajax.get(`${hsGlobal.apiPath}members/plan-data`, json => {
    App.data.subscription = new SubscriptionModel(json.subscription)
    App.data.features = json.features
    App.data.plan = new PricingPlanModel(json.plan)
    App.data.details = new DetailsModel(json.details)
    App.data.member = json.member
    App.data.card = new BillingCardModel(json.card)
    App.data.payments = {
      monthly: new PaymentModel(json.payments.monthly),
      annual: new PaymentModel(json.payments.annual),
    }
    App.data.promos = new PromosCollection(json.promos)
    App.data.discounts = new DiscountCollection(json.discounts)
    App.data.plans = new PricingPlanCollection(json.plans)
    App.data.totals = json.totals
    App.data.totalCreditsRemaining = json.totalCreditsRemaining || 0
    App.data.company = json.company
    App.data.marketing = json.marketingData
    App.data.invoices = new InvoiceCollection(json.invoices)
    App.data.invoicesTotalCount = json.invoicesTotalCount
    App.data.selfServiceAddOns = json.selfServiceAddOns
    App.data.legacyPlanMigration = json.legacyPlanMigration
    App.data.hasPendingAnnualPayment = json.hasPendingAnnualPayment || false
    App.data.hasExistingAnnualPayment = json.hasExistingAnnualPayment || false
    App.data.nextPaymentDateAnnual = json.nextPaymentDateAnnual || false
    App.data.promoBannerPlanIdForStandardUpgrade =
      json.promoBannerPlanIdForStandardUpgrade || false
    App.data.promoBannerPlanIdForPlusUpgrade =
      json.promoBannerPlanIdForPlusUpgrade || false
    App.data.currentTest = json.currentTest || false
    App.data.messages = json.messages
    App.data.aiDrafts = json.aiDrafts
    App.data.contactsServed = json.contactsServed
    App.data.shouldShowAnnualDiscountChangeBanner =
      appData.shouldShowAnnualDiscountChangeBanner || false
  })
}

function toggleTrialNav() {
  if (
    App.data.subscription.get('isTrial') &&
    !App.data.subscription.get('showTrialEnding')
  ) {
    $('.trial-note').parent().hide()
  } else if (
    App.data.subscription.get('isTrial') &&
    App.data.subscription.get('showTrialEnding')
  ) {
    $('.trial-note').parent().show()
  }
}

function triggerSideNav(tab) {
  App.trigger('sidenav:change', tab)
}

/**
 * User without manage account permissions will get
 * a minimalist view once the RoadblockView
 * is loaded. So that we don't have to do too much now, we'll fake
 * the expected parameters and move on
 */
function loadNonOwnerRoadblockData() {
  App.data.subscription = new SubscriptionModel()
  App.data.features = {}
  App.data.plan = new PricingPlanModel()
  App.data.details = new DetailsModel()
  App.data.member = appData.member
  App.data.card = new BillingCardModel()
  App.data.payments = {
    monthly: new PaymentModel(),
    annual: new PaymentModel(),
  }
  App.data.promos = new PromosCollection()
  App.data.discounts = new DiscountCollection()
  App.data.plans = new PricingPlanCollection()
  App.data.totals = {}
  App.data.totalCreditsRemaining = 0
  App.countries = new CountryCollection()
  App.vatCountries = new CountryCollection()
  App.usStates = new UsStateCollection()
  App.data.company = {}
  App.paymentMade = false
  App.data.invoices = new InvoiceCollection()
  App.data.invoicesTotalCount = 0
  App.data.selfServiceAddOns = []
  App.data.hasPendingAnnualPayment = false
  App.data.hasExistingAnnualPayment = false
  App.data.nextPaymentDateAnnual = false
  App.data.promoBannerPlanIdForStandardUpgrade = null
  App.data.promoBannerPlanIdForPlusUpgrade = null
  App.data.currentTest = false
  App.data.marketing = {}
  App.data.messages = {}
  App.data.aiDrafts = {}
  App.data.contactsServed = {}
  App.data.shouldShowAnnualDiscountChangeBanner = false
}

function loadFirstRunData() {
  if (isFirstRun) {
    isFirstRun = false
    App.data.subscription = new SubscriptionModel(appData.subscription)
    App.data.features = appData.features
    App.data.plan = new PricingPlanModel(appData.plan)
    App.data.details = new DetailsModel(appData.details)
    App.data.member = appData.member
    App.data.card = new BillingCardModel(appData.card)
    App.data.payments = {
      monthly: new PaymentModel(appData.payments.monthly),
      annual: new PaymentModel(appData.payments.annual),
    }
    App.data.promos = new PromosCollection(appData.promos)
    App.data.discounts = new DiscountCollection(appData.discounts)
    App.data.plans = new PricingPlanCollection(appData.plans)
    App.data.totals = appData.totals
    App.data.totalCreditsRemaining = appData.totalCreditsRemaining || 0
    App.countries = new CountryCollection(appData.countries)
    App.vatCountries = new CountryCollection(appData.vatCountries)
    App.usStates = new UsStateCollection(appData.usStates)
    App.data.company = appData.company
    App.paymentMade = paymentMade
    App.data.invoices = new InvoiceCollection(appData.invoices)
    App.data.invoicesTotalCount = appData.invoicesTotalCount
    App.data.selfServiceAddOns = appData.selfServiceAddOns
    App.data.legacyPlanMigration = appData.legacyPlanMigration
    App.data.hasPendingAnnualPayment = appData.hasPendingAnnualPayment || false
    App.data.hasExistingAnnualPayment =
      appData.hasExistingAnnualPayment || false
    App.data.nextPaymentDateAnnual = appData.nextPaymentDateAnnual || false
    App.data.promoBannerPlanIdForStandardUpgrade =
      appData.promoBannerPlanIdForStandardUpgrade || false
    App.data.promoBannerPlanIdForPlusUpgrade =
      appData.promoBannerPlanIdForPlusUpgrade || false
    App.data.currentTest = appData.currentTest || false
    App.data.contacts = appData.contacts
    App.data.marketing = appData.marketingData
    App.data.messages = appData.messages
    App.data.aiDrafts = appData.aiDrafts
    App.data.contactsServed = appData.contactsServed
    App.data.lastMigrationDate = appData.lastMigrationDate || null
    App.data.shouldShowAnnualDiscountChangeBanner =
      appData.shouldShowAnnualDiscountChangeBanner || false
  }
}

function getChangePlanInfoFromJson(planId, json) {
  // if we are just getting the payment info when adding a card,
  // then we might be on a plan that isn't active. If so, then just
  // use our current plan
  const plan = App.data.plans.findWhere({ id: planId }) || App.data.plan
  return {
    payments: {
      annual: new PaymentModel(json.payments.annual),
      monthly: new PaymentModel(json.payments.monthly),
    },
    change: json.change,
    warnings: json.warnings,
    newPlan: plan,
    nextAction: json.nextAction,
  }
}

function isEligibleForRefund() {
  if (App.data.invoicesTotalCount > 1) {
    return false
  }
  if (App.data.invoicesTotalCount === 0) {
    return true
  }

  const firstPaymentDate = moment(App.data.invoices[0]?.authorizedAt)
  const daysBetween = moment().diff(firstPaymentDate, 'days')

  return daysBetween <= 30
}

function safelyNavigateToPlanPage() {
  if (App.data.plan.get('isContactsBasedBillingType')) {
    // In this case we need to hit the server to re-render loading the
    // hs-app-ui version of this app instead of the current app.
    window.location = '/members/plan'
  } else {
    // In this case, we can stay within the current app, changing the
    // page without talking to the server.
    App.appRouter.navigate(`/plan/`, { replace: true, trigger: true })
  }
}
