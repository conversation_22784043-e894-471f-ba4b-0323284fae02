import Badge from 'hsds/components/badge'
import Heading from 'hsds/components/heading'
import Page from 'hsds/components/page'
import Text from 'hsds/components/text'
import { getToken } from 'hsds/tokens'
import styled from 'styled-components'

const planTypeGradient = {
  standard: `linear-gradient(90deg, var(--color-green-200, #E0FCDE) 0%, var(--color-green-400, #83DB9C) 100%);`,
  plus: `linear-gradient(90deg,var(--color-cobalt-300, #9ecbff) 0%,var(--color-cobalt-400, #66a3ff) 100%)`,
  pro: `linear-gradient(90deg,var(--color-purple-300, #d7baf5) 0%,var(--color-purple-400, #ad82d8) 100%)`,
}

export const StrongNoWrap = styled.strong`
  white-space: nowrap;
`

export const CardUI = styled(Page.Card)`
  position: relative;

  &:before {
    position: absolute;
    top: 0;
    left: 0;
    margin: 4px;
    height: 3px;
    width: calc(100% - 8px);
    content: ' ';
    border-radius: 32px;
    background: ${props => planTypeGradient[props.planType] ?? ''};
  }
`

export const HeaderUI = styled.header`
  margin-bottom: 25px;
`

export const HeaderTitle = styled(Heading)`
  font-size: 18px;
  margin-bottom: 4px;
  display: flex;
  align-items: center;

  svg {
    width: 22px;
    height: 22px;
    margin-right: 6.5px;
  }
`

export const LineItemListUI = styled.ol`
  margin: 0;
  padding: 25px;
  position: relative;
  margin-left: -25px;
  margin-right: -25px;
  li {
    display: flex;
    justify-content: space-between;
    align-items: start;
    margin: 0;
    padding: 0;
    margin-bottom: 25px;

    &:last-child {
      margin-bottom: 0;
    }
  }

  border-radius: 8px;
  background: ${props =>
    props.highlight
      ? `linear-gradient(88.82deg,rgba(249, 242, 255, 0.34) 1.43%,rgba(158, 203, 255, 0.34) 111.92%);`
      : ''};
`

export const ItemWithSwitchUI = styled.div`
  display: flex;
  align-items: center;

  gap: 10px;
`

export const ItemValueUi = styled.div`
  color: ${getToken('color.text.headline')};
  font-size: ${({ isTotal }) => (isTotal ? '24px' : '13px')};
  font-weight: 500;
  display: flex;
  align-items: baseline;

  small {
    color: ${getToken('color.text.disabled')};
    font-size: 13px;
  }
`

export const ItemTitleUi = styled(Text)`
  color: ${getToken('color.text.headline')};
  line-height: 1;
`

export const ItemDescriptionUi = styled(Text)`
  color: ${getToken('color.charcoal.600')};
  margin: 0;
  margin-top: 6px;
`

export const Divider = styled.hr`
  height: 1px;
  background-color: ${getToken('color.charcoal.300')};
  margin: 0%;
`

export const IncludedBadgeTrigger = styled(Badge)`
  cursor: pointer;
  user-select: none;
`

export const IncludedWrapper = styled.div`
  padding: 10px;
`

export const IncludedHeader = styled(Text)`
  margin-bottom: 10px;
  color: ${getToken('color.text.headline')};
`

export const IncludedFooter = styled.footer`
  margin-top: 10px;
  margin-left: 23px;
`

export const IncludedList = styled.ul`
  list-style: none;
  padding: 0;
  margin: 0;

  li {
    margin-bottom: 8px;
    display: flex;
    align-items: center;
    height: 29px;
    gap: 2px;

    &.custom-icon {
      gap: 10px;
      color: ${getToken('color.text.headline')};
    }

    .c-Avatar,
    .evolved-shimmer {
      width: 26px;
      height: 26px;
    }

    .is-iconName-check svg path {
      fill: ${getToken('color.cobalt.600')};
    }

    &:last-child {
      margin-bottom: 0;
    }
  }
`
