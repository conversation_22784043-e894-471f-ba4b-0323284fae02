import LineItemHighlights from '../../data/line-item-highlights'
import { planType } from '../../types'
import Callout from '../FooterCallout/FooterCallout'
import {
  CardUI,
  Divider,
  LineItemListUI,
  HeaderUI,
  HeaderTitle,
  StrongNoWrap,
} from './CBPLineItems.css'
import IncludedBadge from './IncludedBadge'
import Item from './Item'
import {
  getPriceComparisonText,
  createLineItemGroups,
  createPromoLineItems,
  getPromoDiscountDescription,
} from './utils'
import formatMoney from '@common/templates/helpers/formatMoney'
import formatPercent from '@common/templates/helpers/formatPercent'
import Flexy from 'hsds/components/flexy'
import Page from 'hsds/components/page'
import Text from 'hsds/components/text'
import PropTypes from 'prop-types'
import React from 'react'
import { PlanSymbol, PlanType } from 'shared/components/PlanSymbol'

export default function CBPLineItems(props) {
  const {
    currentPlan,
    paymentsPostMigration,
    isPayingAnnually,
    postMigrationNextPaymentDateAnnual,
  } = props

  function formatMigrationDate(format) {
    return moment.utc(currentPlan.endOfLifeDate).format(format)
  }

  const isAnnual = isPayingAnnually

  // if the company is not paying annually (!isPayingAnnually) they we should
  // let them toggle to see the annual savings
  let payment = isPayingAnnually
    ? paymentsPostMigration.monthlyOnAnnual
    : paymentsPostMigration.monthly

  const annualMonthlyDifference =
    paymentsPostMigration.monthly.grandTotalWithTax -
    paymentsPostMigration.monthlyOnAnnual.grandTotalWithTax

  const itemGroups = [
    {
      id: 'highlights',
      highlight: true,
      items: LineItemHighlights,
    },
    {
      id: 'changes',
      highlight: false,
      items: [
        // line items
        ...createLineItemGroups({
          paymentsPostMigration,
          isAnnual,
        }),

        // promos including the CBP Loyalty Discount
        ...createPromoLineItems(payment),

        // sales tax
        payment.tax?.isApplicable && {
          title: 'Sales tax',
          description: `Estimated at ${formatPercent(
            payment.tax.rate * 100,
            2
          )}%`,
          value: payment.tax.amount,
        },
      ].filter(Boolean),
    },
  ]

  return (
    <CardUI planType={currentPlan.typeText}>
      <HeaderUI>
        <HeaderTitle selector="h2" size="h4">
          <PlanSymbol plan={currentPlan.typeText ?? PlanType.Standard} />
          {currentPlan.shortName} Plan
        </HeaderTitle>

        <Flexy align="middle">
          <Flexy.Item>
            <Text share="subtle">
              Your bill is projected to{' '}
              {getPriceComparisonText(
                isAnnual
                  ? props.lastUbpPayment.increaseMonthlyOnAnnual
                  : props.lastUbpPayment.increaseMonthly
              )}
            </Text>
          </Flexy.Item>
          <Flexy.Block />
          <Flexy.Item>
            <IncludedBadge planType={currentPlan.typeText} />
          </Flexy.Item>
        </Flexy>
      </HeaderUI>

      <Page.Content>
        {itemGroups.map(group => {
          return (
            <LineItemListUI key={group.id} highlight={group.highlight}>
              {group.items.map(item => {
                return <Item key={`${group.id}-${item.title}`} {...item} />
              })}
            </LineItemListUI>
          )
        })}
        <Divider />
        <LineItemListUI>
          {!props.isPayingAnnually && (
            <Item
              title="Estimated total"
              description={<></>}
              value={payment.grandTotalWithTax}
              isTotal={true}
            />
          )}

          {props.isPayingAnnually && (
            <Item
              isTotal={true}
              title="Estimated total"
              value={payment.grandTotalWithTax}
            />
          )}
        </LineItemListUI>

        {!props.isPayingAnnually && annualMonthlyDifference > 0 && (
          <Callout title="Want to keep costs down?">
            Switch to annual billing on or after{' '}
            {formatMigrationDate('MMMM Do')} and your estimated price will drop
            to{' '}
            <StrongNoWrap>
              {formatMoney(
                paymentsPostMigration.monthlyOnAnnual.grandTotalWithTax
              )}
              /month
            </StrongNoWrap>
            . That’s a saving of{' '}
            <StrongNoWrap>
              {formatMoney(annualMonthlyDifference * 12)}/year
            </StrongNoWrap>
            .
          </Callout>
        )}

        {props.isPayingAnnually && paymentsPostMigration.annual.grandTotal > 0 && (
          <Callout title="How does this change affect annual billing?">
            <p>
              Starting in {formatMigrationDate('MMMM')}, we’ll deduct{' '}
              {formatMoney(payment.grandTotalWithTax)} each month from your
              existing account credits.
            </p>
            <p>
              Based on your current usage, we estimate that your first annual payment of{' '}
              <strong>{formatMoney(paymentsPostMigration.annual.grandTotalWithTax)}</strong>{' '}
              under the new pricing will be made on{' '}
              <strong>
                {moment
                  .utc(postMigrationNextPaymentDateAnnual)
                  .format('MMMM D, YYYY')}
              </strong>
              .
            </p>
          </Callout>
        )}
      </Page.Content>
    </CardUI>
  )
}

CBPLineItems.propTypes = {
  currentPlan: planType,
  isPayingAnnually: PropTypes.bool,
}
