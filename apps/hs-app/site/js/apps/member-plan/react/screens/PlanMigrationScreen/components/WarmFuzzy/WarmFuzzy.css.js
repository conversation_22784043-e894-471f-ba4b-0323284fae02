import Page from 'hsds/components/page'
import { getColor } from 'hsds/utils/color'
import styled from 'styled-components'

export const WarmFuzzyUI = styled(Page.Card)`
  color: ${getColor('charcoal.700')};
  .c-List {
    margin: 0;

    li {
      padding: 2px 0;

      .c-Icon {
        color: ${getColor('green.500')};
        display: inline-block;
        margin-left: -3px;
        vertical-align: middle;
      }

      .c-Text {
        margin-left: 6px;
      }
    }
  }

  .features {
    align-items: normal;
  }
`
