import React, { PureComponent } from 'react'
import PropTypes from 'prop-types'
import formatMoney from '../../../../../common/templates/helpers/formatMoney'
import {
  FigureUI,
  CHART_CLASS_LOW,
  CHART_CLASS_MED,
  CHART_CLASS_HIGH,
  CHART_CLASS_DISABLED,
} from './CreditRemainingCircleChart.css.js'

export class CreditRemainingCircleChart extends PureComponent {
  static propTypes = {
    totalCreditsRemaining: PropTypes.number.isRequired,
    totalForNextAnnualPayment: PropTypes.number.isRequired,
  }

  static defaultProps = {
    totalCreditsRemaining: 100,
    totalForNextAnnualPayment: 100,
  }

  getFormattedCreditsRemainingTotal() {
    // above $10k (up to realistically ~$200k as no one would prepay more)
    if (this.props.totalCreditsRemaining >= 10000) {
      // not using formatMoney as it rounds up in some cases
      return '$' + Math.floor(this.props.totalCreditsRemaining / 1000) + 'k'
    }

    // from $1k to 9.9k
    if (this.props.totalCreditsRemaining >= 1000) {
      // not using formatMoney as it rounds up in some cases
      return '$' + Math.floor(this.props.totalCreditsRemaining / 100) / 10 + 'k'
    }

    // from $0.01 to $999.99
    return formatMoney(this.props.totalCreditsRemaining, 2)
  }

  getCreditsRemainingPercentage() {
    const { totalCreditsRemaining, totalForNextAnnualPayment } = this.props

    if (totalForNextAnnualPayment == 0) {
      return 0
    }

    const percentage = Math.floor(
      (totalCreditsRemaining / totalForNextAnnualPayment) * 100
    )
    return percentage > 100 ? 100 : percentage
  }

  getChartClassForPercentage(creditsPercent) {
    // a $0 annual payment gets a separate "greyed out" class
    if (this.props.totalForNextAnnualPayment === 0) {
      return CHART_CLASS_DISABLED
    }

    // 8-12 months worth of credits remaining
    if (creditsPercent > 66) {
      return CHART_CLASS_HIGH
    }

    // 4-8 months worth of credits remaining
    if (creditsPercent > 33) {
      return CHART_CLASS_MED
    }

    // 0-4 months worth of credits remaining
    return CHART_CLASS_LOW
  }

  render() {
    const creditsRemainingPercentage = this.getCreditsRemainingPercentage()
    const creditsRemainingTotal = this.getFormattedCreditsRemainingTotal()
    const figureClassName = this.getChartClassForPercentage(
      creditsRemainingPercentage
    )
    const foregroundCircleClassName = `circle-foreground percent${creditsRemainingPercentage}`

    return (
      <FigureUI
        data-cy="CreditRemainingCircle"
        className={figureClassName}
        percentage={creditsRemainingPercentage}
      >
        <svg
          role="img"
          xmlns="http://www.w3.org/2000/svg"
          xmlnsXlink="http://www.w3.org/1999/xlink"
        >
          <title>Credits Remaining</title>
          <desc>Credits Remaining</desc>
          <circle
            className="circle-background"
            strokeWidth="2"
            fill="none"
            r="38"
            cx="40"
            cy="40"
          />
          <circle className="circle-inner" r="34" cx="40" cy="40" />
          <circle
            data-cy="CreditRemainingCircle.Percent"
            className={foregroundCircleClassName}
            strokeWidth="2"
            fill="none"
            r="38"
            cx="40"
            cy="40"
          />
        </svg>
        <figcaption>{creditsRemainingTotal}</figcaption>
      </FigureUI>
    )
  }
}

export default CreditRemainingCircleChart
