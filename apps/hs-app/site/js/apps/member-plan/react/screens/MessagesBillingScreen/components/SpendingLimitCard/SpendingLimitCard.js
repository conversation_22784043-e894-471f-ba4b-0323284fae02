import { MessagesBillingContext } from '../../MessagesBillingScreen'
import { RADIO_OPTIONS } from '../../constants'
import FooterAction from '../FooterAction'
import {
  CardUI,
  PageCardUI,
  RadioUI,
  ChoiceGroupUI,
  SpendingLimitUI,
  SpendingLimitText,
  ReviewButtonUI,
} from './SpendingLimitCard.css'
import useSpendingLimit from './hooks'
import Slider from '@common/react/components/Slider'
import Page from 'hsds/components/page'
import Text from 'hsds/components/text'
import moment from 'moment'
import React, { useContext, Fragment } from 'react'

function SpendingLimitCard() {
  const context = useContext(MessagesBillingContext)
  const {
    state: { viewCount, active, currentTier, nextPaymentDate, includedViews },
  } = context
  const {
    steps,
    isEdited,
    isAuto,
    tier,
    isViewsOverride,
    nextTier,
    defaultValue,
    usageIsAboveLimitTiers,
    getTierPrice,
    onRadioChange,
    onCancel,
    onTierChange,
    onSave,
  } = useSpendingLimit(context)

  const tierMaxViews = tier.maxViews

  const maxTiersMaxValue = steps[steps.length - 1]
  const isMaxTier = tierMaxViews === maxTiersMaxValue

  const getSliderLowerBound = () => {
    if (usageIsAboveLimitTiers) return defaultValue
    return isViewsOverride ? includedViews : viewCount
  }
  const sliderLowerBound = getSliderLowerBound()

  const maxSpend =
    isViewsOverride && sliderLowerBound === tierMaxViews
      ? 0
      : getTierPrice(tierMaxViews)

  const autoUpgradeLabel = (
    <div>
      Automatically upgrade to the next tier (
      {nextTier.maxViews.toLocaleString()} unique viewers for $
      {nextTier.cumulativePrice.toLocaleString()}) when the limit is reached
    </div>
  )

  const openBeacon = event => {
    event.preventDefault()
    if (typeof window.Beacon === 'function') {
      const { Beacon } = window
      Beacon(
        'prefill',
        {
          subject: `Help me set a higher spending limit for Messages`,
          text: `Hi there! Can you help me set a monthly spending limit higher than 150,000 unique viewers?`,
        },
        { persist: false }
      )
      Beacon('navigate', '/ask/message/')
      Beacon('open')
    }
  }

  const pauseStatusText = active ? (
    <p>
      Messages will be paused when you reach
      <strong> {tierMaxViews.toLocaleString()} </strong>unique viewers
    </p>
  ) : (
    <p>
      You reached <strong>{currentTier.maxViews.toLocaleString()}</strong>{' '}
      unique viewers and <strong>your messages are now paused.</strong>{' '}
    </p>
  )

  const maxTierText = isMaxTier ? (
    <p>
      To reach{' '}
      {active ? 'over 150,000 unique viewers,' : 'more viewers this month,'}{' '}
      switch to auto-upgrade or{' '}
      <a
        onClick={openBeacon}
        data-testid="MessagesBilling.ContactUs"
        data-cy="MessagesBilling.ContactUs"
        href="/"
      >
        contact us.
      </a>{' '}
    </p>
  ) : (
    ''
  )

  const aboveLimitTiersCard = () => (
    <CardUI>
      <Text>
        <strong>Heads up!</strong> Spending limits can’t be set after
        <strong> 150,000 unique viewers</strong> have been reached. To limit
        spending before usage resets on{' '}
        <strong> {moment(nextPaymentDate).format('MMMM D')}</strong>, consider
        pausing high-volume Messages.
      </Text>

      <ReviewButtonUI size="lg" color="blue" href="/messages">
        Review Messages
      </ReviewButtonUI>
    </CardUI>
  )

  return (
    <>
      <PageCardUI>
        <Page.Header
          data-cy="SpendingLimit.Heading"
          render={({ Title }) => (
            <Title headingLevel="h2">Spending Limit</Title>
          )}
        />
        <ChoiceGroupUI
          data-testid="SpendingLimit.Radio"
          selectionLimits="radio"
          value={
            isAuto ? RADIO_OPTIONS.AUTOMATIC : RADIO_OPTIONS.SPENDING_LIMIT
          }
          onChange={onRadioChange}
        >
          <RadioUI
            data-testid="Automatic.upgrade"
            data-cy="Automatic.upgrade"
            label={autoUpgradeLabel}
            value={RADIO_OPTIONS.AUTOMATIC}
          />
          <RadioUI
            data-testid="Fixed.limit"
            data-cy="Fixed.limit"
            label="Set a monthly spending limit"
            disabled={usageIsAboveLimitTiers}
            value={RADIO_OPTIONS.SPENDING_LIMIT}
          />
        </ChoiceGroupUI>

        {!isAuto && (
          <SpendingLimitUI>
            <Slider
              steps={steps}
              defaultValue={defaultValue}
              lowerBound={sliderLowerBound}
              onChange={onTierChange}
              value={tierMaxViews}
            />
            {!usageIsAboveLimitTiers && (
              <SpendingLimitText
                data-testid="SpendingLimit.Details"
                data-cy="SpendingLimit.Details"
              >
                <p>
                  Maximum spend:
                  <strong> ${maxSpend}/month </strong>
                </p>
                {pauseStatusText}
                {maxTierText}
                {!isMaxTier && !active && (
                  <p>Increase your limit to reach more viewers this month.</p>
                )}
              </SpendingLimitText>
            )}
          </SpendingLimitUI>
        )}
        {usageIsAboveLimitTiers && aboveLimitTiersCard()}
      </PageCardUI>
      <FooterAction
        isEdited={isEdited}
        tierValue={tierMaxViews}
        price={getTierPrice(tierMaxViews)}
        onCancel={onCancel}
        isAuto={isAuto}
        onSave={onSave}
      />
    </>
  )
}

export default SpendingLimitCard
