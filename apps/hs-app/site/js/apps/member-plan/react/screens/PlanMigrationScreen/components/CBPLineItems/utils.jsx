import formatMoney from '@common/templates/helpers/formatMoney'
import formatNumber from '@common/templates/helpers/formatNumber'
import formatPercent from '@common/templates/helpers/formatPercent'
import pluralize from '@common/templates/helpers/pluralize'
import Constants from '@member-plan/utils/defs'
import Link from 'hsds/components/link'
import React from 'react'

export function getPromoDiscountDescription(promo) {
  if (!promo) {
    return null
  }

  const amount = Math.abs(promo.amount)

  switch (promo.discType) {
    case Constants.DISCOUNT_TYPE_FLAT_AMOUNT_DOLLAR_OFF_TOTAL:
      return `${formatMoney(amount)} off the total`
    case Constants.DISCOUNT_TYPE_FLAT_AMOUNT_PERCENT_OFF_TOTAL:
      return `${formatPercent(amount)}% off the total`
    case Constants.DISCOUNT_TYPE_FLAT_AMOUNT_DOLLAR_OFF_PER_USER:
      return `${formatMoney(amount)} off per User`
    case Constants.DISCOUNT_TYPE_FLAT_AMOUNT_PERCENT_OFF_PER_USER:
      return `${formatPercent(amount)}% off per User`
  }
}

export function getPriceComparisonText(change) {
  if (change > 0) {
    return (
      <>
        increase by <strong>{formatMoney(change)}/mo</strong>
      </>
    )
  } else if (change === 0) {
    return 'stay the same'
  } else if (change < 0) {
    return (
      <>
        decrease by <strong>{formatMoney(Math.abs(change))}/mo</strong>
      </>
    )
  }
}

export function findActiveLineItem(lineItem, paymentsPostMigration, isAnnual) {
  const name = lineItem.name.split('(')[0]
  const month = paymentsPostMigration.monthly.lineItems.find(li => {
    return li.name.startsWith(name)
  })
  const annual = paymentsPostMigration.monthlyOnAnnual.lineItems.find(li => {
    return li.name.startsWith(name)
  })

  return isAnnual ? annual : month
}

export function createLineItemGroups({ paymentsPostMigration, isAnnual }) {
  return paymentsPostMigration.monthly.lineItems.map(item => {
    const active = findActiveLineItem(item, paymentsPostMigration, isAnnual)

    switch (item.type) {
      // CONTACTS
      case Constants.LINE_ITEM_CONTACTS_SERVED: {
        return {
          title: `${formatNumber(item.quantity)} Contacts`,
          description: (
            <>
              Based on your last three months of usage.{' '}
              <Link href="#contacts-helped">View history</Link>
            </>
          ),
          value: active.total,
        }
      }

      // INBOXES
      case Constants.LINE_ITEM_ADDITIONAL_MAILBOX: {
        if (item.quantity === 0) {
          return false
        }

        const per = formatMoney(item.price)

        return {
          title: `Additional Inboxes`,
          description: `${item.quantity} Inboxes (${per}/Inbox/mo)`,
          value: active.total,
        }
      }

      // DOCS
      case Constants.LINE_ITEM_DOCS: {
        if (item.quantity === 0) {
          return false
        }

        const displayQuantity = item.quantity
        const pluralizedQuantity = pluralize(displayQuantity, 'Sites')
        const per = formatMoney(item.price)

        return {
          title: `Additional Docs Sites`,
          description: `${displayQuantity} ${pluralizedQuantity} (${per}/Site/mo)`,
          value: active.total,
        }
      }

      case Constants.LINE_ITEM_ADDONS_PER_MONTH: {
        const formattedName = item.name.split('(')[0].trim()
        const addOnsPreviouslyBilledPerUser = [
          'Collaboration (Custom Fields, Teams)',
          'Premium Integrations',
        ]
        const isPreviouslyBilledPerUser =
          addOnsPreviouslyBilledPerUser.includes(item.name)

        const description = isPreviouslyBilledPerUser
          ? '$5/user on legacy plans, $50 flat fee on new contact plans'
          : null

        return {
          title: `${formattedName} Add-on`,
          description,
          value: active.total,
        }
      }

      default:
        return false
    }
  })
}

export function createPromoLineItems({ promos }) {
  if (!Array.isArray(promos)) {
    return []
  }
  return promos.map(promo => {
    if (promo.isContactBasedLoyaltyDiscount) {
      return {
        title: promo.name,
        value: promo.calculatedAmount,
      }
    } else {
      // For regular promos
      return {
        title: promo.name,
        description: getPromoDiscountDescription(promo),
        value: promo.calculatedAmount,
      }
    }
  }).filter(Boolean)
}
