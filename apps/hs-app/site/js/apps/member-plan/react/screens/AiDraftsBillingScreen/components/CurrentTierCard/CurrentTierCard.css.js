import Badge from 'hsds/components/badge'
import Icon from 'hsds/components/icon'
import Page from 'hsds/components/page'
import { PopoverUI } from 'hsds/components/popover/Popover.styles'
import Text from 'hsds/components/text'
import { getColor } from 'hsds/utils/color'
import styled from 'styled-components'

export const TierText = styled('h3')`
  font-size: 16px;
  line-height: 20px;
  font-weight: 500;
  color: ${getColor('charcoal.1000')};
  display: flex;
  align-items: center;
`

export const TierSubtext = styled('p')`
  margin-top: 4px;
  font-size: 13px;
  line-height: 150%;
  font-weight: 400;
  color: ${getColor('charcoal.700')};
`
export const PriceUI = styled('span')`
  position: relative;
  -webkit-font-smoothing: antialiased;
  font-weight: 400;
  font-size: 38px;
  letter-spacing: -1.25px;
  line-height: 46px;
  margin-left: 15px;
  color: ${getColor('charcoal.1000')};
  &:before {
    content: '$';
    position: absolute;
    font-size: 20px;
    font-weight: 500;
    line-height: 36px;
    left: -13px;
  }
`
export const PageHeaderUI = styled(Page.Header)`
  &.is-withBottomMargin {
    margin-bottom: 21px;
  }
  &.is-withBorder {
    padding-bottom: 9px;
  }
  .c-PageHeader__subtitle {
    margin-top: 4px;
  }
`

export const BadgeUI = styled(Badge)`
  margin-left: 5px;
`

export const CardUI = styled(Page.Card)`
  position: relative;
  p {
    margin-bottom: 0;
  }
`
export const InfoIconUI = styled(Icon)`
  color: ${getColor('charcoal.600')};
  margin-bottom: -2px;
  margin-left: 4px;
`

export const PopoverContainerUI = styled(PopoverUI)`
  hr {
    margin: 7px -13px 9px;
  }
  padding-top: 10px;
  max-width: 241px;
  height: 128px;
`
export const PopoverHeadingUI = styled(Text)`
  && {
    font-size: 17px;
  }
  font-weight: 400;
  color: ${getColor('charcoal.600')};
`
export const PopoverBodyUI = styled(Text)`
  font-weight: 400;
  color: ${getColor('charcoal.900')};
`
