import { getColor } from 'hsds/utils/color'
import styled from 'styled-components'

const CHART_CLASS_LOW = 'js-c-chart--low'
const CHART_CLASS_MED = 'js-c-chart--med'
const CHART_CLASS_HIGH = 'js-c-chart--high'
const CHART_CLASS_DISABLED = 'js-c-chart--disabled'

export { CHART_CLASS_LOW, CHART_CLASS_MED, CHART_CLASS_HIGH, CHART_CLASS_DISABLED }

export const FigureUI = styled('figure')`
  box-sizing: border-box;
  height: 80px;
  margin: 0;
  position: relative;
  width: 80px;

  * {
    box-sizing: border-box;
  }

  &.animate svg .circle-foreground {
    animation: offset 1.5s ease-in-out forwards;
    animation-delay: 1s;
  }

  svg {
    height: 100%;
    width: 100%;

    .circle-background,
    .circle-foreground {
      stroke: ${getColor('charcoal.200')};
    }

    .circle-inner {
      r: 34px;
      cx: 50%;
      cy: 50%;
      stroke: none;
      stroke-width: none;
    }

    .circle-foreground {
      stroke-linecap: round;
      transform-origin: 50% 50%;
      transform: rotate(-90deg);
    }
  }

  figcaption {
    display: inline-block;
    font-size: 15px;
    font-weight: 500;
    left: 50%;
    line-height: 1;
    overflow: hidden;
    position: absolute;
    text-align: center;
    top: 50%;
    transform: translate(-50%, -50%);
    width: 100%;
  }

  @keyframes offset {
    100% {
      stroke-dashoffset: 0;
    }
  }

  &.${CHART_CLASS_HIGH} {
    svg {
      .circle-foreground {
        stroke: ${getColor('cobalt.600')};
      }
      .circle-inner {
        fill: ${getColor('cobalt.200')};
      }
    }
    figcaption {
      color: ${getColor('cobalt.600')};
    }
  }

  &.${CHART_CLASS_MED} {
    svg {
      .circle-foreground {
        stroke: ${getColor('yellow.500')};
      }
      .circle-inner {
        fill: ${getColor('yellow.200')};
      }
    }
    figcaption {
      color: ${getColor('yellow.700')};
    }
  }

  &.${CHART_CLASS_LOW} {
    svg {
      .circle-foreground {
        stroke: ${getColor('red.500')};
      }
      .circle-inner {
        fill: ${getColor('red.200')};
      }
    }
    figcaption {
      color: ${getColor('red.500')};
    }
  }

  &.${CHART_CLASS_DISABLED} {
    svg {
      .circle-foreground {
        stroke: ${getColor('charcoal.700')};
      }
      .circle-inner {
        fill: ${getColor('charcoal.200')};
      }
    }
    figcaption {
      color: ${getColor('charcoal.700')};
    }
  }

  .circle-foreground {
    ${({ percentage }) => {
      return `
        stroke-dasharray: ${percentage * (240 / 100)}px 240px;
      `
    }};

    &.percent0 {
      display: none;
    }

    &.percent90 {
      stroke-dasharray: 215px 240px;
    }
    &.percent91 {
      stroke-dasharray: 218px 240px;
    }
    &.percent92 {
      stroke-dasharray: 220px 240px;
    }
    &.percent93 {
      stroke-dasharray: 222px 240px;
    }
    &.percent94 {
      stroke-dasharray: 224px 240px;
    }
    &.percent95 {
      stroke-dasharray: 226px 240px;
    }
    &.percent96 {
      stroke-dasharray: 228px 240px;
    }
    &.percent97 {
      stroke-dasharray: 230px 240px;
    }
    &.percent98 {
      stroke-dasharray: 232px 240px;
    }
    &.percent99 {
      stroke-dasharray: 234px 240px;
    }
    &.percent100 {
      stroke-dasharray: 240px 240px;
    }
  }
`
