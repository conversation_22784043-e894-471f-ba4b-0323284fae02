import Flexy from 'hsds/components/flexy'
import Heading from 'hsds/components/heading'
import Page from 'hsds/components/page'
import SimpleModal from 'hsds/components/simple-modal'
import Spinner from 'hsds/components/spinner'
import Text from 'hsds/components/text'
import { getToken } from 'hsds/tokens'
import styled from 'styled-components'

export const ActionsUI = styled(Flexy)`
  .c-Button {
    width: 153px;
  }
`

export const CardUI = styled(Page.Card)``

export const TitleUI = styled(Heading)`
  text-align: center;
  font-size: 18px;
  margin-bottom: 17px;
`

export const DescriptionUI = styled(Text)`
  display: block;
  line-height: 24px;
  color: ${getToken('color.text.subtle')};
  text-align: center;
`

export const VideoWrapperUI = styled('div')`
  width: 100%;
  margin: 50px 0;
  aspect-ratio: 16 / 9;
  border-radius: 8px;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  overflow: hidden;
  background-color: #ede8e6;
  img,
  button.play,
  .wistia_embed {
    z-index: 0;
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    object-fit: cover;
    z-index: 0;
  }

  button.play {
    display: block;
    cursor: pointer;
    z-index: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    border: none;
    background-color: transparent;
  }
`

export const ModalVideoUI = styled(SimpleModal)`
  --hsds-token-simpleModal-color-background-body: transparent;
  --hsds-token-shadow-700: none;

  position: fixed;
`

export const ModalLoadingUI = styled(Spinner)`
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #fff;
`

export const ModalCloseButtonUI = styled('button')`
  position: fixed;
  top: 0;
  right: 0;
  border: none;
  background-color: transparent;
  color: #fff;
  font-size: 24px;
  cursor: pointer;
  padding: 10px;
`

export const ModalVideoWrapperUI = styled('div')`
  width: 80vw;
  height: 80vh;
  display: flex;
  align-items: center;
  justify-content: center;
`

export const ModalVideoContentUI = styled('div')`
  aspect-ratio: 16 / 9;
  width: 100%;
  background-color: rgba(0, 0, 0, 0.9);
  max-width: 1200px;
  font-size: 0;
  border-radius: 8px;
  overflow: hidden;
  position: relative;
  box-shadow: var(--hsds-token-shadow-700);

  iframe {
    position: relative;
    z-index: 1;
  }
`

export const VideoEmbedIframe = styled('iframe')`
  width: 100%;
  height: 100%;
  border: none;
`
