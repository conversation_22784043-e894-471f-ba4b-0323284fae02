import AddOnsList from './components/AddOnsList'
import { getCurrentTheme } from '@common/utils/theme'
import { Provider as HSDSProvider } from 'hsds/components/hsds'
import Page from 'hsds/components/page'
import PropTypes from 'prop-types'
import React, { PureComponent } from 'react'
import styled from 'styled-components'
import { createGlobalStyle } from 'styled-components'

const GlobalStyle = createGlobalStyle`
  #planOverview.s-Settings-v2.s-Settings-v2{
    padding:0;
  }
`

const PageUI = styled(Page)`
  &&& {
    --hsds-page-max-width: 700px;
    --hsds-page-min-width: 480px;
    /* --hsds-page-gutter: 0; */
  }

  .c-Page {
    margin: 0;
    max-width: 100%;
    min-width: 0;
  }
`

export class AddOnsListScreen extends PureComponent {
  static propTypes = {
    selfServiceAddOns: PropTypes.array.isRequired,
    onAddOnChange: PropTypes.func.isRequired,
    plan: PropTypes.object.isRequired,
  }

  render() {
    const themeName = getCurrentTheme()

    return (
      <>
        <GlobalStyle />
        <HSDSProvider scope="hsds-react" themeName={themeName}>
          <PageUI className="c-Page--add-ons">
            <AddOnsList {...this.props} />
          </PageUI>
        </HSDSProvider>
      </>
    )
  }
}

export default AddOnsListScreen
