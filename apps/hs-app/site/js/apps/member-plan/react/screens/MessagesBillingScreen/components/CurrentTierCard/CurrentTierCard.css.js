import Page from 'hsds/components/page'
import { getColor } from 'hsds/utils/color'
import styled from 'styled-components'

export const TierText = styled('h3')`
  font-size: 16px;
  line-height: 20px;
  font-weight: 500;
  color: ${getColor('charcoal.1000')};
`

export const TierSubtext = styled('p')`
  margin-top: 6px;
  font-size: 13px;
  line-height: 150%;
  font-weight: 400;
  color: ${getColor('charcoal.700')};
`
export const PriceUI = styled('span')`
  position: relative;
  -webkit-font-smoothing: antialiased;
  font-weight: 400;
  font-size: 38px;
  letter-spacing: -1.25px;
  line-height: 46px;
  margin-left: 15px;
  color: ${getColor('charcoal.1000')};
  &:before {
    content: '$';
    position: absolute;
    font-size: 20px;
    font-weight: 500;
    line-height: 36px;
    left: -13px;
  }
`

export const CardUI = styled(Page.Card)`
  position: relative;
  p {
    margin-bottom: 0;
  }
`
