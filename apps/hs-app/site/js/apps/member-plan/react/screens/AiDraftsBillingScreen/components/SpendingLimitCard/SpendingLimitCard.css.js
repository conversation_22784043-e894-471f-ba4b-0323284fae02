import Page from 'hsds/components/page'
import Text from 'hsds/components/text'
import { getColor } from 'hsds/utils/color'
import styled from 'styled-components'

export const PageCardUI = styled(Page.Card)`
  p {
    margin-bottom: 0;
  }
`

export const PageHeaderUI = styled(<PERSON>.Header)`
  &.is-withBottomMargin {
    margin-bottom: 4px;
  }
  .c-PageHeader__subtitle {
    margin: 3px 0 0;
  }
`
export const SpendingLimitUI = styled('div')`
  transition: all 300ms ease-in-out 0ms;
`
export const SpendingLimitText = styled('div')`
  margin-top: 16px;
  p {
    line-height: 22px;
    color: ${getColor('charcoal.700')};
    strong {
      color: ${getColor('charcoal.1000')};
    }
  }
`

export const SpendingLimitTitleUI = styled(Text)`
  margin: 18px 0px 20px;
`
