import { GlobalStyle, PageUI } from './AiDraftsBillingScreen.css'
import { BlankSlate } from './components/BlankSlate/BlankSlate'
import CurrentTierCard from './components/CurrentTierCard'
import SpendingLimitCard from './components/SpendingLimitCard/SpendingLimitCard'
import { StatusBanner } from './components/StatusBanner/StatusBanner'
import { useAiDraftsBillingScreenState } from './hooks/useAiDraftsBillingScreenState'
import { getCurrentTheme } from '@common/utils/theme'
import { AI_DRAFTS_SPENDING_NOT_AVAILABLE } from '@member-plan/utils/defs'
import { Provider as HSDSProvider } from 'hsds/components/hsds'
import PropTypes from 'prop-types'
import React from 'react'

export const AiDraftsBillingContext = React.createContext()
export function AiDraftsBillingScreen({
  aiDrafts,
  setSpendingLimit,
  enableAIDrafts,
  refreshAiDrafts,
}) {
  const { aiDraftsState, updateAiDraftsState } = useAiDraftsBillingScreenState(
    aiDrafts,
    refreshAiDrafts
  )

  const themeName = getCurrentTheme()

  if (aiDrafts.aiDraftsSpendingStatus === AI_DRAFTS_SPENDING_NOT_AVAILABLE) {
    return (
      <HSDSProvider scope="hsds-react">
        <BlankSlate />
      </HSDSProvider>
    )
  }

  return (
    <>
      <GlobalStyle />
      <HSDSProvider scope="hsds-react" themeName={themeName}>
        <AiDraftsBillingContext.Provider
          value={{
            state: aiDraftsState,
            actions: {
              enableAIDrafts,
              setSpendingLimit,
              updateAiDraftsState,
            },
          }}
        >
          <PageUI data-testid={'AiDraftsBillingScreen'}>
            <StatusBanner />
            <CurrentTierCard />
            <SpendingLimitCard />
          </PageUI>
        </AiDraftsBillingContext.Provider>
      </HSDSProvider>
    </>
  )
}

export default AiDraftsBillingScreen

const tierShape = PropTypes.shape({
  id: PropTypes.number.isRequired,
  bundle: PropTypes.number.isRequired,
  cumulativePrice: PropTypes.number.isRequired,
  maxAllowedAiDraftConvos: PropTypes.number.isRequired,
  minAllowedAiDraftConvos: PropTypes.number.isRequired,
  tier: PropTypes.number.isRequired,
})

AiDraftsBillingScreen.propTypes = {
  aiDrafts: PropTypes.shape({
    isSpendingLimitReached: PropTypes.bool.isRequired,
    aiDraftsSpendingStatus: PropTypes.number.isRequired,
    aiDraftConvosCount: PropTypes.number.isRequired,
    currentTier: tierShape.isRequired,
    freeTrialCompletionDate: PropTypes.string,
    hasFreeAiDraftConvosLeft: PropTypes.bool.isRequired,
    includedFreeTrialAiDraftConvos: PropTypes.number.isRequired,
    isUnlimited: PropTypes.bool.isRequired,
    maxAllowedAiDraftConvos: PropTypes.number.isRequired,
    nextPaymentDate: PropTypes.string.isRequired,
    nextTier: tierShape.isRequired,
    spendingLimit: PropTypes.object.isRequired,
    tiers: PropTypes.arrayOf(tierShape).isRequired,
    usedFreeTrialAiDraftConvos: PropTypes.number.isRequired,
  }),
  setSpendingLimit: PropTypes.func.isRequired,
  enableAIDrafts: PropTypes.func.isRequired,
  refreshAiDrafts: PropTypes.func.isRequired,
}
