import Button from 'hsds/components/button'
import Page from 'hsds/components/page'
import { getToken } from 'hsds/tokens'
import { getColor } from 'hsds/utils/color'
import styled from 'styled-components'

export const BlankSlatePageUI = styled(Page)`
  &&&& {
    --hsds-page-max-width: 900px;

    @media (min-width: 1600px) {
      --hsds-page-max-width: 1000px;
    }
  }

  .c-Page {
    margin: 0;
    max-width: 100%;
    min-width: 0;
  }

  .c-PageCard {
    --hsds-page-card-padding-top: 0;
    --hsds-page-card-padding-side: 90px;
    overflow: hidden;
  }
`

export const BlankSlateHeaderUI = styled.div`
  width: calc(100% + 2 * var(--hsds-page-card-padding-side) - 8px);
  height: 80px;
  margin-left: calc(var(--hsds-page-card-padding-side) * -1 + 4px);
  margin-right: calc(var(--hsds-page-card-padding-side) * -1 + 4px);
  margin-top: 6px;

  display: flex;
  align-items: center;

  gap: 24px;

  padding-left: 35px;

  border-radius: 4px;
  background: ${getColor('purple.100')};

  color: ${getColor('purple.900')};
  font-size: 16px;
  font-style: normal;
  font-weight: 500;
  line-height: 18px;
`

export const BlankSlateContentUI = styled.div`
  display: flex;
  justify-content: space-between;
  padding-top: 65px;
`

export const BlankSlateContentTextUI = styled.div`
  display: flex;
  flex-direction: column;
  width: 360px;

  color: ${getToken('color.text.default')};
  font-size: 14px;
  font-weight: 400;
  line-height: 22px;

  strong {
    font-weight: 700;
  }
`

export const BlankSlateContentMainTextUI = styled.div`
  margin-top: 8px;
  margin-bottom: 30px;
`

export const ButtonUI = styled(Button)`
  max-width: 135px;
`

export const ButtonsUI = styled.div`
  display: flex;
  align-items: center;
  gap: 15px;
`

export const ImageUI = styled.img`
  height: 242px;
  margin-right: calc(var(--hsds-page-card-padding-side) * -1);
`
