import Link from 'hsds/components/link'
import Page from 'hsds/components/page'
import { getColor } from 'hsds/utils/color'
import styled from 'styled-components'
import { createGlobalStyle } from 'styled-components'

export const HeadingUI = styled('h1')`
  font-weight: 500;
  font-size: 22px;
  line-height: 28px;
  color: ${getColor('charcoal.1000')};
  margin: 5px 0px 22px;
`

export const GlobalStyle = createGlobalStyle`
  #planOverview.s-Settings-v2.s-Settings-v2{
    padding:0;
  }
`

export const MessagesBillingUI = styled(Page)`
  &&& {
    --hsds-page-max-width: 700px;
  }

  .c-Page {
    margin: 0;
    max-width: 100%;
    min-width: 0;
  }

  /* position: relative;
  max-width: 700px;
  width: 100%;
  margin: auto; */

  .c-PageActions {
    padding: 0px;
    margin-top: 0;
    margin-bottom: 0;
    .c-PageActions__primary {
      padding: 0px;
      button {
        height: 40px;
        min-width: 120px;
      }
    }
    .c-PageActions__serious {
      padding: 0px;
    }
  }
`

export const BackUI = styled(Link)`
  font-style: normal;
  font-weight: normal;
  font-size: 14px;
  line-height: 19px;
  color: ${getColor('charcoal.700')};
  &:hover {
    color: ${getColor('charcoal.900')};
    &:after {
      opacity: 100%;
    }
  }
  &:after {
    background-image: url('${hsGlobal.imagePath}plans/Path.svg');
    background-repeat: no-repeat;
    background-size: contain;
    content: '';
    position: absolute;
    width: 10px;
    height: 10px;
    left: -18px;
    padding-right: 10px;
    top: 5px;
    cursor: pointer;
    opacity: 80%;
    @media (max-width: 1020px) {
      left: 0px;
    }
  }
  @media (max-width: 1020px) {
    padding-left: 18px;
  }
`
