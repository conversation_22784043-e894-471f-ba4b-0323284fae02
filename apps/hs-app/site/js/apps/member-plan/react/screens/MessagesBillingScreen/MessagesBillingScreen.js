import {
  MessagesBillingUI,
  HeadingUI,
  BackUI,
  GlobalStyle,
} from './MessagesBillingScreen.css'
import CurrentTierCard from './components/CurrentTierCard'
import SpendingLimitCard from './components/SpendingLimitCard'
import { reducer } from './reducer/spending-limit.reducer'
import { getCurrentTheme } from '@common/utils/theme'
import { Provider as HSDSProvider } from 'hsds/components/hsds'
import PropTypes from 'prop-types'
import React, { useReducer } from 'react'

export const MessagesBillingContext = React.createContext()
export function MessagesBillingScreen({ messages, setSpendingLimit, isTrial }) {
  const [messagesState, dispatch] = useReducer(reducer, messages)

  const themeName = getCurrentTheme()

  return (
    <>
      <GlobalStyle />
      <HSDSProvider scope="hsds-react" themeName={themeName}>
        <MessagesBillingContext.Provider
          value={{
            state: { ...messagesState, isTrial },
            actions: { setSpendingLimit, dispatch },
          }}
        >
          <MessagesBillingUI data-testid="MessagesBillingScreen">
            <BackUI data-cy="MessagesBilling.Back.Button" href="/members/plan/">
              Overview
            </BackUI>
            <HeadingUI>Messages Billing</HeadingUI>
            <CurrentTierCard />
            <SpendingLimitCard />
          </MessagesBillingUI>
        </MessagesBillingContext.Provider>
      </HSDSProvider>
    </>
  )
}

MessagesBillingScreen.propTypes = {
  messages: PropTypes.object.isRequired,
  isTrial: PropTypes.bool.isRequired,
  setSpendingLimit: PropTypes.func.isRequired,
}

export default MessagesBillingScreen
