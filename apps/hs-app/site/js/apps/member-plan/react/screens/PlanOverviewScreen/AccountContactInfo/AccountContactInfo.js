import {
  AccountContactInfoUI,
  ContactEmailUI,
  ContactInfoUI,
  ContactTitleUI,
  PageLayoutUI,
} from './AccountContactInfo.css'
import { getCurrentTheme } from '@common/utils/theme'
import Avatar from 'hsds/components/avatar'
import Flexy from 'hsds/components/flexy'
import { Provider as HSDSProvider } from 'hsds/components/hsds'
import Page from 'hsds/components/page'
import Text from 'hsds/components/text'
import PropTypes from 'prop-types'
import React, { PureComponent } from 'react'

export class AccountContactInfo extends PureComponent {
  static propTypes = {
    owner: PropTypes.shape({
      id: PropTypes.number,
      name: PropTypes.string,
      fname: PropTypes.string,
      lname: PropTypes.string,
      phone: PropTypes.string,
      email: PropTypes.string,
      photo: PropTypes.oneOfType([PropTypes.string, PropTypes.bool]),
      lastVisit: PropTypes.string,
      showAddress: PropTypes.bool,
      showAvatar: PropTypes.bool,
    }),
    billing: PropTypes.shape({
      name: PropTypes.string,
      address: PropTypes.string,
      address2: PropTypes.string,
      city: PropTypes.string,
      state: PropTypes.string,
      zip: PropTypes.string,
      email: PropTypes.string,
      showAddress: PropTypes.bool,
      showAvatar: PropTypes.bool,
    }),
  }

  static defaultProps = {
    owner: {
      name: 'Account Owner',
      fname: 'Account',
      lname: 'Owner',
      phone: null,
      photo: false,
      lastVisit: null,
      showAddress: false,
      showAvatar: false,
    },
    billing: {
      name: null,
      address: null,
      address2: null,
      city: null,
      state: null,
      zip: null,
      email: null,
      showAddress: false,
      showAvatar: false,
    },
  }

  getInitials() {
    const { owner } = this.props
    return `${owner.fname.charAt(0)}${owner.lname.charAt(0)}`
  }

  getTitle() {
    if (this.showBillingContact()) {
      return 'Account Contacts'
    }

    return 'Account Owner'
  }

  getSubtitle() {
    if (this.showBillingContact()) {
      return `The Account Owner and billing contact for ${this.props.billing.name}`
    }

    return `${this.props.owner.fname} can make plan changes and update payment settings.`
  }

  showBillingContact() {
    const { owner, billing } = this.props
    return !!billing.email && owner.email !== billing.email
  }

  render() {
    const { owner, billing } = this.props
    const themeName = getCurrentTheme()

    return (
      <HSDSProvider scope="hsds-react" themeName={themeName}>
        <PageLayoutUI>
          <AccountContactInfoUI>
            <Page.Header
              data-cy="AccountContact.Page.Header"
              render={({ Title, Subtitle }) => (
                <div>
                  <Title headingLevel="h1" className="c-account-contacts-title">
                    {this.getTitle()}
                  </Title>
                  <Subtitle>{this.getSubtitle()}</Subtitle>
                </div>
              )}
            />
            <Flexy gap="md" align="top" className="u-mrg-t-2">
              <AccountOwnerContact
                {...owner}
                initials={this.getInitials()}
                showBillingContact={this.showBillingContact()}
              />
              <BillingContact
                {...billing}
                showBillingContact={this.showBillingContact()}
              />
            </Flexy>
          </AccountContactInfoUI>
        </PageLayoutUI>
      </HSDSProvider>
    )
  }
}

const AccountOwnerContact = props => {
  if (!props.showBillingContact) {
    return (
      <Contact
        {...props}
        showAvatar={true}
        className="c-account-contacts-owner single-contact"
      />
    )
  }

  return (
    <Contact
      {...props}
      title="Account Owner"
      className="c-account-contacts-owner"
    />
  )
}

const BillingContact = props => {
  if (!props.showBillingContact) {
    return null
  }

  return <Contact {...props} title="Billing Contact" />
}

const Contact = props => {
  return (
    <Flexy.Block className={props.className}>
      <Flexy gap="lg" align="top">
        <ContactAvatar {...props} />
        <ContactInfo {...props} />
      </Flexy>
    </Flexy.Block>
  )
}

const ContactAvatar = props => {
  const { name, photo, initials, showAvatar } = props

  if (!showAvatar || (!photo && !initials)) {
    return null
  }

  return (
    <Flexy.Item data-cy="AccountContact.Avatar">
      <Avatar name={name} size="xl" image={photo} initials={initials} />
    </Flexy.Item>
  )
}

const ContactInfo = props => {
  const { title, name, email } = props

  return (
    <ContactInfoUI>
      <ContactTitleUI size="h5" data-cy="AccountContact.ContactTitleUI">
        {title}
      </ContactTitleUI>
      <ContactLine data-cy="AccountContact.ContactLine">{name}</ContactLine>
      <ContactEmailUI
        href={`mailto:${email}`}
        target="_blank"
        data-cy="AccountContact.ContactEmailUI"
      >
        {email}
      </ContactEmailUI>
      <ContactAddress {...props} />
    </ContactInfoUI>
  )
}

const ContactAddress = props => {
  const { address, address2, city, state, zip, showAddress } = props

  if (!showAddress) {
    return null
  }

  return (
    <div>
      <ContactLine data-cy="AccountContact.ContactLine.address">
        {address}
      </ContactLine>
      <ContactLine data-cy="AccountContact.ContactLine.address2">
        {address2}
      </ContactLine>
      <ContactLine data-cy="AccountContact.ContactLine.cityStateZip">
        {city ? `${city} ` : null}
        {state ? `${state}, ` : null}
        {zip}
      </ContactLine>
    </div>
  )
}

const ContactLine = props => {
  if (!props.children) {
    return null
  }

  return (
    <Text faint data-cy="AccountContact.ContactLine.Name">
      {props.children}
      <br />
    </Text>
  )
}

export default AccountContactInfo
