import PropTypes from 'prop-types'

export const paymentType = PropTypes.shape({
  lineItems: PropTypes.array.isRequired,
  grandTotalWithTax: PropTypes.number.isRequired,
  tax: PropTypes.shape({
    amount: PropTypes.number.isRequired,
    rate: PropTypes.number.isRequired,
    isCharged: PropTypes.bool.isRequired,
  }).isRequired,
  monthlyCbpLoyaltyDiscount: PropTypes.shape({
    name: PropTypes.string,
    calculatedAmount: PropTypes.number,
    expiresAt: PropTypes.string,
    isContactBasedLoyaltyDiscount: PropTypes.bool,
  }),
}).isRequired

export const paymentsType = PropTypes.shape({
  monthly: paymentType,
  annual: paymentType,
  monthlyOnAnnual: paymentType,
}).isRequired

export const lastUbpPaymentDataType = PropTypes.shape({
  total: PropTypes.number.isRequired,
  increaseMonthly: PropTypes.number.isRequired,
  increaseMonthlyOnAnnual: PropTypes.number.isRequired,
}).isRequired

export const planType = PropTypes.shape({
  annualDiscount: PropTypes.number.isRequired,
  endOfLifeDate: PropTypes.string,
  shortName: PropTypes.string.isRequired,
  docsAddOn: PropTypes.number.isRequired,
}).isRequired
