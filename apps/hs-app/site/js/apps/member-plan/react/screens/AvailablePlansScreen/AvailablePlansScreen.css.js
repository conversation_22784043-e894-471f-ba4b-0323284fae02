import Button from 'hsds/components/button'
import Page from 'hsds/components/page'
import { getColor } from 'hsds/utils/color'
import styled, { createGlobalStyle } from 'styled-components'

export const GlobalStyle = createGlobalStyle`
  .s-available-plans {
    max-width: 1080px;
    margin: auto;
  }
  .c-alert {
    .c-alert__content {
      align-items: center;
      > * {
        min-width: initial;
      }
    }
  }
`

export const PageUI = styled(Page)`
  margin: 0px;
  max-width: 100%;
  min-width: 0px;
  &&& {
    --hsds-page-margin-top: 16px;
    --hsds-page-margin-bottom: 0;
    --hsds-page-max-width: 1080px;
    --hsds-page-min-width: 480px;
    --hsds-page-gutter: 0;
    min-height: 0;
  }

  .c-Page {
    margin: 0;
    max-width: 100%;
    min-width: 0;
  }
`
export const DividerUI = styled('div')`
  background: ${getColor('charcoal.300')};
  height: 1px;
  width: 100%;
  margin-top: 43px;
`
export const BackButtonUI = styled(Button)`
  margin-top: 15px;
  &&& {
    font-size: 13px;
  }
`
