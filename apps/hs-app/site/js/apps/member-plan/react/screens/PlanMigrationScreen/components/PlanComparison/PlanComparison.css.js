import Flexy from 'hsds/components/flexy'
import Page from 'hsds/components/page'
import { getColor, lighten } from 'hsds/utils/color'
import styled from 'styled-components'

export const PlanComparisonUI = styled(Page.Card)`
  position: relative;
  color: ${getColor('charcoal.700')};
  .c-Link {
    color: ${getColor('cobalt.600')};
  }
  p {
    margin-bottom: 0;
  }
`

export const PlanPriceUI = styled(Flexy)`
  .c-Flexy__block {
    padding: 24px 0;
    text-align: center;

    .c-Heading {
      font-size: 13px;
      margin-bottom: 10px;
    }

    .price {
      font-size: 30px;
      position: relative;

      .symbol {
        font-size: 20px;
        left: -14px;
        position: absolute;
        top: 0px;
      }
    }

    p + p {
      margin-top: -4px;
    }
  }

  .plan-price {
    border-radius: 4px;

    &--current {
      background-color: ${lighten(getColor('charcoal.200'), 1)};
    }

    &--new {
      background: ${lighten(getColor('cobalt.200'), 2)}
        url(${hsGlobal.imagePath}plans/comparison/comparison-new-plan.png)
        no-repeat bottom;
      background-size: cover;
    }

    .asterisk {
      margin-left: 2px;
    }
  }
`

export const CalloutUI = styled.div`
  position: relative;

  .asterisk {
    position: absolute;
    left: -8px;
  }
`

export const FooterUI = styled.div`
  background-color: ${getColor('charcoal.100')};
  border-radius: 0 0 4px 4px;
  bottom: 0;
  left: 0;
  padding: 24px 0;
  position: absolute;
  text-align: center;
  width: 100%;

  .c-Link {
    color: ${getColor('cobalt.700')};
  }
`
