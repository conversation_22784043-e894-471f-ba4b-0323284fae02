import Page from 'hsds/components/page'
import styled from 'styled-components'
import { createGlobalStyle } from 'styled-components'

export const GlobalStyle = createGlobalStyle`
  #planOverview.s-Settings-v2.s-Settings-v2{
    padding:0;
  }
`

export const PageUI = styled(Page)`
  &&& {
    --hsds-page-max-width: 700px;
  }

  .c-Page {
    margin: 0;
    max-width: 100%;
    min-width: 0;
  }

  .c-PageActions {
    padding: 0px;
    margin-top: 0;
    margin-bottom: 0;
    .c-PageActions__primary {
      padding: 0px;
      button {
        height: 40px;
        min-width: 120px;
      }
    }
    .c-PageActions__serious {
      padding: 0px;
    }
  }
`
