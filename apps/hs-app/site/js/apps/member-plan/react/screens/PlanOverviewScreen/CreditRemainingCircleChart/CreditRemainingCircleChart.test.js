import CreditRemainingCircleChart from './CreditRemainingCircleChart'
import {
  CHART_CLASS_LOW,
  CHART_CLASS_MED,
  CHART_CLASS_HIGH,
  CHART_CLASS_DISABLED,
} from './CreditRemainingCircleChart.css.js'
import '@testing-library/jest-dom'
import { render } from '@testing-library/react'
import React from 'react'

describe('CreditRemainingCircleChart tests', () => {
  it('should render high class at 100% credits remaining', () => {
    const { container } = render(
      <CreditRemainingCircleChart
        totalCreditsRemaining={1000}
        totalForNextAnnualPayment={1000}
      />
    )
    expect(container.querySelector('figure')).toHaveClass(CHART_CLASS_HIGH)
    expect(container.querySelector('circle.circle-foreground')).toHaveClass(
      'percent100'
    )
  })

  it('should render medium class at 66% credits remaining', () => {
    const { container } = render(
      <CreditRemainingCircleChart
        totalCreditsRemaining={666}
        totalForNextAnnualPayment={1000}
      />
    )
    expect(container.querySelector('figure')).toHaveClass(CHART_CLASS_MED)
    expect(container.querySelector('circle.circle-foreground')).toHaveClass(
      'percent66'
    )
  })

  it('should render low class at 33% credits remaining', () => {
    const { container } = render(
      <CreditRemainingCircleChart
        totalCreditsRemaining={333}
        totalForNextAnnualPayment={1000}
      />
    )
    expect(container.querySelector('figure')).toHaveClass(CHART_CLASS_LOW)
    expect(container.querySelector('circle.circle-foreground')).toHaveClass(
      'percent33'
    )
  })

  it('should render high class and 100% when more credits than needed for next annual payment', () => {
    const { container } = render(
      <CreditRemainingCircleChart
        totalCreditsRemaining={999999}
        totalForNextAnnualPayment={12000}
      />
    )
    expect(container.querySelector('figure')).toHaveClass(CHART_CLASS_HIGH)
    expect(container.querySelector('circle.circle-foreground')).toHaveClass(
      'percent100'
    )
  })

  it('should format total credits correctly', () => {
    let remaining, expectedFormatted
    const amounts = [
      [0, '$0'],
      [0.01, '$0.01'],
      [999.99, '$999.99'],
      [1000, '$1k'],
      [9999.99, '$9.9k'],
      [10000, '$10k'],
      [178987.65, '$178k'],
    ]

    amounts.forEach(credits => {
      remaining = credits[0]
      expectedFormatted = credits[1]

      const { container } = render(
        <CreditRemainingCircleChart
          totalCreditsRemaining={remaining}
          totalForNextAnnualPayment={1000}
        />
      )

      const figcaption = container.querySelector('figcaption')
      expect(figcaption).toHaveTextContent(expectedFormatted)
    })
  })

  it('should apply CHART_CLASS_DISABLED when totalForNextAnnualPayment is 0', () => {
    const { container } = render(
      <CreditRemainingCircleChart
        totalCreditsRemaining={500}
        totalForNextAnnualPayment={0}
      />
    )
    expect(container.querySelector('figure')).toHaveClass(CHART_CLASS_DISABLED)
    expect(container.querySelector('circle.circle-foreground')).toHaveClass('percent0')
  })
})
