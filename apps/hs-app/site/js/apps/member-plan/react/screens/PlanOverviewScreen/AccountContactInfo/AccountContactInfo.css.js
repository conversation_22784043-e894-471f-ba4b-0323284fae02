import Flexy from 'hsds/components/flexy'
import Heading from 'hsds/components/heading'
import Link from 'hsds/components/link'
import Page from 'hsds/components/page'
import { PageLayoutUI as PageLayoutUIBase } from 'hsds/components/page/Page.styles'
import { getColor } from 'hsds/utils/color'
import styled from 'styled-components'

export const PageLayoutUI = styled(PageLayoutUIBase)`
  // clearing page layout styles, as we only need the tokens and medias queries breakpoint
  && {
    min-height: 0;
    width: 100%;
    max-width: 100%;
    padding: 0;
  }
`

export const AccountContactInfoUI = styled(Page.Card)`
  .c-account-contacts-title {
    margin-top: 0;
    text-align: left;
  }

  .c-account-contacts-owner.single-contact {
    .c-Avatar__title {
      font-size: 16px;
      font-weight: 500;
    }
    .c-Text {
      color: ${getColor('charcoal.900')};
    }
  }
`

export const ContactEmailUI = styled(Link)`
  display: inline-block;
  margin-bottom: 2px;

  &:hover {
    color: ${getColor('cobalt.600')};
  }
`

export const ContactInfoUI = styled(Flexy.Block)`
  line-height: 1.4em;
`

export const ContactTitleUI = styled(Heading)`
  margin-bottom: 14px;
`
