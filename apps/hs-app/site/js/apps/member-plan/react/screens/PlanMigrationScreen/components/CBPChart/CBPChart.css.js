import Heading from 'hsds/components/heading'
import Page from 'hsds/components/page'
import { getToken } from 'hsds/tokens'
import styled from 'styled-components'

export const CardUI = styled(Page.Card)`
  position: relative;
`

export const Header = styled.header`
  margin-bottom: 15px;
`

export const HeaderTitle = styled(Heading)`
  font-size: 18px;
  margin-bottom: 4px;
`

export const Footer = styled.footer`
  margin-top: 24px;
  padding: 20px;
  border-radius: 4px;
  border: 1px solid ${getToken('color.charcoal.300')};
  background: ${getToken('color.charcoal.100')};

  h3 {
    color: ${getToken('color.text.headline')};
    font-weight: 500;
    font-size: 14px;
    line-height: 22px;
    margin-bottom: 4px;
  }

  p {
    margin-bottom: 0;
    color: ${getToken('color.text.dark')};
    font-size: 14px;
    line-height: 22px;
  }
`

export const ChartContainer = styled.canvas`
  height: 190px;
  width: 100%;
  margin-bottom: 24px;
`
