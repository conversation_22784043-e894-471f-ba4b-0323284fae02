import Page from 'hsds/components/page'
import { getToken } from 'hsds/tokens'
import styled from 'styled-components'

export const CardUI = styled(Page.Card)`
  .c-PageContent {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
  }

  .c-Button {
    width: 100%;
    max-width: 225px;
  }

  h2 {
    color: ${getToken('color.text.default')};
    font-size: 18px;
    font-weight: 500;
    margin: 0;
  }

  p {
    color: ${getToken('color.text.default')};
    font-size: 13px;
    font-weight: 400;
    text-align: center;
    margin: 17px 0 24px;
  }
`
