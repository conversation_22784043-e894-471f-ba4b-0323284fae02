import Button from 'hsds/components/button'
import Card from 'hsds/components/card'
import { ChoiceGroup } from 'hsds/components/choice'
import Page from 'hsds/components/page'
import Radio from 'hsds/components/radio'
import { getColor } from 'hsds/utils/color'
import styled from 'styled-components'

export const PageCardUI = styled(Page.Card)`
  p {
    margin-bottom: 0;
  }
`
export const SpendingLimitUI = styled('div')`
  transition: all 300ms ease-in-out 0ms;
`
export const SpendingLimitText = styled('div')`
  margin-top: 16px;
  p {
    line-height: 22px;
    color: ${getColor('charcoal.700')};
    strong {
      color: ${getColor('charcoal.1000')};
    }
  }
`

export const RadioUI = styled(Radio)`
  .c-Choice__label {
    width: 100%;
    .c-Flexy {
      align-items: baseline;
      width: 95%;
    }
  }
  .c-Choice__label-text {
    padding-left: unset;
    color: ${getColor('charcoal.1000')};
  }
`

export const ChoiceGroupUI = styled(ChoiceGroup)`
  .c-FormGroupChoice {
    margin-bottom: 18px;

    &:last-child {
      margin-bottom: 0;
    }
  }
`
export const CardUI = styled(Card)`
  background: ${getColor('cobalt.100')};
  padding: 30px;
  display: flex;
  flex-direction: column;
  margin-top: 22px;
  border: none;
  .c-Text {
    color: ${getColor('charcoal.700')};
    strong {
      font-weight: 500;
    }
  }
`
export const ReviewButtonUI = styled(Button)`
  margin-top: 20px;
  max-width: fit-content;
`
