import SalesTaxLineItem from '../../SalesTaxLineItem/SalesTaxLineItem'
import {
  GlobalStyle,
  ModalUI,
  TriggerButtonUI,
  LinkLearnMoreUI,
} from './AnnualBillSwitchModal.css'
import formatDate from '@common/templates/helpers/formatDate'
import formatMoney from '@common/templates/helpers/formatMoney'
import formatPercent from '@common/templates/helpers/formatPercent'
import { openBeaconAndNavigate } from '@common/utils/Beacon'
import { getCurrentTheme } from '@common/utils/theme'
import { connect } from '@helpscout/brigade'
import App from '@member-plan/App'
import Constants from '@member-plan/utils/defs'
import Heading from 'hsds/components/heading'
import { Provider as HSDSProvider } from 'hsds/components/hsds'
import Pricing3TagsIllo from 'hsds/illos/pricing-3-tags'
import PropTypes from 'prop-types'
import React, { Component } from 'react'
import { Provider } from 'react-redux'

function noop() {}

const SALES_TAX_DOC_URL =
  'https://docs.helpscout.com/article/106-payments-and-invoices#sales-tax'

export class AnnualBillSwitchModal extends Component {
  state = {
    isModalOpen: false,
    isPaying: false,
  }

  getTotalUsers = () => {
    const { payments } = this.props
    const { monthly } = payments

    //TODO copy the logic from AnnualPaymentView.serializeData() if we ever
    // come back to plans with a base price and included users
    return monthly.lineItems[0].quantity
  }

  getAmounts = () => {
    const { plan, payments } = this.props
    const { annualDiscount, price, pricePerUser } = plan
    let {
      subtotal: annualSubtotal,
      grandTotalWithTax: annualGrandTotalWithTax,
    } = payments.annual

    if (plan.isContactsBasedBillingType) {
      const annualPricePerUser = null
      const monthlyPrice = Number.parseInt(payments.monthly.lineItems[0].tierPrice)
      const annualPrice = Number.parseInt(payments.annual.lineItems[0].price)
      const annualDiscountDollars = (monthlyPrice - annualPrice) * 12
      annualSubtotal += annualDiscountDollars

      return {
        monthlyPrice,
        annualSubtotal,
        annualPricePerUser,
        annualDiscountDollars,
        annualGrandTotalWithTax,
      }
    }

    const monthlyPrice = Number.parseInt(price)
    const monthlyPricePerUser = Number.parseInt(pricePerUser)

    const annualDiscountPerUser = Number.parseInt(annualDiscount)
    const annualPricePerUser = Math.abs(
      monthlyPricePerUser - annualDiscountPerUser
    )

    // with real dollar accounting subtotal and grandtotal are the same,
    // so it looks like the discount is $0. Since we're still displaying
    // the modal in the same way as when we used inflated accounting we'll
    // just recalculate the discount based on the number of users and then
    // inflate the subtotal with that
    const totalUsers = this.getTotalUsers()
    const annualDiscountDollars = totalUsers * annualDiscountPerUser * 12
    annualSubtotal += annualDiscountDollars

    return {
      monthlyPrice,
      annualSubtotal,
      annualPricePerUser,
      annualDiscountDollars,
      annualGrandTotalWithTax,
    }
  }

  getTriggerButtonConfig = () => {
    const {
      hasExistingAnnualPayment,
      showAutoRenewSection,
      subscription,
      plan,
      payments,
      totalCreditsRemaining,
    } = this.props

    const isLink = hasExistingAnnualPayment || showAutoRenewSection

    const triggerCypressAttribute = isLink
      ? 'annualBilling.Credit.link'
      : 'annualBilling.Switch.button'
    let triggerText = isLink ? 'make a one-time payment' : 'Switch to Annual'

    const totalUsers = this.getTotalUsers()
    const annualDiscountPerUser = Number.parseInt(plan.annualDiscount)
    const monthlyDiscountDollars = totalUsers * annualDiscountPerUser
    const monthlySubtotal = payments.monthly.subtotal
    const monthlySubtotalWithoutAnnualDiscount =
      monthlySubtotal + monthlyDiscountDollars

    if (
      isLink &&
      !subscription.annualAutoRenewEnabled &&
      monthlySubtotalWithoutAnnualDiscount > totalCreditsRemaining
    ) {
      triggerText = 'make a new annual payment'
    }

    return {
      triggerCypressAttribute,
      isLink,
      triggerText,
    }
  }

  makeAnnualPayment = event => {
    event && event.preventDefault()

    this.setState({ isPaying: true }, () => {
      const { card, details, member } = this.props
      const doPayment = App.controller.checkPrepays()

      if (doPayment) {
        App.controller.billingFrequency = Constants.ANNUAL_CYCLE

        const data = {
          ...card,
          ...details,
          cycle: Constants.ANNUAL_CYCLE,
        }

        App.controller
          .makePaymentWithStoredCard(member.id, data)
          .done(this.paymentSuccess)
          .fail(this.paymentError)
      }
    })
  }

  paymentSuccess = (response, statusText, xhr) => {
    const location = HS.Utils.Ajax.getRedirect(xhr)

    if (location !== null) {
      HS.Controller.PubSub.publish('ajax-redirect', location)
      window.location.href = location
      return
    }

    App.controller.displayUpdatedPlan()
    this.setState({ isModalOpen: false, isPaying: false })
  }

  paymentError = response => {
    if (response.status !== 301) {
      App.controller.hasFailedAnnualPayment = true
      this.setState({ isModalOpen: false, isPaying: false }, () => {
        App.trigger('annual-payment-failed', response)
      })
    }
  }

  // match the same descriptions as in PromoRowView
  getPromoDescriptionLines = promo => {
    if (!promo || typeof promo !== 'object') {
      return []
    }

    if (promo.type === 'credit') {
      return [`${formatMoney(promo.remaining)} remaining`]
    }

    let descriptionLines = []

    if (promo.discType === Constants.DISCOUNT_TYPE_FLAT_AMOUNT_PERCENT_OFF_TOTAL) {
      descriptionLines.push(`${formatPercent(promo.amount)}% off`)
    } else if (promo.discType === Constants.DISCOUNT_TYPE_FLAT_AMOUNT_PERCENT_OFF_PER_USER) {
      descriptionLines.push(`${formatPercent(promo.amount)}% off per User`)
    }

    if (promo.isExpires && promo.expiresAt) {
      descriptionLines.push(
        `Expires ${formatDate(promo.expiresAt, 'MMM D, YYYY')}`
      )
    }

    const {
      tierDiscountPercentageText = null,
      minRequirementsText = null,
      maxUserLimitText = null,
    } = promo

    if (tierDiscountPercentageText !== null && tierDiscountPercentageText !== '') {
      descriptionLines.push(tierDiscountPercentageText)
    }

    if (minRequirementsText !== null && minRequirementsText !== '') {
      descriptionLines.push(`Minimum of ${minRequirementsText}`)
    }

    if (maxUserLimitText !== null && maxUserLimitText !== '') {
      descriptionLines.push(`Maximum of ${maxUserLimitText}`)
    }

    return descriptionLines
  }

  renderPromoLineItems() {
    const annualPromos = this.props.payments.annual.promos ?? []
    if (annualPromos.length === 0) {
      return null
    }

    return annualPromos.map((promo, index) => {
      const promoIdentifier = promo.name.toLowerCase().replace(/\s+/g, '-')
      const key = `annual-promo-${promoIdentifier}-${index}`
      const descriptionLines = this.getPromoDescriptionLines(promo)

      return (
        <tr className="AnnualBillSwitchModal__table__promo-item" key={key}>
          <td>
            <Heading
              selector="h4"
              size="h4"
              data-cy={`annualPayment.promo.${promoIdentifier}.heading`}
            >
              {promo.name}
            </Heading>
            {descriptionLines.length > 0 && (
              <div className="AnnualBillSwitchModal__promo-description" data-cy={`annualPayment.promo.${promoIdentifier}.descriptionContainer`}>
                {descriptionLines.map((line, lineIndex) => (
                  <p key={`desc-${promoIdentifier}-${lineIndex}`} data-cy={`annualPayment.promo.${promoIdentifier}.description.${lineIndex}`}>
                    {line}
                  </p>
                ))}
              </div>
            )}
          </td>
          <td
            className="amount"
            data-cy={`annualPayment.promo.${promoIdentifier}.amount`}
          >
            {formatMoney(promo.calculatedAmount)}
          </td>
        </tr>
      );
    }).filter(Boolean);
  }

  render() {
    const themeName = getCurrentTheme()
    const { isModalOpen, isPaying } = this.state
    const {
      payments: { monthly },
      plan,
      hasExistingAnnualPayment,
      showAutoRenewSection,
      taxStore,
      updateContactInfo,
      subscription,
    } = this.props
    const { isTrial, trialEndDay } = subscription
    const trialEndDayFormatted = trialEndDay
      ? formatTrialEndDay(trialEndDay)
      : null

    const {
      name: planName,
      isContactsBasedBillingType,
      percentAnnualDiscount,
    } = plan
    const totalUsers = this.getTotalUsers()
    const {
      monthlyPrice,
      annualSubtotal,
      annualPricePerUser,
      annualDiscountDollars,
      annualGrandTotalWithTax,
    } = this.getAmounts()
    const contactsBasedBilledTier = monthly.lineItems[0].quantity

    const { triggerCypressAttribute, isLink, triggerText } =
      this.getTriggerButtonConfig()

    return (
      <HSDSProvider scope="hsds-react" themeName={themeName}>
        <GlobalStyle />
        <ModalUI
          className="AnnualBillSwitchModal"
          isOpen={isModalOpen}
          title={`${isContactsBasedBillingType ? `${plan.name} Plan:` : ''} Make an Annual Payment`}
          showAutoRenewSection={true}
          trigger={
            <TriggerButtonUI
              size="sm"
              linked={isLink}
              inlined={isLink}
              color="blue"
              data-cy={triggerCypressAttribute}
            >
              {triggerText}
            </TriggerButtonUI>
          }
          onOpen={() => {
            this.setState({ isModalOpen: true })
          }}
          onClose={() => {
            this.setState({ isModalOpen: false })
            HS.Controller.PubSub.unsubscribe('Ajax:Error', this.props.subId)
          }}
          version={2}
        >
          <div className="AnnualBillSwitchModal__body">
            <section className="AnnualBillSwitchModal__notice">
              <Pricing3TagsIllo size={62} />
              <p data-cy="annualPayment.Note">
                When you make an annual payment you are purchasing credits.{' '}
                {isContactsBasedBillingType
                  ? 'Credits get used monthly. If you increase your monthly contacts they will run out faster.'
                  : 'Credits get used monthly, and if you add users over time they will run out faster.'
                }
                {' '}
                <LinkLearnMoreUI
                  color="blue"
                  linked
                  data-beacon-article="5f08b88804286306f8068585"
                  data-cy="annualPayment.GetHelp.link"
                >
                  Learn more
                </LinkLearnMoreUI>
              </p>
            </section>
            <table className="AnnualBillSwitchModal__table">
              <tbody>
                <tr className="AnnualBillSwitchModal__table__subtotal">
                  <td>
                    <Heading
                      selector="h4"
                      size="h4"
                      data-cy="annualPayment.Subtotal.heading"
                    >
                      Annual Subtotal
                    </Heading>
                    <p data-cy="annualPayment.Subtotal.description">
                      {isContactsBasedBillingType
                        ? (
                          <>
                            Up to {contactsBasedBilledTier} contacts for ${monthlyPrice}/mo,
                            plus Inboxes, Docs, and Add-ons
                          </>
                        ) : (
                          <>
                            {totalUsers} users @ ${monthlyPrice} per month ({planName}{' '}
                            Plan) plus Mailboxes, Docs, and Add-ons
                          </>
                        )
                      }
                    </p>
                  </td>
                  <td
                    className="amount"
                    data-cy="annualPayment.Subtotal.amount"
                  >
                    {formatMoney(annualSubtotal)}
                  </td>
                </tr>
                <tr className="AnnualBillSwitchModal__table__savings">
                  <td>
                    <Heading
                      selector="h4"
                      size="h4"
                      data-cy="annualPayment.Savings.heading"
                    >
                      Annual Payment Savings
                    </Heading>
                    <p data-cy="annualPayment.Savings.description">
                      {isContactsBasedBillingType
                        ? `You saved ${percentAnnualDiscount}% from the cost of your contacts`
                        : `You pay only $${annualPricePerUser} per user per month`
                      }
                    </p>
                  </td>
                  <td className="amount" data-cy="annualPayment.Savings.amount">
                    ({formatMoney(annualDiscountDollars)})
                  </td>
                </tr>
                {this.renderPromoLineItems()}
                {taxStore ? (
                  <tr className="AnnualBillSwitchModal__table__tax">
                    <td colSpan="2">
                      <Provider store={taxStore}>
                        <SalesTaxLineItem
                          estimatorDisabled={
                            !hsGlobal.memberPermissions.manageAccount
                          }
                          onFindOutMoreClick={event => {
                            event && event.preventDefault()
                            window.open(SALES_TAX_DOC_URL, '_blank')
                          }}
                          onGetInTouchClick={event => {
                            // Beacon is not loaded, so fallback to the regular link
                            if (typeof window.Beacon === 'undefined') return

                            // prevent the mailto and open Beacon instead directly on the message window
                            event && event.preventDefault()
                            openBeaconAndNavigate('/ask/message/')
                          }}
                          onUpdateDetailsClick={() => {
                            this.setState({ isModalOpen: false }, () => {
                              updateContactInfo()
                            })
                          }}
                        />
                      </Provider>
                    </td>
                  </tr>
                ) : null}
              </tbody>
              <tfoot>
                <tr className="AnnualBillSwitchModal__table__total">
                  <td colSpan="2">
                    <div className="total-container">
                      <p>
                        {!isTrial
                          ? 'Today you pay'
                          : `Charged when your trial ends on ${trialEndDayFormatted}`}
                      </p>
                      <div
                        className="total-amount"
                        data-cy="annualPayment.Total.amount"
                      >
                        <span className="currency">$</span>
                        <span className="amount">
                          {formatMoney(annualGrandTotalWithTax, 2, false)}
                        </span>
                      </div>
                    </div>
                  </td>
                </tr>
              </tfoot>
            </table>
          </div>
          <ModalUI.ActionFooter
            primaryButtonText={!isTrial ? 'Pay Now' : 'Schedule Payment'}
            onPrimaryClick={this.makeAnnualPayment}
            primaryButtonDisabled={isPaying}
            onCancel={() => {
              this.setState({ isModalOpen: false })
            }}
          />
        </ModalUI>
      </HSDSProvider>
    )
  }
}

AnnualBillSwitchModal.propTypes = {
  card: PropTypes.any,
  details: PropTypes.any,
  features: PropTypes.any,
  member: PropTypes.any,
  payments: PropTypes.any,
  plan: PropTypes.any,
  hasExistingAnnualPayment: PropTypes.bool,
  showAutoRenewSection: PropTypes.bool,
  subId: PropTypes.any,
  subscription: PropTypes.any,
  taxStore: PropTypes.any,
  updateContactInfo: PropTypes.func,
}

AnnualBillSwitchModal.defaultProps = {
  updateContactInfo: noop,
}

const mapStateToProps = ({
  card,
  details,
  features,
  member,
  payments,
  plan,
  hasExistingAnnualPayment,
  showAutoRenewSection,
  subId,
  subscription,
  totalCreditsRemaining,
  taxStore,
}) => {
  return {
    card,
    details,
    features,
    member,
    payments,
    plan,
    hasExistingAnnualPayment,
    showAutoRenewSection,
    subId,
    subscription,
    totalCreditsRemaining,
    taxStore,
  }
}

const mapActionsToProps = store => {
  const { updateContactInfo } = store.getStatelessExternalActions()

  return { updateContactInfo }
}

/**
 * The trialEndDay coming from the `subscription` object includes a `0` on a single digit day
 * (so "July 03rd" instead of "July 3rd"), this function removes that zero if it exists
 * @param {string} trialEndDay the date to format
 * @return {string}
 */
function formatTrialEndDay(trialEndDay) {
  const [month, day] = trialEndDay.split(' ')

  if (day.charAt(0) === '0') {
    return `${month} ${day.substring(1)}`
  }
  return trialEndDay
}

export default connect(
  mapStateToProps,
  mapActionsToProps
)(AnnualBillSwitchModal)
