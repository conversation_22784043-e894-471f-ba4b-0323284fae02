<h5 class="u-mrg-b-1">
  <a href="{{data.company.url}}" target="_blank">{{data.company.name}}</a>
</h5>
{{#if data.company.isPartnerSourced}}
  <span class="badge green">Partner-Sourced</span>
{{/if}}
{{#if data.company.hasHipaa}}
  <span class="badge purple">HIPAA</span>
{{/if}}
{{#if data.isContactBased}}
  <span class="badge blue">Contact-Based</span>
{{/if}}
{{#if data.company.isDoNotLogin}}
  <span class="badge red">DO NOT ASSUME</span>
{{/if}}
<ul>
    {{#if data.isCanceled}}
        <li><span class="c-sb-list-item__label t-tx-charcoal-650">
          Canceled on {{data.company.canceled}}
        </span></li>
        {{else}}
        <li><span class="c-sb-list-item__label t-tx-charcoal-650">
            Customer since {{data.company.created}}
        </span></li>
    {{/if}}
</ul>

{{#data.hasMember}}
<h5><a href="{{data.member.url}}" target="_blank">{{data.role}}</a></h5>
<ul>
  {{#if data.member.canceled}}
    <li>Status: <span class="red">Canceled</span></li>
    <li>Canceled: {{ data.member.canceledAt}}</li>
  {{else}}
    <li>Status: Active</li>
  {{/if}}
</ul>
{{/data.hasMember}}

{{#if data.company.isFirstNinetyDays}}
  <h4>First 90 Days</h4>
{{else}}
  <h4>Usage Stats</h4>
{{/if}}
<ul>
 <li><span class="t-tx-charcoal-900">Saved Replies:</span> {{data.savedReplyCount}}</li>
 <li><span class="t-tx-charcoal-900">Tags:</span> {{data.tagCount}}</li>
 <li><span class="t-tx-charcoal-900">Views:</span> {{data.viewsCount}}</li>
 <li><span class="t-tx-charcoal-900">Workflows:</span> {{data.workflowCount}}</li>
</ul>
<h4><a href="{{data.subscription.url}}" target="_blank">Subscription</a></h4>
<ul>
{{#if data.company.hasStatus}}
    <li><span class="t-tx-charcoal-900">Status:</span> {{data.company.status}}</li>
{{/if}}
{{#if data.showPaymentDetails}}
  <li><span class="t-tx-charcoal-900">Plan:</span> {{data.plan}}</li>
  {{#if data.isContactBased}}
    <li><span class="t-tx-charcoal-900">CBP Tier:</span> {{#ifCond data.currentTier '===' '-'}}Not Set{{else}}{{data.currentTier}}{{/ifCond}}</li>
    <li><span class="t-tx-charcoal-900">CBP Price:</span> ${{data.startingPrice}}</li>
  {{else}}
    <li><span class="t-tx-charcoal-900">CBP:</span> {{data.subscription.cbpMigrationDate}}</li>
  {{/if}}
  <li><span class="t-tx-charcoal-900">Users:</span> {{data.users}} ({{data.lightUserCount}} Light)</li>
  <li><span class="t-tx-charcoal-900">Inboxes:</span> {{data.mailboxes}}</li>
  <li><span class="t-tx-charcoal-900">Payment:</span> <span id="nextPmt">{{data.subscription.nextPaymentDate}}</span></li>
   {{#if data.hasLTV}}
   <li>${{data.lifetimeVal}} lifetime value</li>
   {{/if}}
   {{#if data.expectedUsers}}
   <li><span class="t-tx-charcoal-900">Expected users:</span> {{data.expectedUsers}}</li>
   {{/if}}
{{else}}
  <li><span class="t-tx-charcoal-900">Users:</span> {{data.users}} ({{data.lightUserCount}} Light)</li>
  <li><span class="t-tx-charcoal-900">Inboxes:</span> {{data.mailboxes}}</li>
{{/if}}
{{#ifCond data.subscription.billingFrequencyText '!==' null}}
    <li><span class="t-tx-charcoal-900">Billing: </span>{{data.subscription.billingFrequencyText}}</li>
{{/ifCond}}
{{#if data.showPaymentDetails}}
    {{#if data.addons}}
    <li><span class="t-tx-charcoal-900">Add-ons:</span> {{join data.addons ", "}}</li>
    {{/if}}
{{/if}}

{{#if data.promos}}
    <li><span class="t-tx-charcoal-900">Promos:</span><ul>
    {{#each data.promos}}
        <li>{{name}}</li>
    {{/each}}
    </ul></li>
{{/if}}
</ul>

<div class="c-sb-section c-sb-section--toggle">
  <div class="c-sb-section__title js-sb-toggle">
  {{#if data.docs.isAvail}}
    <i class="icon-sb"></i> Docs: Yes, {{data.docs.siteCount}} {{data.docs.label}}
  {{else}}
        <i class="icon-sb"></i>Docs: No
  {{/if}}
    <i class="caret sb-caret"></i>
  </div>
  <div class="c-sb-section__body">
    <ul class="styled">
    {{#each data.docs.sites}}
            {{#if hasUrl}}
                <li><a href="{{url}}">{{subdomain}}</a></li>
            {{else}}
                <li>{{subdomain}}: public site disabled</li>
            {{/if}}
        {{/each}}
    </ul>
  </div>
</div>

{{#if data.linked}}
Manually linked:
 <ul>
    <li>by {{data.linked.byName}}</li>
    <li>on {{data.linked.at}}</li>
</ul>
{{/if}}
