import { ReactView } from '@helpscout/brigade'
import React from 'react'
import BlankInboxLoader from '../react/components/BlankInboxLoader'
import { unescapeHtml } from 'shared/utils/StringUtils'

const LoaderView = ReactView(
  Marionette.ItemView.extend({
    template() {
      const { message } = this.options
      return <BlankInboxLoader message={unescapeHtml(message)} />
    },
  })
)

export default LoaderView
