import App from '../App'
import PersonalizationQuestions from '../react/views/PersonalizationQuestions'
import LoaderView from './LoaderView'
import { ReactView } from '@helpscout/brigade'
import React from 'react'

const PersonalizationQuestionsView = ReactView(
  Marionette.ItemView.extend({
    template() {
      return <PersonalizationQuestions save={this.save.bind(this)} />
    },
    async getMailboxSlug() {
      let mailboxSlug = ''

      if (App.data.mailbox) {
        mailboxSlug = App.data.mailbox.get('slug')
      }

      // it likely won't be available, so we'll fetch the first mailbox
      if (!mailboxSlug) {
        try {
          const response = await fetch(`${hsGlobal.apiPath}mailboxes?limit=1`)
          const data = await response.json()
          if (data.mailboxes && data.mailboxes.length > 0) {
            mailboxSlug = data.mailboxes[0].slug
          }
        } catch (error) {
          console.error('Failed to fetch mailbox:', error)
        }
      }

      return mailboxSlug
    },
    save(data) {
      // show loader before saving
      App.main.show(
        new LoaderView({
          message: 'Personalizing your experience&hellip;',
        })
      )

      this.model.set(data)
      this.model.save().then(async () => {
        const mailboxSlug = await this.getMailboxSlug()
        const redirectUrl = mailboxSlug
          ? `/inboxes/${mailboxSlug}/views?tour=1`
          : '/'

        window.location.href = redirectUrl
      })
    },
  })
)

export default PersonalizationQuestionsView
