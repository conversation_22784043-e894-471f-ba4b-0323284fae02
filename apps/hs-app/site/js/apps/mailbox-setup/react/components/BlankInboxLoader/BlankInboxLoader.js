import React from 'react'
import PropTypes from 'prop-types'
import Loader from 'shared/components/Loader'
import {
  SkeletonW<PERSON>per,
  SkeletonHeader,
  SkeletonLayout,
  SkeletonSidenav,
  SkeletonMain,
} from './BlankInboxLoader.css'

function BlankInboxLoader({ message }) {
  return (
    <SkeletonWrapper>
      <Loader
        absolute={true}
        imagePath={hsGlobal.imagePath}
        message={message}
        gradientOverlay={true}
      />
      <SkeletonHeader />
      <SkeletonLayout>
        <SkeletonSidenav />
        <SkeletonMain />
      </SkeletonLayout>
    </SkeletonWrapper>
  )
}

export default BlankInboxLoader

BlankInboxLoader.propTypes = {
  message: PropTypes.string,
}
