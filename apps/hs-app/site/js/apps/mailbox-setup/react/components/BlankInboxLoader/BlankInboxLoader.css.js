import styled from 'styled-components'

import { getColor } from 'hsds/utils/color'

const HEADER_HEIGHT = 54

export const SkeletonWrapper = styled.div`
  position: relative;
`

export const SkeletonHeader = styled.header`
  background: ${getColor('cobalt.800')};
  height: ${HEADER_HEIGHT}px;
  position: relative;
`

export const SkeletonLayout = styled.div`
  display: grid;
  grid-template-rows: 100%;
  grid-template-columns: minmax(58px, min-content) 1fr;
  grid-template-areas: 'sidenav mailboxContent';
  width: 100%;
  height: 100%;
`

export const SkeletonSidenav = styled.aside`
  background-color: ${getColor('charcoal.200')};
  border-right: 1px solid ${getColor('charcoal.400')};
  width: 250px;
  height: calc(100vh - ${HEADER_HEIGHT}px);
`
export const SkeletonMain = styled.main`
  grid-area: mailboxContent;
  overflow: hidden;
  height: calc(100vh - ${HEADER_HEIGHT}px);
`
