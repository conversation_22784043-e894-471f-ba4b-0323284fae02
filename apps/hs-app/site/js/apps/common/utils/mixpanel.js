const MEMBER_ID_ALLOW_LIST = [
  'Clicked Button',
  'Clicked Copy Workflow Button',
  'Clicked Copy Workflow to Mailbox Button',
  'Clicked Link',
  'In App Tour "Next" Button Clicked',
  'In App Tour "Close" Button Clicked',
]

/**
 * Tracks a mixpanel event. This is a wrapper for the mixpanel track function.
 * It is safe to use this function if mixpanel is not defined or if mixpanel
 * has not finished loading and initializing yet.
 *
 * If mixpanel is enabled, but it has not finished loading yet, a mixpanel
 * stub will be used. The init call and any track calls will be queued up
 * by the stub and executed once the stub is replaced with the real mixpanel.
 *
 * @param eventName  - The name of the event
 * @param properties - Additional properties to send with the event
 */
export const track = (eventName, properties = {}) => {
  if (typeof window?.mixpanel?.track === 'function') {
    const { companyId, memberId } = window.hsGlobal ?? {}
    try {
      window.mixpanel.track(eventName, {
        ...properties,
        companyId,
        memberId: MEMBER_ID_ALLOW_LIST.includes(eventName)
          ? memberId
          : undefined,
        userId: memberId,
      })
    } catch (e) {
      console.error(e)
    }
  }
}
