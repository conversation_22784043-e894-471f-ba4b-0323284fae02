import { track } from '../mixpanel'

describe('track', () => {
  beforeEach(() => {
    const spy = jest.fn()
    global.hsGlobal = { companyId: 10, memberId: 200 }
    global.mixpanel = { track: spy }
    jest.clearAllMocks()
  })

  afterEach(() => {
    jest.clearAllMocks()
  })

  test('Executes the track function', () => {
    track('Some Event')
    expect(global.mixpanel.track).toHaveBeenCalledWith('Some Event', {
      companyId: 10,
      userId: 200,
    })
  })

  test('Executes the track function with member properties', () => {
    track('Some Event', { random: 'property' })
    expect(global.mixpanel.track).toHaveBeenCalledWith('Some Event', {
      companyId: 10,
      userId: 200,
      random: 'property',
    })
  })

  test('Executes the track function with optional properties', () => {
    track('Some Event', { random: 'property' })
    expect(global.mixpanel.track).toHaveBeenCalledWith('Some Event', {
      companyId: 10,
      userId: 200,
      random: 'property',
    })
  })
})
