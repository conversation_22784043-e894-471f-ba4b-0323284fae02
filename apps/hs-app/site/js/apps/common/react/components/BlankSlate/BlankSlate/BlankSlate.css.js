import Button from 'hsds/components/button'
import Icon from 'hsds/components/icon'
import Image from 'hsds/components/image'
import Page from 'hsds/components/page'
import { getColor } from 'hsds/utils/color'
import styled from 'styled-components'

export const ButtonUI = styled(Button)`
  margin-right: 20px;
`

export const ExampleImageUI = styled(Image)`
  bottom: 0;
  position: absolute;
  right: 40px;

  @media (max-width: ${props => props.mediaMaxWidth || '880px'}) {
    display: none;
  }
`

export const PageUI = styled(Page)`
  --hsds-page-max-width: 900px;

  .c-PageCard {
    min-height: 480px;
    padding: 4px;
    position: relative;
  }

  .c-PageSection {
    background-image: url(${hsGlobal.imagePath}blank-slate/brush-stroke.png);
    background-position: right -15px top;
    background-repeat: no-repeat;
    padding: 100px 50px;
    width: auto;
  }

  .c-PageContent,
  header {
    max-width: 330px;
  }

  header {
    border-bottom: 0 !important;
  }

  .c-Heading.is-md {
    font-size: 22px;
    margin-bottom: 8px;
  }

  .c-PageHeader__subtitle,
  .c-PageHeader__subtitle > .is-13 {
    font-size: 14px;
    line-height: 22px;
  }

  .c-PageHeader.is-withBottomMargin {
    margin-bottom: 0;
  }
`

export const ListUI = styled('ul')`
  font-size: 14px;
  list-style: none;
  margin-bottom: 32px;
  margin-left: 0;
  margin-top: 5px;
`

export const ListItemUI = styled('li')`
  line-height: 21px;
  margin: 0;
  padding: 0;
`

export const ListIconUI = styled(Icon)`
  color: ${getColor('green.500')};
  display: inline-block;
  left: -7px;
  position: relative;
  top: 8px;
  height: 26px !important;
  width: 26px !important;
`
