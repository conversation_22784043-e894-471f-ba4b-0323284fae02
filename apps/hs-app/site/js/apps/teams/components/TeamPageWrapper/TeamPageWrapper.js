import { discardChanges } from '../../state/teams/actions'
import { deleteTeam } from '../../state/teams/operations'
import {
  getIsDeleting,
  getIsDirty,
  getIsLoading,
  getSelectedTeam,
} from '../../state/teams/selectors'
import ConfirmDeleteTeamModal from '../ConfirmDeleteTeamModal'
import Header from './Header'
import { PageCardUI, PageUI } from './TeamPageWrapper.css'
import Button from 'hsds/components/button'
import Page from 'hsds/components/page'
import PropTypes from 'prop-types'
import React, { useState } from 'react'
import { connect } from 'react-redux'
import { withRouter } from 'react-router'
import { useMediaQuery } from 'shared/hooks/useMediaQuery'

function TeamPageWrapper({
  children,
  deleteTeam,
  discardChanges,
  history,
  isDeleting,
  isDirty,
  isLoading,
  isSaving,
  onSave = () => {},
  showActions = true,
  teamData,
}) {
  const [isModalOpen, setModalOpen] = useState(false)
  const isNarrow = useMediaQuery(`(max-width: 1600px)`)

  function onDeleteClick() {
    setModalOpen(true)
  }

  function onDeleteCancel() {
    setModalOpen(false)
  }

  function onDeleteConfirm() {
    deleteTeam(teamData.id, { history })
  }

  return (
    <PageUI>
      <PageCardUI>
        <Header
          avatarUrl={teamData.photo}
          initials={teamData.initials}
          isLoading={isLoading}
          name={teamData.name}
        />
        <Page.Section>
          <Page.Content>{children}</Page.Content>
        </Page.Section>
      </PageCardUI>
      {showActions && (
        <Page.Actions
          primary={
            <Button
              size={isNarrow ? 'xl' : 'xxl'}
              color="blue"
              disabled={!isDirty || isSaving}
              onClick={onSave}
            >
              {isSaving ? 'Saving...' : 'Save'}
            </Button>
          }
          secondary={
            isDirty && !isSaving ? (
              <Button size="lg" linked color="grey" onClick={discardChanges}>
                Discard changes
              </Button>
            ) : null
          }
          serious={
            <Button
              size="lg"
              color="red"
              linked
              onClick={onDeleteClick}
              disabled={isSaving}
            >
              Delete team
            </Button>
          }
        />
      )}
      <ConfirmDeleteTeamModal
        isDeleting={isDeleting}
        isOpen={isModalOpen}
        onCancel={onDeleteCancel}
        onConfirm={onDeleteConfirm}
      />
    </PageUI>
  )
}

TeamPageWrapper.propTypes = {
  children: PropTypes.node,
  deleteTeam: PropTypes.func.isRequired,
  discardChanges: PropTypes.func.isRequired,
  history: PropTypes.shape({
    replace: PropTypes.func.isRequired,
  }).isRequired,
  isDeleting: PropTypes.bool.isRequired,
  isDirty: PropTypes.bool.isRequired,
  isLoading: PropTypes.bool.isRequired,
  isSaving: PropTypes.bool,
  onSave: PropTypes.func,
  showActions: PropTypes.bool,
  teamData: PropTypes.shape({
    id: PropTypes.number,
    initials: PropTypes.string,
    name: PropTypes.string,
    photo: PropTypes.string,
  }),
}

const mapStateToProps = state => ({
  isDeleting: getIsDeleting(state),
  isDirty: getIsDirty(state),
  isLoading: getIsLoading(state),
  teamData: getSelectedTeam(state),
})

const mapDispatchToProps = {
  deleteTeam,
  discardChanges,
}

export default connect(
  mapStateToProps,
  mapDispatchToProps
)(withRouter(TeamPageWrapper))
