import NoResults from '../NoResults'
import SkeletonTable from '../SkeletonTable'
import { TextUI } from './Table.css'
import Avatar from 'hsds/components/avatar'
import Flexy from 'hsds/components/flexy'
import Table from 'hsds/components/table'
import PropTypes from 'prop-types'
import React from 'react'

function makeColumns(onRowKeyDown) {
  return [
    {
      title: 'Team Name',
      columnKey: ['name', 'photo', 'id'],
      width: '80%',
      renderCell: function (team) {
        const { name, photo } = team
        const initials = (name && name[0]) || ''
        return (
          <Flexy gap="lg">
            <Flexy.Item>
              <Avatar
                image={photo && photo.url}
                name={name}
                initials={initials}
                size="smmd"
              />
            </Flexy.Item>
            <Flexy.Block>
              <TextUI
                size="14"
                weight={500}
                tabIndex="0"
                onKeyDown={event => onRowKeyDown(event, team)}
              >
                {name}
              </TextUI>
            </Flexy.Block>
          </Flexy>
        )
      },
    },
    { title: 'Members', columnKey: 'membersCount', width: '20%' },
  ]
}

function TableComponent({ data, isLoading, query, onTableRowClick }) {
  function onRowClick(_, team) {
    onTableRowClick(team.id)
  }

  function onRowKeyDown(event, team) {
    if (event && event.keyCode === 13) {
      onTableRowClick(team.id)
    }
  }

  if (isLoading) {
    return <SkeletonTable />
  }

  if (!isLoading && data.length === 0) {
    return <NoResults query={query} />
  }

  const columns = makeColumns(onRowKeyDown)

  return (
    <Table
      data-testid="TeamsTable"
      withTallRows
      columns={columns}
      data={data}
      onRowClick={onRowClick}
      tableWidth={{ min: '500px' }}
    />
  )
}

TableComponent.propTypes = {
  data: PropTypes.array.isRequired,
  isLoading: PropTypes.bool,
  query: PropTypes.string.isRequired,
  onTableRowClick: PropTypes.func.isRequired,
}

export default TableComponent
