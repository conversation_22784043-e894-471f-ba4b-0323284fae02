import { setIsLoading } from '../../state/teams/actions'
import { createTeam, fetchTeams } from '../../state/teams/operations'
import useFilteredItems from '../../utils/useFilteredItems'
import NewTeamModal from '../NewTeamModal'
import TeamsBlankSlate from '../TeamsBlankSlate'
import Header from './Header'
import Table from './Table'
import { PageUI } from './TeamsList.css'
import Page from 'hsds/components/page'
import PropTypes from 'prop-types'
import React, { Fragment, useEffect, useState } from 'react'
import { connect } from 'react-redux'
import { withRouter } from 'react-router'

function TeamsList({
  createTeam,
  fetchTeams,
  history,
  isLoading,
  setIsLoading,
  teams,
}) {
  const { data: allTeams, mailboxes } = teams
  const [isModalOpen, setModalOpen] = useState(false)
  const { searchBarProps, filteredItems } = useFilteredItems(allTeams)

  useEffect(() => {
    fetchTeams()
  }, [fetchTeams])

  function onTableRowClick(id) {
    setIsLoading(true)
    history.push(`/${id}`)
  }

  function onCreateTeamClick() {
    setModalOpen(true)
  }

  function onCreateTeamCancel() {
    setModalOpen(false)
  }

  function onCreateTeamConfirm(name, image, permissions) {
    return createTeam(name, image, permissions).then(({ id }) => {
      setModalOpen(false)
      setTimeout(() => history.push(`/${id}`), 100)
    })
  }

  const newTeamModal = (
    <NewTeamModal
      isOpen={isModalOpen}
      mailboxes={mailboxes}
      onCancel={onCreateTeamCancel}
      onConfirm={onCreateTeamConfirm}
    />
  )

  if (!isLoading && allTeams && allTeams.length === 0) {
    return (
      <Fragment>
        <TeamsBlankSlate onCallToActionClick={onCreateTeamClick} />
        {newTeamModal}
      </Fragment>
    )
  }

  return (
    <PageUI fullPage={true}>
      <Page.Card>
        <Page.Section>
          <Page.Content>
            <Header
              count={allTeams.length}
              isLoading={isLoading}
              searchBarProps={searchBarProps}
              onCreateTeamClick={onCreateTeamClick}
            />
            <Table
              data={filteredItems}
              isLoading={isLoading}
              query={searchBarProps.value}
              onTableRowClick={onTableRowClick}
              tableDescription="Team members"
            />
            {newTeamModal}
          </Page.Content>
        </Page.Section>
      </Page.Card>
    </PageUI>
  )
}

TeamsList.propTypes = {
  createTeam: PropTypes.func.isRequired,
  fetchTeams: PropTypes.func.isRequired,
  history: PropTypes.shape({
    push: PropTypes.func.isRequired,
  }).isRequired,
  setIsLoading: PropTypes.func.isRequired,
  isLoading: PropTypes.bool,
  teams: PropTypes.shape({
    data: PropTypes.array.isRequired,
    mailboxes: PropTypes.array.isRequired,
  }).isRequired,
}

const mapStateToProps = ({ teams }) => {
  return {
    teams,
    isLoading: teams.isLoading || teams.isInitialLoad,
  }
}

const mapDispatchToProps = {
  createTeam,
  fetchTeams,
  setIsLoading,
}

export default connect(
  mapStateToProps,
  mapDispatchToProps
)(withRouter(TeamsList))
