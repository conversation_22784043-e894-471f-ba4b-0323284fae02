import TeamsList from '../../components/TeamsList'
import TeamsPlusPromo from '../../components/TeamsPlusPromo'
import PropTypes from 'prop-types'
import React from 'react'
import { connect } from 'react-redux'

function HomeView({ showTeamsPlusPromo }) {
  if (showTeamsPlusPromo) {
    return <TeamsPlusPromo />
  }

  return <TeamsList />
}

HomeView.propTypes = {
  showTeamsPlusPromo: PropTypes.bool,
}

const mapStateToProps = ({ teams: { showTeamsPlusPromo } }) => {
  return {
    showTeamsPlusPromo,
  }
}

export default connect(mapStateToProps)(HomeView)
