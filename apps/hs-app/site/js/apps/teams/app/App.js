import * as reducers from '../state'
import AppRouter from './AppRouter'
import { configureStore } from '@common/redux/utils'
import { hsApiClient, useRedirectInterceptor } from '@common/utils/clients'
import PropTypes from 'prop-types'
import React from 'react'
import { Provider as ReduxProvider } from 'react-redux'
import HsApp from 'shared/components/HsApp'

export const App = ({
  basename,
  fileUploader,
  flashError = () => {},
  flashSuccess = () => {},
  flashWarning = () => {},
  initialState = {},
}) => {
  const apiClient = hsApiClient()
  const apiClientV1 = hsApiClient('/api/v1/')
  useRedirectInterceptor(apiClient)
  useRedirectInterceptor(apiClientV1)
  const store = configureStore(reducers, initialState, {
    apiClient,
    apiClientV1,
    fileUploader,
    flashError,
    flashSuccess,
    flashWarning,
  })
  return (
    <ReduxProvider store={store}>
      <HsApp isFullscreen={false}>
        <AppRouter basename={basename} />
      </HsApp>
    </ReduxProvider>
  )
}

App.propTypes = {
  basename: PropTypes.string.isRequired,
  fileUploader: PropTypes.func.isRequired,
  flashError: PropTypes.func.isRequired,
  flashSuccess: PropTypes.func.isRequired,
  flashWarning: PropTypes.func.isRequired,
  initialState: PropTypes.object.isRequired,
}

export default App
