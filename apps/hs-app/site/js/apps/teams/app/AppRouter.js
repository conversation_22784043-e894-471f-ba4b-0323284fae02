import HomeView from '../views/HomeView'
import TeamRouter from './TeamRouter'
import PropTypes from 'prop-types'
import React from 'react'
import { BrowserRouter as Router, Switch, Route } from 'react-router-dom'

function AppRouter({ basename }) {
  return (
    <Router basename={basename}>
      <Switch>
        <Route path="/:id">
          <TeamRouter />
        </Route>
        <Route path="/">
          <HomeView />
        </Route>
      </Switch>
    </Router>
  )
}

AppRouter.propTypes = {
  basename: PropTypes.string.isRequired,
}

export default AppRouter
