import { ReportRoutes } from './routes/ReportRoutes'
import { useNavHeightTheme } from '@beacons/react/utils/useNavHeightTheme'
import {
  ContentUI,
  OverviewGridUI,
  PageCardUI,
  PageUI,
} from '@beacons/react/views/BeaconReport/BeaconReport.css'
import { Toolbar } from '@beacons/react/views/BeaconReport/Toolbar/Toolbar'
import { BeaconContext } from '@beacons/react/views/BeaconReport/context/BeaconReportContext'
import { isTestEnv } from '@common/utils/env'
import { Provider as HSDSProvider } from 'hsds/components/hsds'
import Page from 'hsds/components/page'
import PropTypes from 'prop-types'
import React, { useMemo } from 'react'
import { BrowserRouter } from 'react-router-dom'
import { ThemeProvider } from 'styled-components'

export function BeaconReport({ beacon, navigate }) {
  const basename = isTestEnv() ? '/' : `/settings/beacons/${beacon.id}/report`

  const context = useMemo(() => {
    return {
      beacon,
      navigate,
      basename,
    }
  }, [basename, beacon, navigate])

  const theme = useNavHeightTheme(57)

  return (
    <HSDSProvider scope="hsds-react" themeName="default">
      <BrowserRouter basename={basename}>
        <BeaconContext.Provider value={context}>
          <ThemeProvider theme={theme}>
            <OverviewGridUI>
              <Toolbar />
              <ContentUI>
                <PageUI fullPage>
                  <PageCardUI>
                    <Page.Section>
                      <ReportRoutes />
                    </Page.Section>
                  </PageCardUI>
                </PageUI>
              </ContentUI>
            </OverviewGridUI>
          </ThemeProvider>
        </BeaconContext.Provider>
      </BrowserRouter>
    </HSDSProvider>
  )
}

BeaconReport.propTypes = {
  beacon: PropTypes.object,
  navigate: PropTypes.func,
}
