import Page from 'hsds/components/page'
import styled from 'styled-components'

const breakpoints = {
  max: '1000px',
  narrow: '800px',
  fluid: '799px',
}

export const NoBeaconsPageUI = styled(Page)`
  --hsds-page-max-width: 1200px;
`

export const NoBeaconsPageCardUI = styled(Page.Card)`
  && {
    --hsds-page-card-padding-top: var(
      --hsds-token-page-card-padding-default-top
    );
    --hsds-page-card-padding-bottom: var(
      --hsds-token-page-card-padding-narrow-bottom
    );
    --hsds-page-card-padding-side: var(
      --hsds-token-page-card-padding-narrow-side
    );

    @media (max-width: ${breakpoints.fluid}) {
      --hsds-page-card-padding-top: 20px;
      --hsds-page-card-padding-bottom: 20px;
      --hsds-page-card-padding-side: 20px;
    }
  }

  display: flex;
  flex-direction: row;
  gap: 50px;
  justify-content: center;
  align-items: center;

  max-width: 1000px;
  width: 100%;
  margin: 0 auto;
`

export const NoBeaconsDetailsSectionUI = styled(Page.Section)`
  margin-bottom: 0;
  padding: 20px 0px 20px 50px;
  min-height: 500px;

  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;

  a {
    width: 100%;

    &:last-of-type {
      margin-top: 10px;
    }
  }

  @media (min-width: ${breakpoints.narrow}) and (max-width: ${breakpoints.max}) {
    padding: 20px 0px 20px 20px;
    min-height: 450px;
  }

  @media (max-width: ${breakpoints.fluid}) {
    padding: 60px 40px;
    min-height: 450px;
    align-items: flex-start;
  }
`

export const NoBeaconsImageSectionUI = styled(Page.Section)`
  border-radius: 10px;
  height: 100%;

  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;

  img {
    width: 287px;
    height: 500px;
    aspect-ratio: 287/500;
  }

  @media (max-width: ${breakpoints.fluid}) {
    display: none;
  }
`

export const NoBeaconsListUI = styled.ul`
  list-style: none;
  margin: 20px 0 30px 0;

  li {
    display: flex;
    align-items: flex-start;
    gap: 10px;
  }
`
