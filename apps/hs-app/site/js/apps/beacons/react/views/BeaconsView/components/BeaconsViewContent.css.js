import { ReportDateFilterUI } from '@beacons/react/components/ReportDateFilter/ReportDateFilter'
import Button from 'hsds/components/button'
import Page from 'hsds/components/page'
import { getColor } from 'hsds/utils/color'
import styled from 'styled-components'

export const PageUI = styled(Page)``

export const HeaderUI = styled.div`
  display: flex;
  align-items: flex-start;
  flex-direction: column;

  gap: 30px;

  color: ${getColor('charcoal.1000')};
  font-size: 22px;
  font-weight: 500;
  line-height: normal;

  margin-bottom: 20px;
`

export const HeaderRowUI = styled.div`
  display: flex;
  align-items: center;
  justify-content: flex-start;
  width: 100%;

  ${ReportDateFilterUI} {
    margin-left: initial;
  }
`

export const BeaconsHeaderUI = styled.div`
  display: flex;
  align-items: center;
  gap: 8px;
`

export const NewBeaconButtonUI = styled(Button)`
  margin-left: auto;
`
