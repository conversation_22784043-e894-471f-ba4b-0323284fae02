import { BeaconsViewContent } from './components/BeaconsViewContent'
import { useNavHeightTheme } from '@beacons/react/utils/useNavHeightTheme'
import { Provider as HSDSProvider } from 'hsds/components/hsds'
import PropTypes from 'prop-types'
import React from 'react'
import { BrowserRouter } from 'react-router-dom'
import { ThemeProvider } from 'styled-components'

export function BeaconsView({ beacons }) {
  const theme = useNavHeightTheme()
  return (
    <HSDSProvider scope="hsds-react" themeName="default">
      <BrowserRouter>
        <ThemeProvider theme={theme}>
          <BeaconsViewContent beacons={beacons} />
        </ThemeProvider>
      </BrowserRouter>
    </HSDSProvider>
  )
}

BeaconsView.propTypes = {
  beacons: PropTypes.any,
}
