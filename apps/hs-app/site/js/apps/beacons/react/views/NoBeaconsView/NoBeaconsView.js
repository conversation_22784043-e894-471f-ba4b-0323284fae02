import {
  NoBeaconsListUI,
  NoBeaconsPageUI,
  NoBeaconsPageCardUI,
  NoBeaconsImageSectionUI,
  NoBeaconsDetailsSectionUI,
} from './NoBeaconsView.css'
import Button from 'hsds/components/button'
import Heading from 'hsds/components/heading'
import { Provider as HSDSProvider } from 'hsds/components/hsds'
import Icon from 'hsds/components/icon'
import Text from 'hsds/components/text'
import Checkmark from 'hsds/icons/check'
import PropTypes from 'prop-types'
import React from 'react'
import { BrowserRouter } from 'react-router-dom'

const LIST_ITEMS = [
  'Offer support with one click, on the web or in\u2011app',
  'Resolve FAQs automatically with AI Answers',
  'Give customers access to previous conversations',
]

const NoBeaconsView = ({ imagePath }) => {
  return (
    <HSDSProvider scope="hsds-react" themeName="default">
      <BrowserRouter>
        <NoBeaconsPageUI data-cy="noBeaconsPage">
          <NoBeaconsPageCardUI>
            <NoBeaconsDetailsSectionUI>
              <Heading size="h2" as="h1" weight={500}>
                Meet Beacon, your embeddable support hub
              </Heading>
              <NoBeaconsListUI>
                {LIST_ITEMS.map(item => (
                  <li key={item}>
                    <Icon size={24} icon={Checkmark} state="success" />
                    <Text shade="muted" size={14}>
                      {item}
                    </Text>
                  </li>
                ))}
              </NoBeaconsListUI>
              <Button
                data-cy="createBeaconLink"
                size="xxl"
                color="blue"
                href="/settings/beacons/new"
              >
                Create a Beacon
              </Button>
              <Button
                data-cy="devDocsLink"
                target="_blank"
                rel="noopener noreferrer"
                href="https://developer.helpscout.com/beacon-2/"
                linked
                size="xxl"
                color="blue"
              >
                Developer docs
              </Button>
            </NoBeaconsDetailsSectionUI>
            <NoBeaconsImageSectionUI>
              <img
                data-cy="beaconsPromoImage"
                src={`${imagePath}beacons/beacon-promo-purple.png`}
                alt=""
              />
            </NoBeaconsImageSectionUI>
          </NoBeaconsPageCardUI>
        </NoBeaconsPageUI>
      </BrowserRouter>
    </HSDSProvider>
  )
}

NoBeaconsView.propTypes = {
  imagePath: PropTypes.string.isRequired,
}

export default NoBeaconsView
