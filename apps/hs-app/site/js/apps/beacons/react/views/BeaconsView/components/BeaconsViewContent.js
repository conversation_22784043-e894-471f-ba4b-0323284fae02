import { BeaconsTable } from './BeaconsTable/BeaconsTable'
import {
  BeaconsHeaderUI,
  HeaderRowUI,
  HeaderUI,
  NewBeaconButtonUI,
  PageUI,
} from './BeaconsViewContent.css'
import { BeaconsReporting } from '@beacons/react/components/BeaconsReporting/BeaconsReporting'
import { ReportDateFilter } from '@beacons/react/components/ReportDateFilter/ReportDateFilter'
import { useBeaconsStats } from '@beacons/react/reporting/hooks/useBeaconsStats'
import BadgeCount from 'hsds/components/badge-count'
import Page from 'hsds/components/page'
import PropTypes from 'prop-types'
import React from 'react'

export function BeaconsViewContent({ beacons }) {
  function anyBeaconHas(field) {
    return beacons.some(beacon => beacon[field] === true)
  }

  const {
    beaconsStats,
    isLoading,
    selectedFilter,
    setSelectedFilter,
    customDates,
    setCustomDates,
  } = useBeaconsStats()
  return (
    <PageUI fullPage>
      <Page.Card>
        <Page.Section>
          <HeaderUI data-cy="BeaconPageHeading">
            <HeaderRowUI>
              <BeaconsHeaderUI>
                Beacons
                <BadgeCount>{beacons.length}</BadgeCount>
              </BeaconsHeaderUI>
              <NewBeaconButtonUI
                size="lg"
                color="blue"
                data-cy="newBeaconButton"
                href="/settings/beacons/new"
              >
                New Beacon
              </NewBeaconButtonUI>
            </HeaderRowUI>
            <HeaderRowUI>
              <ReportDateFilter
                value={selectedFilter}
                onChange={item => setSelectedFilter(item.value)}
                customDates={customDates}
                onCustomDatesChange={setCustomDates}
              />
            </HeaderRowUI>
          </HeaderUI>

          <BeaconsReporting
            anyBeaconHas={anyBeaconHas}
            beaconsStats={beaconsStats}
            isLoading={isLoading}
          />

          <BeaconsTable
            beacons={beacons}
            beaconsStats={beaconsStats.perBeaconSummary}
            isLoadingStats={isLoading}
            maybeShowResolutionRate={anyBeaconHas('aiAnswersEnabled')}
          />
        </Page.Section>
      </Page.Card>
    </PageUI>
  )
}

BeaconsViewContent.propTypes = {
  beacons: PropTypes.any,
}
