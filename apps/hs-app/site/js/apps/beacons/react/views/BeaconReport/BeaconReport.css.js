import Page from 'hsds/components/page'
import styled from 'styled-components'

export const ContentUI = styled('div')`
  flex: 1;
  height: 100%;
  display: flex;
  position: relative;
  overflow-y: auto;

  width: 100%;
  flex-direction: column;
  align-items: center;

  --overview-side-margin: 60px;
`

export const PageUI = styled(Page)`
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: center;

  min-width: 728px;

  max-width: inherit;

  --hsds-page-min-width: calc(728px - calc(var(--overview-side-margin) * 2));

  .c-Page__Body {
    width: 100%;
  }

  padding: 20px var(--overview-side-margin) 0;

  @media screen and (max-width: 1030px) {
    margin-top: 0;
  }
`

export const PageCardUI = styled(Page.Card)`
  &&& {
    padding-bottom: 80px;

    &:last-child {
      margin-bottom: 40px;
    }
  }
`

export const OverviewGridUI = styled.div`
  display: flex;
  flex-direction: column;
  width: 100%;

  .c-ToolbarWrapper {
    position: unset;
  }

  ${ContentUI} {
    overflow-y: auto;
  }

  && {
  }
`
