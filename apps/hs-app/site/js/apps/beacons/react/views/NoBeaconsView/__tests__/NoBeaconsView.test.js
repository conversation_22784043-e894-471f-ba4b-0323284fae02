import NoBeaconsView from '../NoBeaconsView'
import { render, screen } from '@testing-library/react'
import React from 'react'

describe('<NoBeaconsView />', () => {
  it('renders the Create a Beacon link with the expected href', () => {
    render(<NoBeaconsView />)

    const createBeaconLink = screen.getByRole('link', {
      name: /create a beacon/i,
    })

    expect(createBeaconLink).toBeInTheDocument()
    expect(createBeaconLink).toHaveAttribute('href', '/settings/beacons/new')
  })

  it('renders the Developer docs button with the expected href and target', () => {
    render(<NoBeaconsView />)

    const docsLink = screen.getByRole('link', { name: /developer docs/i })
    expect(docsLink).toBeInTheDocument()
    expect(docsLink).toHaveAttribute(
      'href',
      'https://developer.helpscout.com/beacon-2/'
    )
    expect(docsLink).toHaveAttribute('target', '_blank')
  })

  it('renders the promo image with the correct src and blank alt to mark it as decorative', () => {
    render(<NoBeaconsView imagePath="/test/path/" />)

    const image = screen.getByRole('img')
    expect(image).toHaveAttribute(
      'src',
      '/test/path/beacons/beacon-promo-purple.png'
    )
    expect(image).toHaveAttribute('alt', '')
  })
})
