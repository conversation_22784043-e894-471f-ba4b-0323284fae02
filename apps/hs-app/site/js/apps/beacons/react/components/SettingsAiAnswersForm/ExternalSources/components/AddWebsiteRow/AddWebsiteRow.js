import { useAiAnswersFormContext } from '../../../AiAnswersFormContext'
import { FormUI } from '../../ExternalSources.css'
import { useAddExternalSource } from '../../ExternalSources.hooks'
import {
  CustomListItemUI,
  EmptyListUI,
  ListItemTitleUI,
  ListItemUrlUI,
  menuCSS,
  TogglerUI,
} from './AddWebsiteRow.css'
import { useSuggestionItems } from './useSuggestionItems'
import { WebsitesModal } from '@beacons/react/components/SettingsAiAnswersForm/ExternalSources/components/WebsitesModal/WebsitesModal'
import classNames from 'classnames'
import Button from 'hsds/components/button'
import DropList from 'hsds/components/drop-list'
import Input from 'hsds/components/input'
import Portal from 'hsds/components/portal'
import PropTypes from 'prop-types'
import React, { useRef, useState } from 'react'

function renderListItem({ item, isHighlighted }) {
  const { name, label } = item

  const classes = classNames(isHighlighted && 'is-highlighted')

  return (
    <CustomListItemUI className={classes} as="div">
      <ListItemTitleUI>{name}</ListItemTitleUI>
      <ListItemUrlUI>{label}</ListItemUrlUI>
    </CustomListItemUI>
  )
}

export function AddWebsiteRow({
  close,
  currentSources,
  pageLimit,
  currentPageCount,
}) {
  const { addExternalSource, isAdding } = useAddExternalSource()
  const { handleExternalSourceAdded } = useAiAnswersFormContext()

  const [errorMessage, setErrorMessage] = useState(undefined)
  const [input, setInput] = useState('')

  const inputRef = useRef(null)
  const formRef = useRef(null)
  const shouldSubmit = useRef(false)

  const [modalOpen, setModalOpen] = useState(false)

  const { hasAvailableItems, filteredItems } = useSuggestionItems(
    currentSources,
    input
  )

  const performSubmit = (additionalBody = {}, onSuccess) => {
    if (isAdding) {
      return
    }

    addExternalSource(
      { url: input, ...additionalBody, type: 'public_site' },
      {
        onSuccess: data => {
          handleExternalSourceAdded(data.id)
          onSuccess?.()
          close()
        },
      }
    )
  }

  const handleSubmit = () => {
    if (isAdding) {
      return
    }

    if (!isValidUrl(input)) {
      setErrorMessage('Please enter a valid URL')
      return false
    }

    if (currentSources.some(source => source.url === input)) {
      setErrorMessage('This website is already added')
      return false
    }

    if (!filteredItems.some(source => source.label === input)) {
      if (currentPageCount >= pageLimit) {
        const headerStyles =
          'color:#131B24;text-align: center;font-size: 18px;font-weight: 500;line-height: 21.6px; margin-bottom: 10px;'
        const textStyles =
          'max-width: 320px;color: #314351;text-align: center;font-size: 13px;font-weight: 400;line-height: 20px;'
        const formattedPageLimit = pageLimit.toLocaleString('en-US')
        HS.Plugins.Flash.okMessage(
          `
             <div style="${headerStyles}">You’ve hit your page limit</div>
             <div style="${textStyles}">To add ${input}, remove enough pages to get under the ${formattedPageLimit}-page limit and try again.</div>
          `,
          undefined,
          {
            ok: 'Close',
          }
        )
        return false
      }

      // when new website - open modal for pages specification
      setModalOpen(true)
      return false
    }

    performSubmit()
    return false
  }

  const isValidUrl = url => {
    try {
      const urlWithProtocol =
        !url.startsWith('http://') && !url.startsWith('https://')
          ? `https://${url}`
          : url
      const parsedURL = new URL(urlWithProtocol)
      return /\.[a-zA-Z]{2,}$/.test(parsedURL.hostname)
    } catch {
      return false
    }
  }

  const [open, setOpen] = useState(true)

  const handleKeyDown = e => {
    if (e.key === 'Escape' && !isAdding) {
      e.stopPropagation()
      e.preventDefault()
      close()
    }
    if (e.key === 'Enter' && !isAdding) {
      // just preventing propagation here, it will be handled on key up
      // the reason is that when selecting item in DropList, this gets called before we get value from DropList callback, so need to delay
      e.stopPropagation()
      e.preventDefault()
      shouldSubmit.current = true
    }
  }
  const handleKeyUp = e => {
    if (e.key === 'Enter' && !isAdding) {
      e.stopPropagation()
      e.preventDefault()
      if (shouldSubmit.current) {
        handleSubmit()
      }
      shouldSubmit.current = false
    }
  }

  const handleSelect = selection => {
    setInput(selection.label)
    setErrorMessage(undefined)
    setOpen(false)
    inputRef.current?.focus()
  }

  const handleBlur = e => {
    // do not close if blur within this element
    if (e.target === inputRef.current && e.relatedTarget === formRef.current) {
      return
    }
    setOpen(false)
  }

  const handleInputChange = value => {
    setErrorMessage(undefined)
    setInput(value)
    setOpen(true)
  }

  return (
    <>
      <FormUI tabIndex={-1} onBlur={handleBlur} ref={formRef}>
        <DropList
          variant="combobox"
          isMenuOpen={hasAvailableItems && open}
          items={filteredItems}
          deactivateInputFilterAction
          withCustomComboboxInput
          menuWidth="toggler"
          menuCSS={menuCSS}
          tippyOptions={{ placement: 'bottom-start' }}
          onSelect={handleSelect}
          customEmptyList={
            <EmptyListUI>Add website &quot;{input}&quot;</EmptyListUI>
          }
          closeOnClickOutside={false}
          clearOnSelect={true}
          renderCustomListItem={renderListItem}
          toggler={
            <TogglerUI>
              <Input
                value={input}
                onChange={handleInputChange}
                onFocus={() => setOpen(true)}
                onKeyDown={handleKeyDown}
                onKeyUp={handleKeyUp}
                name="url"
                placeholder="e.g. yoursite.com/subdomain"
                aria-label="Enter the domain"
                type="url"
                state={errorMessage ? 'error' : 'default'}
                errorMessage={errorMessage}
                // eslint-disable-next-line jsx-a11y/no-autofocus
                autoFocus
                disabled={isAdding}
                inputRef={ref => (inputRef.current = ref)}
                action={
                  <>
                    <Button
                      onClick={handleSubmit}
                      color="blue"
                      size="sm"
                      loading={isAdding}
                    >
                      Add
                    </Button>
                    <Button
                      color="blue"
                      outlined
                      size="sm"
                      onClick={close}
                      disabled={isAdding}
                    >
                      Cancel
                    </Button>
                  </>
                }
              />
            </TogglerUI>
          }
        />
      </FormUI>
      {modalOpen ? (
        <Portal>
          <WebsitesModal
            onClose={() => setModalOpen(false)}
            url={input}
            onSubmit={performSubmit}
            isSubmitting={isAdding}
          />
        </Portal>
      ) : null}
    </>
  )
}

AddWebsiteRow.propTypes = {
  close: PropTypes.func,
  currentSources: PropTypes.array,
  pageLimit: PropTypes.number,
  currentPageCount: PropTypes.number,
}
