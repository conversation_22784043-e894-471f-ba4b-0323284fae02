import DeveloperToolsPromo from '../../../DeveloperToolsPromo'
import withResetStyles from '../../../withResetStyles'
import withScrollToTop from '../../../withScrollToTop'
import BeaconId from '../BeaconId'
import { PageCardUI, PageCardInnerWrapperUI } from './IosInstructions.css'
import { SettingsPageUI } from '@beacons/react/components/SettingsPage/SettingsPage'
import compose from '@common/utils/compose'
import CopyCode from 'hsds/components/copy-code'
import FormGroup from 'hsds/components/form-group'
import FormLabel from 'hsds/components/form-label'
import Page from 'hsds/components/page'
import PropTypes from 'prop-types'
import React, { PureComponent as Component } from 'react'

class BaseIosInstructions extends Component {
  static propTypes = {
    beaconId: PropTypes.string,
    onCopy: PropTypes.func,
  }

  static defaultProps = {
    onCopy: () => undefined,
  }

  static displayName = 'IosInstructions'

  getObjCCode() {
    const { beaconId } = this.props

    return `HSBeaconSettings *settings = [[HSBeaconSettings alloc] initWithBeaconId:@"${beaconId}"];\n[HSBeacon openBeacon:settings];`
  }

  getSwiftCode() {
    const { beaconId } = this.props

    return `let settings = HSBeaconSettings(beaconId: "${beaconId}")\nHSBeacon.open(settings)`
  }

  render() {
    const { beaconId, onCopy } = this.props

    return (
      <SettingsPageUI>
        <PageCardUI>
          <PageCardInnerWrapperUI>
            <Page.Section>
              <Page.Header
                render={({ Title }) => <Title>Install Beacon for iOS</Title>}
              />
              <Page.Content>
                <BeaconId
                  beaconId={beaconId}
                  docsUrl="https://developer.helpscout.com/beacon-2/ios/"
                  onCopy={onCopy}
                />

                <FormGroup>
                  <FormLabel label="Swift">
                    <CopyCode
                      code={this.getSwiftCode()}
                      language="swift"
                      onCopy={onCopy}
                      maxWidth={2000}
                    />
                  </FormLabel>
                </FormGroup>

                <FormGroup>
                  <FormLabel label="Objective-C">
                    <CopyCode
                      code={this.getObjCCode()}
                      language="objectivec"
                      onCopy={onCopy}
                      maxWidth={2000}
                    />
                  </FormLabel>
                </FormGroup>
              </Page.Content>
            </Page.Section>
          </PageCardInnerWrapperUI>
          <DeveloperToolsPromo />
        </PageCardUI>
      </SettingsPageUI>
    )
  }
}

const enhance = compose(withResetStyles, withScrollToTop)

export default enhance(BaseIosInstructions)
