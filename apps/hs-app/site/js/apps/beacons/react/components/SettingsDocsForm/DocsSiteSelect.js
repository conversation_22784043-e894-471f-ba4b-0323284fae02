import React, { Component } from 'react'
import PropTypes from 'prop-types'
import { connect } from '@helpscout/brigade'
import DropList, { SelectTag } from 'hsds/components/drop-list'
import FormLabel from 'hsds/components/form-label'
import { FormGroupUI } from './DocsSiteSelect.css'

export class DocsSiteSelect extends Component {
  static defaultProps = {
    disabled: false,
    docsSiteId: '',
    errorMessage: '',
    handleSiteChange: () => {},
    label: 'Docs Site',
    sites: [],
  }

  static propTypes = {
    disabled: PropTypes.bool,
    docsSiteId: PropTypes.string,
    errorMessage: PropTypes.string,
    handleSiteChange: PropTypes.func,
    label: PropTypes.string,
    sites: PropTypes.array,
  }

  render() {
    const {
      disabled,
      docsSiteId,
      errorMessage,
      sites,
      handleSiteChange,
      label,
    } = this.props

    const selectedItem =
      docsSiteId &&
      sites.find(site => {
        return site.value === docsSiteId
      })

    return (
      <FormGroupUI>
        <FormLabel for="docsSiteId" label={label}>
          <DropList
            autoSetComboboxAt={15}
            items={sites}
            onSelect={({ value }) => handleSiteChange(value)}
            selection={selectedItem}
            tippyOptions={{
              appendTo: document.body,
            }}
            toggler={
              <SelectTag
                aria-label="Choose a docs site"
                disabled={disabled}
                error={
                  !errorMessage && errorMessage.length > 0 ? 'error' : null
                }
                id="docsSiteId"
                name="Docs Site"
                text={selectedItem && selectedItem.label}
              />
            }
          />
        </FormLabel>
      </FormGroupUI>
    )
  }
}

const mapStateToProps = ({
  beacon: { docsSiteId },
  viewModel: { sites },
  errors: { docsSiteId: errorMessage },
}) => {
  return {
    docsSiteId,
    errorMessage,
    sites,
  }
}

const mapActionsToProps = store => {
  const { handleSiteChange } = store.getStatelessExternalActions()
  return {
    handleSiteChange,
  }
}

export default connect(mapStateToProps, mapActionsToProps)(DocsSiteSelect)
