import Heading from 'hsds/components/heading'
import Page from 'hsds/components/page'
import { AddImprovementComposer } from 'shared/components/ImprovementComposer/Add/AddImprovementComposer'
import styled from 'styled-components'

export const AiImprovementsUI = styled(Page)`
  &.with-adjusted-width {
    margin-left: var(--hsds-token-spacing-m, -20px);
    margin-right: var(--hsds-token-spacing-m, -20px);
    width: calc(100% + var(--hsds-token-spacing-xl, 40px));
  }

  padding: 35px 40px 40px;
  min-height: fit-content !important;
  background-color: #f8f9f9;
`

export const AiImprovementsPageCardUI = styled(Page.Card)`
  && {
    --hsds-page-card-padding-top: 40px;
    --hsds-page-card-padding-bottom: 60px;
    --hsds-page-card-padding-side: var(
      --hsds-token-page-card-padding-narrow-side
    );
  }

  display: flex;
  gap: 20px;
`

export const AIImprovementsHeaderSectionUI = styled(Page.Section)`
  display: flex;
  flex-direction: row;
  align-items: flex-end;
  gap: 30px;
  justify-content: space-between;
  margin-bottom: 0;
`

export const AiImprovementsListSectionUI = styled(Page.Section)`
  display: flex;
  flex-direction: column;

  ul {
    list-style: none;
    margin: 0;

    li {
      margin: 0 0 10px 0;
    }
  }
`

export const AiImprovementsHeadingTextUI = styled(Heading)`
  margin-bottom: 4px;
`

export const AddImprovementComposerUI = styled(AddImprovementComposer)`
  margin-top: 5px;
  --composerMaxWidth: 100%;
`
