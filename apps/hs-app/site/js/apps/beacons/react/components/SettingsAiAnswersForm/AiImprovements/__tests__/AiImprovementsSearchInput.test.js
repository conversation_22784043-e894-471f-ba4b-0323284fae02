import AiImprovementsSearchInput from '../AiImprovementsSearchInput'
import { AiImprovementsContext } from '../context/AiImprovementsContext'
import { render, screen } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import React from 'react'
import { waitTime } from 'shared/testUtils/async-utils'

const defaultContext = {
  beaconId: '1',
  currentPage: 1,
  currentImprovements: [],
  handlePageChange: jest.fn(),
  handleSearchChange: jest.fn(),
  isError: false,
  isLoading: false,
  refetchExternalSources: jest.fn(),
  searchValue: '',
  setSearchValue: jest.fn(),
}

const renderWithProvider = (contextOverrides = {}) => {
  const contextValue = { ...defaultContext, ...contextOverrides }
  return {
    ...render(
      <AiImprovementsContext.Provider value={contextValue}>
        <AiImprovementsSearchInput />
      </AiImprovementsContext.Provider>
    ),
  }
}

describe('<AiImprovementsSearchInput />', () => {
  it('renders the search input with placeholder text and a search icon', () => {
    renderWithProvider()

    expect(screen.getByPlaceholderText('Search...')).toBeInTheDocument()
    const icon = screen.getByRole('img', { hidden: true })
    expect(icon).toBeInTheDocument()
    expect(icon).toHaveAttribute('data-icon-name', 'search')
  })

  it('shows the clear button when a search value exists', () => {
    renderWithProvider({ searchValue: 'value' })

    expect(
      screen.getByRole('button', { name: /clear search/i })
    ).toBeInTheDocument()
  })

  it('calls onSearchValueChange when clear the clear button is clicked', async () => {
    renderWithProvider({ searchValue: 'value' })

    await userEvent.click(screen.getByRole('button', { name: /clear search/i }))
    expect(defaultContext.handleSearchChange).toHaveBeenCalledWith('')
  })

  it('calls onSearchValueChange when the input value changes', async () => {
    renderWithProvider({
      currentImprovements: [{ id: 1 }],
    })
    const user = userEvent.setup()
    const input = screen.getByRole('textbox')
    await user.type(input, 'new search')
    await waitTime(500) // We need to wait for lodash's debounce to complete

    expect(defaultContext.handleSearchChange).toHaveBeenCalledWith('new search')
  })

  it('is disabled when there are no improvements and no search value', () => {
    renderWithProvider()

    expect(screen.getByRole('textbox')).toBeDisabled()
  })

  it('is disabled when loading', () => {
    renderWithProvider({ isLoading: true })

    expect(screen.getByRole('textbox')).toBeDisabled()
  })

  it('is enabled when there are improvements', () => {
    renderWithProvider({
      currentImprovements: [{ id: 1 }],
    })

    expect(screen.getByRole('textbox')).not.toBeDisabled()
  })

  it('is enabled when there is a search value', () => {
    renderWithProvider({ searchValue: 'value' })

    expect(screen.getByRole('textbox')).not.toBeDisabled()
  })
})
