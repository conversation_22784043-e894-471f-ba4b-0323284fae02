import SettingsAiAnswersFormWrapper from '../SettingsAiAnswersFormWrapper'
import { QueryClient, QueryClientProvider } from '@tanstack/react-query'
import { render, screen } from '@testing-library/react'
import React from 'react'
import { createTrpcClient, TrpcProvider } from 'shared/utils/trpc'

jest.mock('@helpscout/brigade', () => ({
  connect: () => Component => Component,
}))

const defaultProps = {
  beaconId: '123',
  aiAnswersEnabled: true,
  docsSearchEnabled: true,
  isSiteSelected: true,
  onCancel: jest.fn(),
  onSave: jest.fn(),
  handleAiAnswersEnabledChange: jest.fn(),
  voiceAndTonePromptText: '',
  handleVoiceAndToneChange: jest.fn(),
  externalSourceIds: [],
  snippetIds: [],
  handleExternalSourceAdded: jest.fn(),
  handleExternalSourceRemoved: jest.fn(),
  isSaving: false,
}

describe('<SettingsAiAnswersFormWrapper />', () => {
  beforeAll(() => {
    global.XMLHttpRequest = jest.fn().mockImplementation(() => ({
      open: jest.fn(),
      send: jest.fn(),
      setRequestHeader: jest.fn(),
    }))
  })

  afterAll(() => {
    global.XMLHttpRequest = undefined
  })

  beforeEach(() => {
    window.scrollTo = () => undefined
    window.hsGlobal = {
      features: {
        isAiAnswersTidbitsEnabled: false,
      },
      hsAppUiServerUrl: 'http://localhost',
      trpcToken: 'test-token',
    }
  })

  const renderWithProviders = ui => {
    const trpcClient = createTrpcClient()
    const queryClient = new QueryClient()
    return render(
      <TrpcProvider queryClient={queryClient} client={trpcClient}>
        <QueryClientProvider client={queryClient}>{ui}</QueryClientProvider>
      </TrpcProvider>
    )
  }

  it('should render tabbed navigation items when isAiAnswersTidbitsEnabled is true', () => {
    window.hsGlobal.features.isAiAnswersTidbitsEnabled = true
    renderWithProviders(<SettingsAiAnswersFormWrapper {...defaultProps} />)

    expect(
      screen.getByTestId('ai-answers-tabbed-navigation')
    ).toBeInTheDocument()
  })

  it('should not render tabbed navigation items when isAiAnswersTidbitsEnabled is false', () => {
    window.hsGlobal.features.isAiAnswersTidbitsEnabled = false
    renderWithProviders(<SettingsAiAnswersFormWrapper {...defaultProps} />)

    expect(
      screen.queryByTestId('ai-answers-tabbed-navigation')
    ).not.toBeInTheDocument()
  })
})
