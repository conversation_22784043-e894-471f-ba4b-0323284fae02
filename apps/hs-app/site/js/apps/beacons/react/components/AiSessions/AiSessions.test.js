import { AiSessions } from '@beacons/react/components/AiSessions/AiSessions'
import { HS_API_URL } from '@common/utils/clients'
import { render, screen, waitFor } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import MockDate from 'mockdate'
import nock from 'nock'
import React from 'react'
import { MemoryRouter } from 'react-router-dom'

HS.csrfToken = 'secret'

const defaultBeacon = {
  id: '1234',
}

const defaultSessions = {
  resolutions: [
    {
      id: '1234',
      beacon_id: '12345',
      created_at: '1713132000000',
      conversation_preview: 'Test session',
      conversation_id: '1234',
      resolution_type: 'RESOLVED',
    },
  ],
  total: 1,
}

function waitTime(time = 50) {
  return waitFor(() => new Promise(resolve => setTimeout(resolve, time)))
}

describe('AI Sessions table', () => {
  beforeEach(() => {
    nock.cleanAll()

    MockDate.set('2024-07-15T17:42:46Z')
    window.hsGlobal.timezone = 'Europe/Warsaw'

    window.hsGlobal.memberPermissions = {
      exportReportingData: true,
    }
  })

  it('should display header', async () => {
    mockSessions()
    renderComponent()

    await waitForRequestsToFinish()

    expect(screen.getByText(/AI Sessions/)).toBeInTheDocument()
  })

  it('should display enabled Export button when there are sessions', async () => {
    mockSessions()
    renderComponent()

    await waitForRequestsToFinish()

    await waitFor(() => {
      expect(screen.getByRole('button', { name: /Export/ })).toBeEnabled()
    })
  })

  it('should NOT display enabled Export button when no permission to export reports', async () => {
    window.hsGlobal.memberPermissions = {
      exportReportingData: false,
    }
    mockSessions()
    renderComponent()

    await waitForRequestsToFinish()

    await waitFor(() => {
      expect(
        screen.queryByRole('button', { name: /Export/ })
      ).not.toBeInTheDocument()
    })
  })

  it('should display disabled Export button when there are no sessions', async () => {
    mockSessions({ response: { resolutions: [], total: 0 } })
    renderComponent()

    await waitForRequestsToFinish()

    await waitFor(() => {
      expect(screen.getByRole('button', { name: /Export/ })).toBeDisabled()
    })
  })

  it('should display empty state when no sessions', async () => {
    mockSessions({ response: { resolutions: [], total: 0 } })
    renderComponent()

    await waitForRequestsToFinish()

    await waitFor(() =>
      expect(screen.queryByRole('table')).not.toBeInTheDocument()
    )
    expect(
      screen.getByText("We couldn't find any results matching those filters.")
    ).toBeInTheDocument()
  })

  it('should display filters', async () => {
    mockSessions()
    renderComponent()

    await waitForRequestsToFinish()

    expect(
      screen.getByRole('button', { name: 'Filter report by range' })
    ).toBeInTheDocument()
    expect(
      screen.getByRole('button', { name: 'Filter sessions by resolution' })
    ).toBeInTheDocument()
  })

  it('should make a request with proper params', async () => {
    mockSessions({
      params: {
        beaconId: defaultBeacon.id,
        start: '2024-07-08T00:00:00',
        end: '2024-07-14T23:59:59',
        timezone: 'Europe/Warsaw',
        limit: '15',
        offset: '0',
      },
    })
    renderComponent()

    await waitForRequestsToFinish()

    expect(screen.getByText('Test session')).toBeInTheDocument()
  })

  it('should show n/a for first question when not available', async () => {
    mockSessions({
      response: {
        resolutions: [
          {
            id: '1234',
            beacon_id: '12345',
            created_at: '1713132000000',
            resolution_type: 'NO_FEEDBACK',
          },
        ],
        total: 1,
      },
    })
    renderComponent()

    await waitForRequestsToFinish()

    expect(screen.getByText('n/a')).toBeInTheDocument()
  })

  it('should make new request when time filter changed', async () => {
    mockSessions()
    renderComponent()

    await waitForRequestsToFinish()

    mockSessions({
      params: { start: '2024-06-14T00:00:00', end: '2024-07-14T23:59:59' },
    })
    userEvent.click(
      screen.getByRole('button', { name: 'Filter report by range' })
    )
    await waitTime(300)
    userEvent.click(screen.getByRole('option', { name: 'Last 30 days' }))

    await waitForRequestsToFinish()
  })

  it('should make new request when resolution filter changed', async () => {
    mockSessions()
    renderComponent()

    await waitForRequestsToFinish()

    mockSessions({
      params: { resolutionType: 'HUMAN' },
    })
    userEvent.click(
      screen.getByRole('button', { name: 'Filter sessions by resolution' })
    )
    await waitTime()
    userEvent.click(screen.getByRole('option', { name: 'Human escalation' }))

    await waitForRequestsToFinish()
  })
  it('should show escalation badge when resolution type human and escalation type email', async () => {
    mockSessions({
      response: {
        resolutions: [
          {
            id: '1234',
            beacon_id: '12345',
            created_at: '1713132000000',
            resolution_type: 'HUMAN',
            session_end_type: 'EMAIL',
          },
        ],
        total: 1,
      },
    })

    renderComponent()

    await waitForRequestsToFinish()

    await waitFor(() =>
      expect(screen.getByText('Send an email')).toBeInTheDocument()
    )
  })

  it('should show escalation badge when resolution type human and escalation type chat', async () => {
    mockSessions({
      response: {
        resolutions: [
          {
            id: '1234',
            beacon_id: '12345',
            created_at: '1713132000000',
            resolution_type: 'HUMAN',
            session_end_type: 'CHAT',
          },
        ],
        total: 1,
      },
    })

    renderComponent()

    await waitForRequestsToFinish()

    await waitFor(() =>
      expect(
        screen.getByText(content => /Started a chat/.test(content))
      ).toBeInTheDocument()
    )
  })

  it('should show escalation badge when resolution type unresolved and escalation type docs', async () => {
    mockSessions({
      response: {
        resolutions: [
          {
            id: '1234',
            beacon_id: '12345',
            created_at: '1713132000000',
            resolution_type: 'NOT_RESOLVED',
            session_end_type: 'DOCS',
          },
        ],
        total: 1,
      },
    })
    renderComponent()

    await waitForRequestsToFinish()

    await waitFor(() =>
      expect(screen.getByText('Docs search')).toBeInTheDocument()
    )
  })

  it('should show escalation badge when resolution type unresolved and escalation type timeout', async () => {
    mockSessions({
      response: {
        resolutions: [
          {
            id: '1234',
            beacon_id: '12345',
            created_at: '1713132000000',
            resolution_type: 'NOT_RESOLVED',
            session_end_type: 'TIMEOUT',
          },
        ],
        total: 1,
      },
    })
    renderComponent()

    await waitForRequestsToFinish()

    await waitFor(() => {
      expect(screen.getByText('Timeout')).toBeInTheDocument()
    })
  })

  it('should display table with sessions', async () => {
    mockSessions()
    renderComponent()

    await waitForRequestsToFinish()

    expect(screen.getAllByRole('row')).toHaveLength(2) // + header
    expect(screen.getByText('Test session')).toBeInTheDocument()
    expect(screen.getByText(/Contact helped/)).toBeInTheDocument()
    expect(screen.getByText('Apr 15')).toBeInTheDocument()
  })

  it('should display new options in filters', async () => {
    mockSessions()
    renderComponent()

    await waitForRequestsToFinish()

    userEvent.click(
      screen.getByRole('button', { name: 'Filter sessions by resolution' })
    )
    await waitTime(200)
    expect(screen.getAllByRole('option').map(el => el.textContent)).toEqual([
      'Contact helped',
      'Contact not helped',
      'Human escalation',
    ])
  })

  it('should make new request when resolution filter changed', async () => {
    mockSessions()
    renderComponent()

    await waitForRequestsToFinish()

    mockSessions({
      params: { resolutionType: 'RESOLVED' },
    })
    userEvent.click(
      screen.getByRole('button', { name: 'Filter sessions by resolution' })
    )
    await waitTime(200)
    await waitFor(() => {
      userEvent.click(screen.getByRole('option', { name: 'Contact helped' }))
    })

    await waitForRequestsToFinish()
  })
})

function mockSessions({ response = defaultSessions, params } = {}) {
  nock('http://localhost')
    .get(HS_API_URL + 'reports/beacons/resolutions')
    .query(
      params
        ? query =>
            Object.entries(params).every(
              ([param, value]) => query[param] === value
            )
        : true
    )
    .reply(200, response)
}

function renderComponent(beacon = defaultBeacon) {
  render(
    <MemoryRouter>
      <AiSessions beacon={beacon} />
    </MemoryRouter>
  )
}

const waitForRequestsToFinish = async (remainingRequests = 0) => {
  await waitFor(() =>
    expect(nock.pendingMocks()).toHaveLength(remainingRequests)
  )
}
