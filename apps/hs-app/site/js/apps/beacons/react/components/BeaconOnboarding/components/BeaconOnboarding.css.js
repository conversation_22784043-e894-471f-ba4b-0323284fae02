import Animate from 'hsds/components/animate'
import Page from 'hsds/components/page'
import styled, { createGlobalStyle } from 'styled-components'

export const AnimateUI = styled(Animate)`
  max-height: 0px;
  transition: max-height 100ms ease-out;

  &.ax-entered {
    max-height: 100%;
  }
`

export const GlobalStyle = createGlobalStyle`
  .beacon-builder-setup__formWrapper{
    padding:0;
  }
`
export const PageUI = styled(Page)`
  && {
    justify-content: center;
    --hsds-page-max-width: 700px;
  }
`

export const PageCardUI = styled(Page.Card)``

export const PageCardInnerWrapperUI = styled('div')``
