import AiAnswersTabbedNavigation from '../AiAnswersTabbedNavigation'
import { render, screen } from '@testing-library/react'
import React from 'react'
import { BrowserRouter } from 'react-router-dom'

describe('<AiAnswersTabbedNavigation />', () => {
  it('should render navigation links with correct hrefs', () => {
    render(
      <BrowserRouter>
        <AiAnswersTabbedNavigation />
      </BrowserRouter>
    )

    expect(screen.getByRole('link', { name: /configure/i })).toHaveAttribute(
      'href',
      '/configure'
    )
    expect(screen.getByRole('link', { name: /improvements/i })).toHaveAttribute(
      'href',
      '/improvements'
    )
  })
})
