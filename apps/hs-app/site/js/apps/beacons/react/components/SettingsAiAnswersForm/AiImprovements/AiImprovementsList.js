import { useAiAnswersFormContext } from '../AiAnswersFormContext'
import AiImprovementCard from './AiImprovementCard'
import { useAiImprovementsContext } from './context/AiImprovementsContext'
import React from 'react'
import { EditImprovementComposer } from 'shared/components/ImprovementComposer/Edit/EditImprovementComposer'
import { useNoty } from 'shared/hooks'
import styled from 'styled-components'

const AiEditImprovementComposerUI = styled(EditImprovementComposer)`
  margin-top: 5px;
  --composerMaxWidth: 100%;
`

const AiImprovementsList = () => {
  const {
    editId,
    handleEditIdChange,
    currentImprovements,
    handleRefetchSources,
  } = useAiImprovementsContext()
  const { beaconId } = useAiAnswersFormContext()
  const { showErrorNoty } = useNoty()

  const handleSaveError = () => {
    showErrorNoty('Cannot save Improvement. Please try again.')
  }

  const handleDeleteError = () => {
    showErrorNoty('Cannot delete Improvement. Please try again.')
  }

  return (
    <ul>
      {currentImprovements?.map(improvement => (
        <li key={improvement.id}>
          {improvement.id === editId ? (
            <AiEditImprovementComposerUI
              beaconId={beaconId}
              improvement={improvement}
              onSave={handleRefetchSources}
              onSaveError={handleSaveError}
              onDelete={handleRefetchSources}
              onDeleteError={handleDeleteError}
              onClose={() => handleEditIdChange(null)}
            />
          ) : (
            <AiImprovementCard
              handleClick={() => handleEditIdChange(improvement.id)}
              improvement={improvement}
            />
          )}
        </li>
      ))}
    </ul>
  )
}

export default AiImprovementsList
