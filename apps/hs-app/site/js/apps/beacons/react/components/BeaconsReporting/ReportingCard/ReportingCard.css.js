import { getReportingColumnsBreakpoint } from '@beacons/react/components/BeaconsReporting/reporting.common.css'
import { getColor } from 'hsds/utils/color'
import styled, { css } from 'styled-components'

export const ReportingCardDetailsSectionUI = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  row-gap: 12px;
`

const oneLineDetails = css`
  ${ReportingCardDetailsSectionUI} {
    display: flex;
    flex-wrap: wrap;
    justify-content: flex-start;
    column-gap: 20px;
    margin: 0 auto;
  }
`

export const ReportingCardUI = styled.div`
  height: 100%;
  padding: 20px 26px;

  width: 100%;

  position: relative;

  border-radius: 8px;
  border: 1px solid ${getColor('charcoal.300')};
  background: #fff;
  box-shadow: 0px 2px 2px 0px rgba(0, 0, 0, 0.05),
    0px 1px 0px 0px rgba(0, 0, 0, 0.08);

  &:only-child {
    ${oneLineDetails};
  }

  @media (max-width: ${props => getReportingColumnsBreakpoint(props)}) {
    ${oneLineDetails};
  }
`

export const ReportingCardContentUI = styled.div`
  display: flex;
  flex-direction: column;
  gap: 24px;

  position: relative;
  overflow: hidden;
  padding: 10px;
`

export const ReportingCardTitleUI = styled.div`
  color: ${getColor('charcoal.1200')};

  font-size: 16px;
  font-weight: 500;
  line-height: 24px;

  display: flex;
  align-items: center;
  gap: 4px;
`

export const ReportingCardTotalSectionUI = styled.div`
  display: flex;
  flex-direction: column;
  gap: 4px;
`

export const ReportingCardTotalSectionTitleUI = styled.div`
  color: ${getColor('charcoal.600')};
  font-size: 11px;
  font-weight: 500;
  line-height: normal;
  letter-spacing: 0.7px;
  text-transform: uppercase;
`

export const ReportingCardTotalSectionValueUI = styled.div`
  color: ${getColor('charcoal.1200')};
  font-size: 36px;
  font-weight: 400;
  line-height: normal;
`

export const ReportingCardBarSectionUI = styled.div`
  width: 100%;
  height: 15px;
`

export const ReportingCardDetailsSectionElementUI = styled.div`
  &:only-child {
    justify-self: center;
  }
`

export const StatsElementUI = styled.div`
  color: ${getColor('charcoal.1000')};
  font-size: 13px;
  font-weight: 500;
  line-height: normal;

  position: relative;
`

export const StatsElementContentUI = styled.div`
  display: flex;
  align-items: center;
  gap: 10px;

  width: max-content;

  position: relative;

  z-index: 1;
  cursor: default;

  &:hover {
    &::after {
      content: '';
      z-index: -1;
      position: absolute;
      display: inline-block;
      left: -8px;
      right: -8px;
      top: -4px;
      bottom: -4px;
      border-radius: 4px;
      background: ${getColor('charcoal.200')};
    }
  }
`

export const StatsElementDotUI = styled.div`
  width: 14px;
  height: 14px;
  border-radius: 100%;
  border: 2px solid #fff;

  background-color: ${({ $color }) => $color};
`
