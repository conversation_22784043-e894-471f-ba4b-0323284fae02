import { ResolutionFilters } from '@beacons/react/reporting/filters/ResolutionFilters'
import Badge from 'hsds/components/badge'
import React from 'react'
import styled from 'styled-components'

export const escalationLabelMap = {
  EMAIL: 'Send an email',
  CHAT: 'Started a chat',
  DOCS: 'Docs search',
  TIMEOUT: 'Timeout',
}

const BadgeUI = styled(Badge)`
  flex-shrink: 0;
`

export function EscalationBadge({ type, escalation_type }) {
  const showEscalationLabel =
    escalation_type &&
    (type === ResolutionFilters.NOT_RESOLVED ||
      type === ResolutionFilters.HUMAN)
  const escalationLabel = showEscalationLabel
    ? escalationLabelMap[escalation_type]
    : ''

  if (!showEscalationLabel) {
    return null
  }

  return (
    <BadgeUI badgeColor="grey" variant="inverted">
      {escalationLabel}
    </BadgeUI>
  )
}
