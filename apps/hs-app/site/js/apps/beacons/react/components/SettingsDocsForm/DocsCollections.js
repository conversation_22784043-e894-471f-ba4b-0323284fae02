import { connect } from '@helpscout/brigade'
import classNames from 'classnames'
import Animate from 'hsds/components/animate'
import Badge from 'hsds/components/badge'
import DropList, { SelectTag } from 'hsds/components/drop-list'
import {
  ListItemTextUI,
  ListItemUI,
  SelectedBadge,
} from 'hsds/components/drop-list/DropList.styles'
import Tooltip from 'hsds/components/tooltip'
import { truncate } from 'lodash'
import PropTypes from 'prop-types'
import React, { useMemo, useRef } from 'react'
import styled from 'styled-components'

const SelectTagUI = styled(SelectTag)`
  width: 100%;
`

const PrivateCollectionBadgeUI = styled(Badge)`
  margin-left: auto;

  > div {
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
  }
`

const LABEL_TEXT_LENGTH = 45

function renderCustomListItem({
  item,
  isSelected,
  isHighlighted,
  withMultipleSelection,
}) {
  const classes = classNames(
    'DropListItem',
    isSelected && 'is-selected',
    isHighlighted && 'is-highlighted',
    withMultipleSelection && 'with-multiple-selection'
  )
  const tooltipProps = {
    title:
      'Private collections will only be used to source answers. They will not be displayed publicly.',
    placement: 'right',
    animationDelay: 500,
    animationDuration: 500,
    maxWidth: 220,
  }

  return (
    <ListItemUI className={classes} as="div">
      <SelectedBadge isSelected={isSelected} />
      <ListItemTextUI>{item.label}</ListItemTextUI>
      {item.isPrivate ? (
        <Tooltip {...tooltipProps}>
          <PrivateCollectionBadgeUI variant="inverted">
            Private collection
          </PrivateCollectionBadgeUI>
        </Tooltip>
      ) : null}
    </ListItemUI>
  )
}

function useCollections({
  currentDocsSiteCollections,
  aiAnswersEnabled,
  docsLimitCollectionIds,
  docsLimitPrivateCollectionIds,
}) {
  const items = useMemo(
    () =>
      currentDocsSiteCollections
        .filter(collection => aiAnswersEnabled || !collection.isPrivate)
        .map(collection => ({
          ...collection,
          id: collection.value,
        })),
    [aiAnswersEnabled, currentDocsSiteCollections]
  )

  const selection = useMemo(
    () =>
      items.filter(
        item =>
          docsLimitCollectionIds?.find(id => item.id === id) ||
          docsLimitPrivateCollectionIds?.find(id => item.id === id)
      ),
    [items, docsLimitCollectionIds, docsLimitPrivateCollectionIds]
  )

  // Truncates the Docs collections list for the label if it's too long
  const activeLabelText = useMemo(
    () =>
      [...selection].reduce((accumulator, { label }, currentIndex, array) => {
        if (accumulator.length + label.length >= LABEL_TEXT_LENGTH) {
          const remainingItemsCount = selection.length - currentIndex
          // Truncate the name if it's the first item and it's really long
          if (currentIndex === 0) {
            return `${truncate(label, { length: LABEL_TEXT_LENGTH - 4 })}`
          }

          // Stop reducing as we've reached the limit
          array.splice(currentIndex)

          return `${accumulator}, and ${remainingItemsCount} more`
        }

        return `${accumulator}${accumulator ? ', ' : ''}${label}`
      }, ''),
    [selection]
  )

  const togglerRef = useRef(null)

  return { items, selection, activeLabelText, togglerRef }
}

export function DocsCollections(props) {
  const { items, selection, activeLabelText, togglerRef } =
    useCollections(props)

  return (
    <Animate sequence="fade">
      <DropList
        menuAriaLabel="Limit to collections"
        toggler={
          <SelectTagUI
            ref={togglerRef}
            aria-label="Select collections"
            text={activeLabelText || 'Select collections'}
          />
        }
        menuWidth="toggler"
        selection={selection}
        onSelect={selected => props.handleCollectionChange(selected)}
        items={items}
        withMultipleSelection
        closeOnSelection={false}
        renderCustomListItem={renderCustomListItem}
        tippyOptions={{
          appendTo: document.body,
        }}
      />
    </Animate>
  )
}

DocsCollections.propTypes = {
  currentDocsSiteCollections: PropTypes.array,
  docsLimitCollectionIds: PropTypes.array,
  docsLimitPrivateCollectionIds: PropTypes.array,
  aiAnswersEnabled: PropTypes.bool,
  handleCollectionChange: PropTypes.func,
}

DocsCollections.defaultProps = {
  currentDocsSiteCollections: [],
  docsLimitCollectionIds: [],
  docsLimitPrivateCollectionIds: [],
  handleCollectionChange: () => {},
}

const mapStateToProps = ({
  beacon: {
    docsLimitCollectionIds,
    docsLimitPrivateCollectionIds,
    aiAnswersEnabled,
  },
  viewModel: { currentDocsSiteCollections = [] },
}) => {
  return {
    currentDocsSiteCollections,
    docsLimitCollectionIds,
    docsLimitPrivateCollectionIds,
    aiAnswersEnabled,
  }
}

const mapActionsToProps = store => {
  const { handleCollectionChange } = store.getStatelessExternalActions()
  return {
    handleCollectionChange,
  }
}

export default connect(mapStateToProps, mapActionsToProps)(DocsCollections)
