import TabBar from 'hsds/components/tab-bar'
import React from 'react'
import styled from 'styled-components'

export const TabBarUI = styled(TabBar)`
  margin-left: -20px;
  margin-right: -20px;
`

const TABS = [
  { route: '/configure', label: 'Configure' },
  { route: '/improvements', label: 'Improvements' },
]

const AiAnswersTabbedNavigation = () => {
  return (
    <TabBarUI
      align="center"
      variant="border-bottom"
      data-testid="ai-answers-tabbed-navigation"
    >
      {TABS.map(({ route, label }) => (
        <TabBar.Item key={label} to={route} withReactRouter data-bypass={false}>
          {label}
        </TabBar.Item>
      ))}
    </TabBarUI>
  )
}

export default AiAnswersTabbedNavigation
