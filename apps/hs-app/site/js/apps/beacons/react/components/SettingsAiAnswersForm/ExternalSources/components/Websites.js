import {
  AddItemUI,
  AddWebsiteButtonUI,
  AddWithExistingButtonUI,
  SourcesContainerUI,
  SourcesHeaderTitleUI,
  SourcesHeaderUI,
  SourcesListUI,
  UsageContainerUI,
  UsageProgressBarContainerUI,
  UsageProgressBarFillUI,
  UsageProgressBarUI,
  UsageTextUI,
} from '../ExternalSources.css'
import { AddWebsiteRow } from './AddWebsiteRow/AddWebsiteRow'
import { WebsiteItem } from './WebsiteItem'
import { EditWebsiteModal } from './WebsitesModal/EditWebsiteModal'
import PropTypes from 'prop-types'
import React, { useState } from 'react'

export function Websites({ data }) {
  const [isAddingWebsite, setIsAddingWebsite] = useState(false)

  if (data?.sources?.length === 0 && !isAddingWebsite) {
    return (
      <>
        <AddWebsiteButtonUI
          size="lg"
          outlined
          color="blue"
          onClick={() => setIsAddingWebsite(true)}
        >
          Add website
        </AddWebsiteButtonUI>
      </>
    )
  }

  return (
    <SourcesContainerUI>
      <SourcesHeaderUI>
        <SourcesHeaderTitleUI>Websites</SourcesHeaderTitleUI>
        <div>
          Let AI Answers reference public websites. Syncing can take up to 24
          hours.
        </div>
      </SourcesHeaderUI>
      <UsageContainerUI>
        <div>Account Usage</div>
        <UsageProgressBarContainerUI>
          <UsageProgressBarUI>
            <UsageProgressBarFillUI
              data-testid="usage-progress-bar-fill"
              style={{
                '--usage-progress': `${Math.min(
                  (data.companyPageCount / data.companyPageLimit) * 100,
                  100
                )}%`,
              }}
            />
          </UsageProgressBarUI>
          <UsageTextUI>
            {data.companyPageCount.toLocaleString('en-US')} of{' '}
            {data.companyPageLimit.toLocaleString('en-US')} pages
          </UsageTextUI>
        </UsageProgressBarContainerUI>
      </UsageContainerUI>
      <SourcesListUI>
        {data?.sources?.map(source => (
          <WebsiteItem key={source.id} source={source} />
        ))}
        <AddItemUI>
          {isAddingWebsite ? (
            <AddWebsiteRow
              close={() => setIsAddingWebsite(false)}
              currentSources={data?.sources}
              pageLimit={data.companyPageLimit}
              currentPageCount={data.companyPageCount}
            />
          ) : (
            <AddWithExistingButtonUI
              size="sm"
              outlined
              color="blue"
              onClick={() => setIsAddingWebsite(true)}
            >
              Add
            </AddWithExistingButtonUI>
          )}
        </AddItemUI>
      </SourcesListUI>
      <EditWebsiteModal />
    </SourcesContainerUI>
  )
}

Websites.propTypes = {
  data: PropTypes.object,
}
