import { useAiAnswersFormContext } from '@beacons/react/components/SettingsAiAnswersForm/AiAnswersFormContext'
import { useResyncExternalSource } from '@beacons/react/components/SettingsAiAnswersForm/ExternalSources/ExternalSources.hooks'
import { SIZE_SM } from 'hsds/components/button'
import DropList, { MeatButton } from 'hsds/components/drop-list'
import PropTypes from 'prop-types'
import React from 'react'
import styled from 'styled-components'

const MeatButtonUI = styled(MeatButton)`
  margin-left: 6px;

  flex-shrink: 0;
`

const DELETE_ACTION = 'delete'
const EDIT_ACTION = 'edit'
const RESYNC_ACTION = 'resync'

export function WebsiteActions({ source }) {
  const { handleExternalSourceRemoved } = useAiAnswersFormContext()
  const { resyncExternalSource, isResyncing } = useResyncExternalSource()

  function onListItemSelectEvent({ event }) {
    event.stopPropagation()
  }

  function onSelect(option) {
    if (option.value === RESYNC_ACTION) {
      resyncExternalSource(source.id)
    }

    if (option.value === DELETE_ACTION) {
      HS.Utils.Main.confirmDelete(
        'Delete this website from this Beacon?',
        () => {
          handleExternalSourceRemoved(source.id, source.type)
        }
      )
    }

    if (option.value === EDIT_ACTION) {
      document.dispatchEvent(
        new CustomEvent('open-edit-website-modal', { detail: { source } })
      )
    }
  }

  const items = [
    ...(source.syncStatus === 'in_progress'
      ? []
      : [
          {
            label: 'Re-sync',
            value: RESYNC_ACTION,
          },
          {
            label: 'Edit',
            value: EDIT_ACTION,
          },
        ]),
    {
      label: 'Delete',
      value: DELETE_ACTION,
    },
  ]

  return (
    <DropList
      clearOnSelect
      menuWidth="small"
      toggler={
        <MeatButtonUI aria-label="Website actions" size={SIZE_SM} seamless />
      }
      onListItemSelectEvent={onListItemSelectEvent}
      onSelect={onSelect}
      disabled={isResyncing}
      items={items}
    />
  )
}

WebsiteActions.propTypes = {
  source: PropTypes.object,
}
