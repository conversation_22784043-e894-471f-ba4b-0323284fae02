import Fieldset from '../Fieldset'
import { useAiAnswersFormContext } from './AiAnswersFormContext'
import { AiAnswers } from './components/AiAnswers'
import { VoiceAndTone } from './components/VoiceAndTone'
import { SettingActionsContent } from '@beacons/react/components/SettingsActions/SettingActionsContent'
import { ExternalSources } from '@beacons/react/components/SettingsAiAnswersForm/ExternalSources/ExternalSources'
import { SettingsPageUI } from '@beacons/react/components/SettingsPage/SettingsPage'
import SuggestedQuestions from '@beacons/react/components/SuggestedQuestions/SuggestedQuestions'
import React from 'react'
import { trpc } from 'shared/utils/trpc'

function Actions() {
  const { isSaving = false, onCancel } = useAiAnswersFormContext()

  return (
    <SettingActionsContent
      canSave={true}
      isSaving={isSaving}
      onCancel={onCancel}
      canDelete={false}
    />
  )
}

export function SettingsAiAnswersForm() {
  const {
    isSaving = false,
    onSave,
    isAiAnswersTidbitsEnabled,
  } = useAiAnswersFormContext()
  const trpcUtils = trpc.useUtils()
  const handleSubmit = event => {
    event.preventDefault()
    onSave(undefined, () => {
      // refetching external sources after successful safe
      // this is done to make sure, in case of removing some source, that it's no longer available to select
      // we cannot do this just by looking at the data on UI, because source might be used in different place and won't be removed in this case
      trpcUtils.externalSources.getExternalSources.invalidate()
    })
  }

  return (
    <form onSubmit={handleSubmit} noValidate>
      <SettingsPageUI
        style={{
          ...(isAiAnswersTidbitsEnabled && {
            marginTop: '50px',
            backgroundColor: '#F8F9F9',
          }),
        }}
        className="with-adjusted-width"
      >
        <Fieldset disabled={isSaving}>
          <AiAnswers />
          <ExternalSources />
          <VoiceAndTone />
          <SuggestedQuestions />
          <Actions />
        </Fieldset>
      </SettingsPageUI>
    </form>
  )
}
