import AiImprovementCard from '../AiImprovementCard'
import { render, screen } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import moment from 'moment-timezone'
import React from 'react'

const mockTimezone = 'America/New_York'
const FIXED_DATE = '2024-03-20T10:00:00Z'

const createImprovement = (overrides = {}) => ({
  id: '1',
  name: 'Test Improvement',
  text: 'Test Description',
  createdAt: '2024-03-20T10:00:00Z',
  ...overrides,
})

const setup = (overrides = {}) => {
  const user = userEvent.setup({ delay: null })
  const handleClick = jest.fn()
  const improvement = createImprovement(overrides)

  render(
    <AiImprovementCard improvement={improvement} handleClick={handleClick} />
  )

  return {
    user,
    handleClick,
    improvement,
  }
}

describe('<AiImprovementCard />', () => {
  beforeEach(() => {
    window.hsGlobal = { timezone: mockTimezone }
    jest.useFakeTimers()
    jest.setSystemTime(new Date(FIXED_DATE))
  })

  afterEach(() => {
    jest.restoreAllMocks()
    jest.useRealTimers()
  })

  it('renders an improvement card with expected information', () => {
    const { improvement } = setup()

    expect(screen.getByText(improvement.name)).toBeInTheDocument()
    expect(screen.getByText(improvement.text)).toBeInTheDocument()
  })

  it('shows "Created Just Now" for improvements that were just created', () => {
    setup({ createdAt: FIXED_DATE })

    expect(screen.getByText('Created Just Now')).toBeInTheDocument()
  })

  it('shows "Created x hr" for improvements created less than a day ago', () => {
    setup({
      createdAt: moment(FIXED_DATE).subtract(1, 'hour').toISOString(),
    })

    expect(screen.getByText('Created 1 hr')).toBeInTheDocument()
  })

  it('shows "Created X days" for improvements within 7 days', () => {
    setup({
      createdAt: moment(FIXED_DATE).subtract(3, 'days').toISOString(),
    })

    expect(screen.getByText('Created 3 days')).toBeInTheDocument()
  })

  it('shows date format for improvements older than a year', () => {
    setup({
      createdAt: '2023-03-10T10:00:00Z',
    })

    expect(screen.getByText('Created Mar 10, 2023')).toBeInTheDocument()
  })

  it('shows "Updated" status when updatedBy is present', () => {
    setup({
      createdAt: '2024-03-10T10:00:00Z',
      syncedAt: moment(FIXED_DATE).subtract(1, 'day').toISOString(),
      updatedBy: 123,
    })

    expect(screen.getByText('Updated 1 day')).toBeInTheDocument()
  })

  it('returns empty string when createdAt is missing', () => {
    setup({ createdAt: undefined })

    expect(screen.queryByText(/Created|Updated/)).not.toBeInTheDocument()
  })

  it('returns empty string when timestamp is missing for updated improvement', () => {
    setup({
      updatedBy: 123,
      syncedAt: undefined,
    })

    expect(screen.queryByText(/Created|Updated/)).not.toBeInTheDocument()
  })

  it('calls handleClick when clicking on the card', async () => {
    const { user, handleClick, improvement } = setup()

    await user.click(screen.getByRole('button', { name: improvement.name }))

    expect(handleClick).toHaveBeenCalledTimes(1)
  })

  it('calls handleClick when pressing the enter key', async () => {
    const { user, handleClick } = setup()

    await user.tab()
    await user.keyboard('{enter}')

    expect(handleClick).toHaveBeenCalledTimes(1)
  })

  it('calls handleClick when pressing the space key', async () => {
    const { user, handleClick } = setup()

    await user.tab()
    await user.keyboard(' ')

    expect(handleClick).toHaveBeenCalledTimes(1)
  })
})
