import { useAiImprovementsContext } from './context/AiImprovementsContext'
import ButtonIcon from 'hsds/components/button-icon'
import Icon from 'hsds/components/icon'
import Input from 'hsds/components/input'
import Circle<PERSON>ross from 'hsds/icons/cross-circle'
import SearchMedium from 'hsds/icons/search'
import debounce from 'lodash.debounce'
import PropTypes from 'prop-types'
import React, { useMemo } from 'react'
import styled from 'styled-components'

const SearchInputClearButtonUI = styled(ButtonIcon)`
  color: var(--hsds-token-color-charcoal-600);
`

const AiImprovementsSearchInput = () => {
  const { isLoading, currentImprovements, searchValue, handleSearchChange } =
    useAiImprovementsContext()

  const isDisabled = (!currentImprovements?.length && !searchValue) || isLoading

  const debouncedSearch = useMemo(
    () => debounce(value => handleSearchChange(value), 300),
    [handleSearchChange]
  )

  return (
    <Input
      aria-label="Search improvements"
      autocomplete="off"
      disabled={isDisabled}
      inlineSuffix={
        searchValue ? (
          <SearchInputClearButtonUI
            seamless
            size="sm"
            icon={CircleCross}
            aria-label="Clear search"
            onClick={() => handleSearchChange('')}
          />
        ) : (
          <Icon icon={SearchMedium} size="24" />
        )
      }
      onChange={debouncedSearch}
      name="search-improvements"
      placeholder="Search..."
      value={searchValue}
    />
  )
}

AiImprovementsSearchInput.propTypes = {
  isLoading: PropTypes.bool,
  searchValue: PropTypes.string,
  onSearchValueChange: PropTypes.func,
  currentImprovements: PropTypes.array,
}

export default AiImprovementsSearchInput
