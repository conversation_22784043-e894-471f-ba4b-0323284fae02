import DeveloperToolsPromo from '../../DeveloperToolsPromo'
import {
  AnimateUI,
  GlobalStyle,
  PageCardInnerWrapperUI,
  PageCardUI,
  PageUI,
} from './BeaconOnboarding.css'
import Step1 from './Steps/Step1'
import Step2 from './Steps/Step2'
import Step3 from './Steps/Step3'
import Step4 from './Steps/Step4'
import Wizard from './Wizard'
import { getCurrentTheme } from '@common/utils/theme'
import Button from 'hsds/components/button'
import { Provider as HSDSProvider } from 'hsds/components/hsds'
import Page from 'hsds/components/page'
import PropTypes from 'prop-types'
import React, { Component } from 'react'
import { ThemeProvider } from 'styled-components'

class BeaconOnboarding extends Component {
  state = {
    beaconId: null,
    currentStep: 1,
    maxSteps: 4,
  }

  static defaultProps = {
    avatars: [],
    isFromSetupGuide: false,
    isSaving: false,
    mailboxes: [],
    sites: [],
    handleValidation: () => {},
    onCancel: () => {},
    onCopy: () => {},
    onCustomize: () => {},
    onSave: () => {},
    onSendInstructionsClick: () => {},
    fetchAvailableAvatars: () => {},
  }

  theme = {
    navHeight: 54,
  }

  componentDidUpdate(prevProps, prevState) {
    const { handleStepChange } = this.props
    if (this.state.currentStep !== prevState.currentStep) {
      handleStepChange(this.state.currentStep)
    }
  }

  componentDidMount() {
    this.setState({
      maxSteps: this.getSteps().length,
    })

    this.props.fetchAvailableAvatars()
  }

  hasDocsOrMailboxes = () => {
    const { mailboxes, sites } = this.props
    const hasDocs = sites && sites.length
    const hasMailboxes = mailboxes && mailboxes.length

    return hasDocs || hasMailboxes
  }

  onPrev = () => {
    const { currentStep } = this.state
    const { onCancel } = this.props

    if (currentStep > 1) {
      this.setState({
        currentStep: currentStep - 1,
      })
    } else {
      onCancel()
    }
  }

  onNext = () => {
    const { currentStep, maxSteps } = this.state
    const { onSave, handleValidation } = this.props

    if (currentStep === 1) {
      return handleValidation(this.goToNextStep)
    }

    if (currentStep === maxSteps - 1) {
      return onSave(
        data => {
          this.setState({
            beaconId: data.id,
            currentStep: maxSteps,
          })
        },
        (status, errors) => {
          if (status !== 422 || !errors) {
            return
          }

          if (errors.name || errors.displayStyleText) {
            this.setState({
              currentStep: 1,
            })
          } else if (errors.messagingMailboxId) {
            this.setState({
              currentStep: 2,
            })
          }
        }
      )
    }

    this.goToNextStep()
  }

  goToNextStep = () => {
    const { currentStep, maxSteps } = this.state
    if (currentStep < maxSteps) {
      this.setState({
        currentStep: currentStep + 1,
      })
    }
  }

  getSteps() {
    const { beaconId } = this.state
    const { mailboxes, sites, onCopy, onSendInstructionsClick } = this.props
    const hasDocs = sites && sites.length
    const hasMailboxes = mailboxes && mailboxes.length
    const steps = [
      <Step1 hasDocsOrMailboxes={this.hasDocsOrMailboxes()} key="Step1" />,
    ]

    if (hasMailboxes) {
      steps.push(<Step2 key="Step2" />)
    }

    if (hsGlobal.features.isDocsEnabled && hasDocs) {
      steps.push(<Step3 key="Step3" />)
    }

    steps.push(
      <Step4
        beaconId={beaconId}
        onCopy={onCopy}
        onSendInstructionsClick={onSendInstructionsClick}
        key="Step4"
      />
    )

    return steps
  }

  getPromoBanner() {
    const { currentStep } = this.state
    const steps = this.getSteps()
    // These imperative checks are needed as there is not a more effective
    // way to determine which step the user is currently on. This is due
    // to the fact that the collection of steps are dynamic - steps can be
    // excluded in certain cases - making the `currentStep` value nondeterministic.
    const stepIndex = currentStep - 1
    const isInstallationStep = steps[stepIndex].key === 'Step4'
    let banner

    if (isInstallationStep) {
      banner = <DeveloperToolsPromo isSetupFlow />
    }

    // Since each step is animated in, we need to animate the banner in
    // also so that it's not a jarring experience when it is displayed.
    if (banner) {
      return (
        <AnimateUI sequence="fade up" duration={300}>
          {banner}
        </AnimateUI>
      )
    }

    return null
  }

  renderBackButton() {
    const { currentStep, maxSteps } = this.state

    if (currentStep === maxSteps) {
      return null
    }

    return (
      <Button
        size="md"
        linked
        color="grey"
        data-cy="beaconOnbBackBtn"
        onClick={this.onPrev}
      >
        {currentStep === 1 ? 'Cancel' : 'Back'}
      </Button>
    )
  }

  renderNextButton() {
    const hasDocsOrMailboxes = this.hasDocsOrMailboxes()
    const { beaconId, currentStep, maxSteps } = this.state
    const { isFromSetupGuide, isSaving, onCancel, onCustomize } = this.props

    if (currentStep === maxSteps) {
      if (isFromSetupGuide) {
        return (
          <Button
            size="lg"
            color="grey"
            outlined
            data-cy="beaconOnbContinueBtn"
            onClick={onCancel}
          >
            Return to Setup Guide
          </Button>
        )
      }

      return (
        <Button
          size="lg"
          color="grey"
          outlined
          data-cy="beaconOnbContinueBtn"
          onClick={() => onCustomize(beaconId)}
        >
          Continue Editing
        </Button>
      )
    }

    const buttonText = isSaving ? 'Saving...' : 'Next'

    return (
      <Button
        size="lg"
        color="blue"
        data-cy="beaconOnbNextBtn"
        onClick={this.onNext}
        disabled={!hasDocsOrMailboxes || isSaving}
      >
        {buttonText}
      </Button>
    )
  }

  render() {
    const { currentStep } = this.state
    const themeName = getCurrentTheme()

    return (
      <>
        <GlobalStyle />
        <HSDSProvider scope="hsds-react" themeName={themeName}>
          <ThemeProvider theme={this.theme}>
            <PageUI className="c-NewBeaconForm">
              <PageCardUI>
                <PageCardInnerWrapperUI>
                  <Wizard currentStep={currentStep}>{this.getSteps()}</Wizard>
                </PageCardInnerWrapperUI>
                {this.getPromoBanner()}
              </PageCardUI>
              <Page.Actions
                className="u-mrg-b-0"
                primary={this.renderNextButton()}
                serious={this.renderBackButton()}
              />
            </PageUI>
          </ThemeProvider>
        </HSDSProvider>
      </>
    )
  }
}

BeaconOnboarding.propTypes = {
  canManageAccount: PropTypes.bool,
  fetchAvailableAvatars: PropTypes.func,
  handleStepChange: PropTypes.func,
  handleValidation: PropTypes.func,
  isFromSetupGuide: PropTypes.bool,
  isSaving: PropTypes.bool,
  mailboxes: PropTypes.arrayOf(
    PropTypes.shape({
      label: PropTypes.string,
      value: PropTypes.string,
    })
  ),
  onCancel: PropTypes.func,
  onCopy: PropTypes.func,
  onCustomize: PropTypes.func,
  onSave: PropTypes.func,
  onSendInstructionsClick: PropTypes.func,
  sites: PropTypes.arrayOf(
    PropTypes.shape({
      label: PropTypes.string,
      value: PropTypes.string,
    })
  ),
}

export default BeaconOnboarding
