import { AiImprovementCardUI } from './AiImprovementCard.css'
import Skeleton from 'hsds/components/skeleton'
import React from 'react'
import styled from 'styled-components'

const SkeletonAvatarContainer = styled.div`
  display: flex;
  flex-direction: row;
  gap: 5px;
  align-items: center;
  margin-top: 5px;
`

const AiImprovementsLoadingSkeleton = () => {
  return (
    <AiImprovementCardUI data-testid="skeleton-container">
      <Skeleton.Heading width="50%" />
      <Skeleton.Paragraph width="100%" />
      <SkeletonAvatarContainer>
        <Skeleton.Avatar size="sm" />
        <Skeleton.Text width="30%" />
      </SkeletonAvatarContainer>
    </AiImprovementCardUI>
  )
}

export default AiImprovementsLoadingSkeleton
