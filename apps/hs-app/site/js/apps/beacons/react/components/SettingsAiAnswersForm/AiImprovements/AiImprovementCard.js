import {
  AiImprovementCardUI,
  AiImprovementNameTextUI,
  AiImprovementStausTextUI,
  AIImprovementAuthorSectionUI,
} from './AiImprovementCard.css'
import { getLastSeen } from '@beacons/react/views/BeaconsView/utils/lastSeenDate'
import Avatar from 'hsds/components/avatar'
import Text from 'hsds/components/text'
import PropTypes from 'prop-types'
import React from 'react'

/**
 * @description
 * Determines the user information that will be rendered within the card.
 *
 * This hook handles all the logic for displaying user information in the card:
 * - Checks if the current user is the creator or updater of the improvement
 * - Gets the appropriate user object (either creator or updater)
 * - Formats the user's name, showing "you" if it's the current user
 */
const useImprovementUser = improvement => {
  const isCurrentUser =
    (improvement.updatedBy || improvement.createdBy) === hsGlobal?.memberId
  const user = improvement.updatedBy
    ? improvement.updatedByUser
    : improvement.createdByUser
  const name = user
    ? `${user.first || ''} ${user.last || ''}`.trim()
    : 'Unknown User'
  const displayName = isCurrentUser ? 'you' : name

  return {
    user,
    displayName,
    isCurrentUser,
  }
}

/**
 * @description
 * Creates a status message for a given improvement.
 *
 * This function generates a status string that shows:
 * - Whether the improvement was created or updated
 * - Who made the change
 * - How long ago the change was made
 *
 * For example: "Updated by John Smith 2 days ago" or "Created by you 1 week ago"
 */
const getImprovementStatus = (improvement, displayName) => {
  const timestamp = improvement.updatedBy
    ? improvement.syncedAt
    : improvement.createdAt
  if (!timestamp) return ''

  const prefix = improvement.updatedBy ? 'Updated' : 'Created'
  const timeString = getLastSeen(timestamp)

  return `${prefix} by ${displayName} ${timeString}`
}

const AiImprovementCard = ({ improvement, handleClick }) => {
  const { user, displayName } = useImprovementUser(improvement)
  const status = getImprovementStatus(improvement, displayName)

  return (
    <AiImprovementCardUI
      role="button"
      tabIndex={0}
      onKeyDown={e => {
        if (e.key === 'Enter' || e.key === ' ') {
          e.preventDefault()
          handleClick(e)
        }
      }}
      aria-label={improvement.name}
      onClick={handleClick}
    >
      <AiImprovementNameTextUI size={13} weight={500}>
        {improvement.name}
      </AiImprovementNameTextUI>
      <Text size={13}>{improvement.text}</Text>
      <AIImprovementAuthorSectionUI>
        <Avatar name={displayName} size="sm" image={user?.photo} />
        <AiImprovementStausTextUI size={13} shade="faint">
          {status}
        </AiImprovementStausTextUI>
      </AIImprovementAuthorSectionUI>
    </AiImprovementCardUI>
  )
}

AiImprovementCard.propTypes = {
  improvement: PropTypes.shape({
    name: PropTypes.string,
    text: PropTypes.string,
    createdBy: PropTypes.number,
    createdAt: PropTypes.string,
    syncedAt: PropTypes.string,
    updatedBy: PropTypes.number,
    createdByUser: PropTypes.shape({
      photo: PropTypes.string,
      first: PropTypes.string,
      last: PropTypes.string,
    }),
    updatedByUser: PropTypes.shape({
      photo: PropTypes.string,
      first: PropTypes.string,
      last: PropTypes.string,
    }),
  }).isRequired,
  handleClick: PropTypes.func.isRequired,
}

export default AiImprovementCard
