import {
  AiImprovementCardUI,
  AiImprovementNameTextUI,
  AiImprovementStausTextUI,
} from './AiImprovementCard.css'
import { getLastSeen } from '@beacons/react/views/BeaconsView/utils/lastSeenDate'
import Text from 'hsds/components/text'
import PropTypes from 'prop-types'
import React from 'react'

/**
 * @description
 * Returns the status of the improvement based on the createdAt, syncedAt, and updatedBy properties.
 * If the improvement has been updated, the status will be "Updated" and the time since the update.
 * If the improvement has not been updated, the status will be "Created" and the time since the creation.
 * Uses getLastSeen helper to format the time difference.
 */
const getImprovementStatus = ({ createdAt, syncedAt, updatedBy }) => {
  if (!createdAt) return ''

  const timestamp = updatedBy != null ? syncedAt : createdAt
  if (!timestamp) return ''

  const prefix = updatedBy != null ? 'Updated' : 'Created'
  const timeString = getLastSeen(timestamp)

  return `${prefix} ${timeString}`
}

const AiImprovementCard = ({ improvement, handleClick }) => {
  const status = getImprovementStatus(improvement)

  return (
    <AiImprovementCardUI
      role="button"
      tabIndex={0}
      onKeyDown={e => {
        if (e.key === 'Enter' || e.key === ' ') {
          e.preventDefault()
          handleClick(e)
        }
      }}
      aria-label={improvement.name}
      onClick={handleClick}
    >
      <AiImprovementNameTextUI size={13} weight={500}>
        {improvement.name}
      </AiImprovementNameTextUI>
      <Text size={13}>{improvement.text}</Text>
      <AiImprovementStausTextUI size={13} shade="faint">
        {status}
      </AiImprovementStausTextUI>
    </AiImprovementCardUI>
  )
}

AiImprovementCard.propTypes = {
  improvement: PropTypes.shape({
    name: PropTypes.string,
    text: PropTypes.string,
    createdAt: PropTypes.string,
    syncedAt: PropTypes.string,
    updatedBy: PropTypes.number,
  }).isRequired,
  handleClick: PropTypes.func.isRequired,
}

export default AiImprovementCard
