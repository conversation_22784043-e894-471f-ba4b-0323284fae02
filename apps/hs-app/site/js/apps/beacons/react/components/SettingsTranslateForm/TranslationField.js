import { updateTranslation } from '../../actions/translations'
import { getTranslationDefaults } from '../../reducers/translationDefaults'
import { getTranslations } from '../../reducers/translations'
import FormGroup from 'hsds/components/form-group'
import Input from 'hsds/components/input'
import { getColor } from 'hsds/utils/color'
import PropTypes from 'prop-types'
import React, { PureComponent as Component } from 'react'
import { connect } from 'react-redux'

export class TranslationField extends Component {
  static propTypes = {
    label: PropTypes.string,
    multiline: PropTypes.bool,
    name: PropTypes.string,
    onChange: PropTypes.func,
    placeholder: PropTypes.string,
    tooltip: PropTypes.string,
    value: PropTypes.string,
  }

  static defaultProps = {
    onChange: () => undefined,
  }

  render() {
    const { label, multiline, name, onChange, placeholder, tooltip, value } =
      this.props

    if (!label) {
      // Don't render anything if the label isn't specified
      // This will be because the translation label didn't exist on the model
      return null
    }

    let labelMarkup = label

    if (tooltip) {
      labelMarkup = (
        <div>
          <div>{label} </div>
          <div style={{ color: getColor('charcoal.600'), marginBottom: 3 }}>
            {tooltip}
          </div>
        </div>
      )
    }

    return (
      <FormGroup className="c-TranslationField">
        <Input
          id={name}
          label={labelMarkup}
          charValidatorLimit={280}
          multiline={multiline ? 5 : false}
          name={name}
          onChange={onChange}
          placeholder={placeholder}
          value={value}
          withCharValidator
        />
      </FormGroup>
    )
  }
}

const mapStateToProps = (state, ownProps) => {
  const { name } = ownProps
  const value = getTranslations(state)[name]
  const placeholder = getTranslationDefaults(state)[name]

  return {
    placeholder,
    value,
  }
}

const mapDispatchToProps = (dispatch, ownProps) => {
  const { name } = ownProps

  return {
    onChange: value => dispatch(updateTranslation(name, value)),
  }
}

export default connect(mapStateToProps, mapDispatchToProps)(TranslationField)
