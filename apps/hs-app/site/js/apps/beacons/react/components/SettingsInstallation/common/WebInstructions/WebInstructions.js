import DeveloperToolsPromo from '../../../DeveloperToolsPromo'
import withResetStyles from '../../../withResetStyles'
import withScrollToTop from '../../../withScrollToTop'
import { Subtitle } from '../../common/subtitle.css'
import {
  ExplanationUI,
  LabelWrapperUI,
  PageCardUI,
  PageCardInnerWrapperUI,
} from './WebInstructions.css.js'
import { SettingsPageUI } from '@beacons/react/components/SettingsPage/SettingsPage'
import compose from '@common/utils/compose'
import Button from 'hsds/components/button'
import CopyCode from 'hsds/components/copy-code'
import Flexy from 'hsds/components/flexy'
import FormGroup from 'hsds/components/form-group'
import HelpText from 'hsds/components/help-text'
import Label from 'hsds/components/label'
import Page from 'hsds/components/page'
import PropTypes from 'prop-types'
import React, { PureComponent as Component } from 'react'

class BaseWebInstructions extends Component {
  static propTypes = {
    code: PropTypes.string,
    onCopy: PropTypes.func,
    onSendInstructionsClick: PropTypes.func,
  }

  static defaultProps = {
    onCopy: () => undefined,
    onSendInstructionsClick: () => undefined,
  }

  static displayName = 'WebInstructions'

  renderExplanation() {
    const { code, onSendInstructionsClick } = this.props

    return (
      <ExplanationUI>
        <LabelWrapperUI>
          <Label>HTML code</Label>
        </LabelWrapperUI>
        <Flexy>
          <Flexy.Item>
            <HelpText>
              <Subtitle>
                Paste this code before the {'</body>'} tag.{' '}
                <Button
                  color="blue"
                  size="sm"
                  linked
                  inlined
                  onClick={event => {
                    event && event.preventDefault()
                    onSendInstructionsClick(event, code)
                  }}
                >
                  Send Instructions
                </Button>
              </Subtitle>
            </HelpText>
          </Flexy.Item>
        </Flexy>
      </ExplanationUI>
    )
  }

  render() {
    const { code, onCopy } = this.props

    return (
      <SettingsPageUI>
        <PageCardUI>
          <PageCardInnerWrapperUI>
            <Page.Section>
              <Page.Header
                render={({ Title }) => <Title>Install Beacon for Web</Title>}
              />

              <Page.Content>
                <FormGroup>
                  {this.renderExplanation()}
                  <CopyCode
                    code={code}
                    language="html"
                    maxWidth={2000}
                    onCopy={onCopy}
                    buttonSize="lg"
                    withButton="outside"
                    buttonProps={{
                      color: 'blue',
                      outlined: false,
                    }}
                  />
                </FormGroup>
              </Page.Content>
            </Page.Section>
          </PageCardInnerWrapperUI>
          <DeveloperToolsPromo />
        </PageCardUI>
      </SettingsPageUI>
    )
  }
}

const enhance = compose(withResetStyles, withScrollToTop)

export default enhance(BaseWebInstructions)
