import { AiAnswersFormContext } from './AiAnswersFormContext'
import AiImprovementsProvider from './AiImprovements/context/AiImprovementsContext'
import { SettingsAiAnswersForm } from './SettingsAiAnswersForm'
import AiAnswersFormWithNavigation from './components/AiAnswersFormWithNavigation'
import withScrollToTop from '@beacons/react/components/withScrollToTop'
import { isTestEnv } from '@common/utils/env'
import { connect } from '@helpscout/brigade'
import { QueryClient, QueryClientProvider } from '@tanstack/react-query'
import { Provider as HSDSProvider } from 'hsds/components/hsds'
import PropTypes from 'prop-types'
import React, { forwardRef } from 'react'
import { BrowserRouter } from 'react-router-dom'
import NotyProvider from 'shared/components/Noty'
import { TrpcProvider, createTrpcClient } from 'shared/utils/trpc'
import { createGlobalStyle } from 'styled-components'

const GlobalStyle = createGlobalStyle`
  // We need to override styles from the beaconSettingsLayout.hbs that is used for all sidebar nav items
  .beacon-builder-settings__formWrapper {
    padding-top: 0;
    transition: padding-left 200ms linear, padding-right 200ms linear, padding-bottom 200ms linear;
  }
`

const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      staleTime: 30000,
    },
  },
})

const trpcClient = createTrpcClient()

const SettingsAiAnswersFormWrapper = forwardRef(
  function SettingsAiAnswersFormWrapper(props, ref) {
    const {
      isSaving,
      aiAnswersEnabled,
      docsSearchEnabled,
      isSiteSelected,
      onCancel,
      onSave,
      handleAiAnswersEnabledChange,
      voiceAndTonePromptText,
      handleVoiceAndToneChange,
      externalSourceIds,
      snippetIds,
      handleExternalSourceAdded,
      handleExternalSourceRemoved,
      beaconId,
    } = props

    const basename = isTestEnv() ? '/' : `/settings/beacons/${beaconId}/answers`
    const isAiAnswersTidbitsEnabled =
      !!window.hsGlobal.features.isAiAnswersTidbitsEnabled

    return (
      <AiAnswersFormContext.Provider
        value={{
          isSaving,
          aiAnswersEnabled,
          docsSearchEnabled,
          isSiteSelected,
          onCancel,
          onSave,
          handleAiAnswersEnabledChange,
          voiceAndTonePromptText,
          handleVoiceAndToneChange,
          externalSourceIds,
          snippetIds,
          beaconId,
          handleExternalSourceAdded,
          handleExternalSourceRemoved,
          isAiAnswersTidbitsEnabled,
        }}
      >
        <TrpcProvider client={trpcClient} queryClient={queryClient}>
          {isAiAnswersTidbitsEnabled && <GlobalStyle />}
          <QueryClientProvider client={queryClient}>
            <HSDSProvider scope="hsds-react">
              {isAiAnswersTidbitsEnabled ? (
                <>
                  <BrowserRouter basename={basename}>
                    <NotyProvider id="hs-app-beacon-settings-ai-answers">
                      <AiImprovementsProvider>
                        <AiAnswersFormWithNavigation />
                      </AiImprovementsProvider>
                    </NotyProvider>
                  </BrowserRouter>
                </>
              ) : (
                <SettingsAiAnswersForm />
              )}
            </HSDSProvider>
          </QueryClientProvider>
        </TrpcProvider>
      </AiAnswersFormContext.Provider>
    )
  }
)

SettingsAiAnswersFormWrapper.propTypes = {
  isSaving: PropTypes.bool,
  aiAnswersEnabled: PropTypes.bool,
  docsSearchEnabled: PropTypes.bool,
  isSiteSelected: PropTypes.bool,
  voiceAndTonePromptText: PropTypes.string,
  handleVoiceAndToneChange: PropTypes.func,
  onCancel: PropTypes.func,
  onSave: PropTypes.func,
  handleAiAnswersEnabledChange: PropTypes.func,
  onDelete: PropTypes.func,
  externalSourceIds: PropTypes.arrayOf(PropTypes.number),
  snippetIds: PropTypes.arrayOf(PropTypes.number),
  handleExternalSourceAdded: PropTypes.func,
  handleExternalSourceRemoved: PropTypes.func,
  beaconId: PropTypes.string,
}

const mapStateToProps = ({
  beacon: {
    isSaving,
    aiAnswersEnabled = false,
    docsSearchEnabled,
    voiceAndTonePromptText,
    externalSourceIds,
    snippetIds,
    id: beaconId,
  },
  viewModel: { isSiteSelected },
}) => {
  return {
    isSaving,
    aiAnswersEnabled,
    docsSearchEnabled,
    isSiteSelected,
    voiceAndTonePromptText,
    externalSourceIds,
    snippetIds,
    beaconId,
  }
}

const mapActionsToProps = store => {
  const {
    onCancel,
    onSave,
    handleAiAnswersEnabledChange,
    handleVoiceAndToneChange,
    handleExternalSourceAdded,
    handleExternalSourceRemoved,
  } = store.getStatelessExternalActions()
  return {
    onCancel,
    onSave,
    handleAiAnswersEnabledChange,
    handleVoiceAndToneChange,
    handleExternalSourceAdded,
    handleExternalSourceRemoved,
  }
}

export default connect(
  mapStateToProps,
  mapActionsToProps
)(withScrollToTop(SettingsAiAnswersFormWrapper))
