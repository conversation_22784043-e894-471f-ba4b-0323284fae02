import { useEditExternalSource } from '../../ExternalSources.hooks'
import {
  THIS_PAGE,
  CUSTOMIZE_SELECTION,
  ALL_PAGES,
  WebsitesModal,
} from './WebsitesModal'
import Portal from 'hsds/components/portal'
import React, { useState, useEffect } from 'react'

export function EditWebsiteModal() {
  const [editingWebsite, setEditingWebsite] = useState(null)
  const { editExternalSource, isEditing } = useEditExternalSource()

  const performEdit = (body = {}, onSuccess) => {
    if (isEditing) {
      return
    }

    editExternalSource(
      { id: editingWebsite.id, ...body, type: 'public_site' },
      {
        onSuccess: () => {
          onSuccess()
        },
      }
    )
  }

  useEffect(() => {
    function open(event) {
      setEditingWebsite(event.detail.source)
    }

    // we are opening this modal from entirely different place in the app and I used custom events to trigger it
    document.addEventListener('open-edit-website-modal', open)

    return () => {
      document.removeEventListener('open-edit-website-modal', open)
    }
  }, [])

  if (!editingWebsite) {
    return null
  }

  return (
    <Portal>
      <WebsitesModal
        url={editingWebsite.url}
        initialSelection={
          editingWebsite.pageLimit === 1 || editingWebsite.pageCount === 1
            ? THIS_PAGE
            : editingWebsite.excludedUrlPaths.length > 0
            ? CUSTOMIZE_SELECTION
            : ALL_PAGES
        }
        initialExcludedPages={editingWebsite.excludedUrlPaths || []}
        websiteName={editingWebsite.name}
        onClose={() => setEditingWebsite(null)}
        editMode={true}
        isSubmitting={isEditing}
        onSubmit={performEdit}
      />
    </Portal>
  )
}
