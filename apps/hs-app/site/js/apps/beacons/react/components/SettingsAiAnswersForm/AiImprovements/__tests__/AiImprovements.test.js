import { AiAnswersFormContext } from '../../AiAnswersFormContext'
import AiImprovements from '../AiImprovements'
import AiImprovementsProvider from '../context/AiImprovementsContext'
import { QueryClient, QueryClientProvider } from '@tanstack/react-query'
import { render, screen, waitFor } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import nock from 'nock'
import React from 'react'
import { waitForRequestsToFinish } from 'shared/testUtils/async-utils'
import { createTrpcClient, TrpcProvider } from 'shared/utils/trpc'

const mockShowErrorNoty = jest.fn()
const mockShowConfirmNoty = jest.fn()
const mockShowSuccessNoty = jest.fn()
jest.mock('shared/hooks/useNoty', () => ({
  useNoty: () => ({
    showErrorNoty: mockShowErrorNoty,
    showSuccessNoty: mockShowSuccessNoty,
    showConfirmNoty: mockShowConfirmNoty,
  }),
}))

if (window.HS) {
  window.HS.csrfToken = 'secret'
}

jest.setTimeout(60000)

const mockSources = [
  {
    id: '1',
    name: 'First Improvement',
    text: 'First Description',
    createdAt: '2024-03-20T10:00:00Z', // March 20,
    syncStatus: 'completed',
    syncedAt: '2024-03-20T10:00:00Z',
    type: 'snippet',
  },
  {
    id: '2',
    name: 'Second Improvement',
    text: 'Second Description',
    createdAt: '2024-03-21T10:00:00Z', // March 21
    updatedBy: 123,
    syncedAt: '2024-03-22T10:00:00Z', // March 22 (most recent)
    syncStatus: 'completed',
    type: 'snippet',
  },
  {
    id: '3',
    name: 'Third Improvement',
    text: 'Third Description',
    createdAt: '2024-03-19T10:00:00Z', // March 19 (oldest)
    syncStatus: 'completed',
    syncedAt: '2024-03-19T10:00:00Z',
    type: 'snippet',
  },
]

const mockGetExternalSources = (response = [], status = 200) => {
  const mock = nock('http://localhost')
    .get(RegExp('getExternalSources'))
    .reply(
      status,
      status === 500
        ? {
            error: {
              data: {
                httpStatus: 500,
              },
            },
          }
        : {
            result: {
              data: {
                json: {
                  sources: response,
                  companyPageCount: 10,
                  companyPageLimit: 100,
                },
              },
            },
          }
    )
  return mock
}

const renderWithMocks = () => {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: {
        retry: false,
      },
    },
  })
  const trpcClient = createTrpcClient()

  return render(
    <TrpcProvider queryClient={queryClient} client={trpcClient}>
      <QueryClientProvider client={queryClient}>
        <AiAnswersFormContext.Provider value={{ beaconId: '1' }}>
          <AiImprovementsProvider>
            <AiImprovements />
          </AiImprovementsProvider>
        </AiAnswersFormContext.Provider>
      </QueryClientProvider>
    </TrpcProvider>
  )
}

const createMockSources = count =>
  Array.from({ length: count }, (_, i) => ({
    id: String(count - i),
    name: `Improvement ${count - i}`,
    text: `Description ${count - i}`,
    createdAt: `2024-03-${20 + (count - i)}T10:00:00Z`,
    type: 'snippet',
  }))

describe('<AiImprovements />', () => {
  beforeEach(() => {
    nock.cleanAll()
  })

  afterEach(() => {
    mockShowErrorNoty.mockReset()
    mockShowConfirmNoty.mockReset()
    mockShowSuccessNoty.mockReset()
  })

  beforeAll(() => {
    global.XMLHttpRequest = global.originalXhr
  })

  afterAll(() => {
    global.XMLHttpRequest = undefined
  })

  describe('loading and error states', () => {
    it('renders the loading state when loading', () => {
      renderWithMocks()

      expect(screen.getByTestId('skeleton-container')).toBeInTheDocument()
    })

    it('renders the error state when there is a fetching error', async () => {
      mockGetExternalSources([], 500)
      renderWithMocks()

      const errorText = await screen.findByText(
        'There was an error fetching improvements, please try again.'
      )
      expect(errorText).toBeInTheDocument()
    })
  })

  describe('improvements list', () => {
    it('renders improvements sorted by most recent first', async () => {
      mockGetExternalSources(mockSources)
      renderWithMocks()
      await waitForRequestsToFinish()

      const improvements = await screen.findAllByRole('listitem')
      expect(improvements).toHaveLength(3)
      // Second improvement is first because it has the most recent syncedAt (March 22)
      expect(improvements[0]).toHaveTextContent('Second Improvement')
      // First improvement is second because it has the second most recent createdAt (March 20)
      expect(improvements[1]).toHaveTextContent('First Improvement')
      // Third improvement is last because it has the oldest createdAt (March 19)
      expect(improvements[2]).toHaveTextContent('Third Improvement')
    })

    it('filters improvements based on search input', async () => {
      mockGetExternalSources(mockSources)
      renderWithMocks()

      await waitFor(() => {
        expect(screen.getAllByRole('listitem')).toHaveLength(3)
      })

      const searchInput = screen.getByRole('textbox')
      await userEvent.type(searchInput, 'Second')

      await waitFor(() => {
        const improvements = screen.getAllByRole('listitem')
        expect(improvements).toHaveLength(1)
        expect(improvements[0]).toHaveTextContent('Second Improvement')
      })
    })

    it('shows blank slate UI when no search results are found', async () => {
      mockGetExternalSources(mockSources)
      renderWithMocks()

      await waitFor(() => {
        expect(screen.getAllByRole('listitem')).toHaveLength(3)
      })

      const searchInput = screen.getByRole('textbox')
      await userEvent.type(searchInput, 'This does not exist')

      await waitFor(() => {
        expect(
          screen.getByTestId('ai-improvements-search-blank-slate')
        ).toBeInTheDocument()
        expect(
          screen.getByText(
            /Couldn't find anything for "This does not exist". Care to try again\?/
          )
        ).toBeInTheDocument()
      })
    })

    it('updates search results as a user types', async () => {
      mockGetExternalSources(mockSources)
      renderWithMocks()

      await waitFor(() => {
        expect(screen.getAllByRole('listitem')).toHaveLength(3)
      })

      const searchInput = screen.getByRole('textbox')
      await userEvent.type(searchInput, 'First')
      await waitFor(() => {
        const improvements = screen.getAllByRole('listitem')
        expect(improvements).toHaveLength(1)
        expect(improvements[0]).toHaveTextContent('First Improvement')
      })

      await userEvent.clear(searchInput)
      await userEvent.type(searchInput, 'Second')
      await waitFor(() => {
        const improvements = screen.getAllByRole('listitem')
        expect(improvements).toHaveLength(1)
        expect(improvements[0]).toHaveTextContent('Second Improvement')
      })
    })
  })

  describe('pagination', () => {
    it('handles pagination correctly', async () => {
      const user = userEvent.setup()
      const manySources = createMockSources(15)
      mockGetExternalSources(manySources)
      renderWithMocks()

      let improvements = await screen.findAllByRole('listitem')
      expect(improvements).toHaveLength(10)
      expect(improvements[0]).toHaveTextContent(
        /Improvement 15.*Description 15/
      )
      expect(improvements[9]).toHaveTextContent(/Improvement 6.*Description 6/)

      await user.click(screen.getByRole('button', { name: /next/i }))

      improvements = screen.getAllByRole('listitem')
      expect(improvements).toHaveLength(5)
      expect(improvements[0]).toHaveTextContent(/Improvement 5.*Description 5/)
      expect(improvements[4]).toHaveTextContent(/Improvement 1.*Description 1/)
    })
  })

  describe('adding an improvement', () => {
    it('opens composer when Add button is clicked', async () => {
      const user = userEvent.setup()
      mockGetExternalSources(mockSources)
      renderWithMocks()
      await waitForRequestsToFinish()

      const addButton = await screen.findByRole('button', { name: 'Add' })
      await user.click(addButton)

      expect(
        screen.getByText(
          'Help AI Answers improve future responses on this topic.'
        )
      ).toBeInTheDocument()
      expect(
        screen.getByPlaceholderText('Provide additional context')
      ).toBeInTheDocument()
      expect(screen.getByLabelText('Generate improvement')).toBeInTheDocument()
    })

    it('closes composer when close button is clicked', async () => {
      const user = userEvent.setup()
      mockGetExternalSources(mockSources)
      renderWithMocks()
      await waitForRequestsToFinish()

      const addButton = await screen.findByRole('button', { name: 'Add' })
      await user.click(addButton)

      const closeButton = screen.getByRole('button', {
        name: 'Discard improvement',
      })
      await user.click(closeButton)

      expect(
        screen.queryByText(
          'Help AI Answers improve future responses on this topic.'
        )
      ).not.toBeInTheDocument()
    })

    it('refreshes improvements list after successful improvement addition', async () => {
      const user = userEvent.setup()
      const mock = mockGetExternalSources(mockSources)
      renderWithMocks()
      await waitForRequestsToFinish()

      const addButton = await screen.findByRole('button', { name: 'Add' })
      await user.click(addButton)

      nock('http://localhost')
        .post(RegExp('generateImprovement'))
        .reply(200, {
          result: {
            data: {
              json: {
                title: 'New Improvement',
                text: 'New improvement text',
              },
            },
          },
        })
        .post(RegExp('createExternalSource'))
        .reply(200, {
          result: {
            data: {
              json: { id: 42 },
            },
          },
        })
        .post(RegExp('patchBeacon'))
        .reply(200, {
          result: {
            data: {
              json: {},
            },
          },
        })

      const input = screen.getByPlaceholderText('Provide additional context')
      await user.type(input, 'This is a new improvement')
      await user.click(screen.getByLabelText('Generate improvement'))

      await waitFor(() => {
        expect(screen.getByText('New Improvement')).toBeInTheDocument()
      })

      const saveButton = screen.getByText('Save Improvement').closest('button')
      await user.click(saveButton)

      await waitFor(() => {
        expect(
          screen.queryByText(
            'Help AI Answers improve future responses on this topic.'
          )
        ).not.toBeInTheDocument()
      })

      expect(mock.isDone()).toBe(true)
      expect(mock.pendingMocks()).toHaveLength(0)
    })
  })

  describe('editing an improvement', () => {
    it('opens the edit composer when an improvement card is clicked', async () => {
      const user = userEvent.setup()
      mockGetExternalSources(mockSources)
      renderWithMocks()
      await waitForRequestsToFinish()

      await screen.findAllByRole('listitem')
      await user.click(
        screen.getByRole('button', { name: 'First Improvement' })
      )

      expect(screen.getByDisplayValue('First Improvement')).toBeInTheDocument()
      expect(screen.getByDisplayValue('First Description')).toBeInTheDocument()
      expect(screen.getByText('Save')).toBeInTheDocument()
    })

    it('closes the edit composer when the close button is clicked', async () => {
      const user = userEvent.setup()
      mockGetExternalSources(mockSources)
      renderWithMocks()
      await waitForRequestsToFinish()

      await screen.findAllByRole('listitem')
      await user.click(
        screen.getByRole('button', { name: 'First Improvement' })
      )

      expect(screen.getByDisplayValue('First Improvement')).toBeInTheDocument()

      await user.click(
        screen.getByRole('button', { name: 'Close improvement editor' })
      )

      expect(
        screen.queryByDisplayValue('First Improvement')
      ).not.toBeInTheDocument()
    })

    it('shows a success noty after a successful edit', async () => {
      nock('http://localhost')
        .post(RegExp('updateExternalSource'))
        .reply(200, {
          result: {
            data: {
              json: {},
            },
          },
        })

      const user = userEvent.setup()
      mockGetExternalSources(mockSources)
      renderWithMocks()
      await waitForRequestsToFinish()

      await screen.findAllByRole('listitem')
      await user.click(
        screen.getByRole('button', { name: 'First Improvement' })
      )

      const titleInput = screen.getByLabelText('Improvement title')
      const bodyInput = screen.getByLabelText('Improvement description')

      await user.clear(titleInput)
      await user.type(titleInput, 'Updated First Improvement')
      await user.clear(bodyInput)
      await user.type(bodyInput, 'Updated First Description')

      await user.click(screen.getByRole('button', { name: 'Save' }))

      await waitFor(() => {
        expect(mockShowSuccessNoty).toHaveBeenCalledWith('Improvement updated')
      })
    })

    it('shows an error noty when saving fails', async () => {
      nock('http://localhost')
        .post(RegExp('updateExternalSource'))
        .reply(500, {
          error: {
            data: {
              httpStatus: 500,
            },
          },
        })

      const user = userEvent.setup()
      mockGetExternalSources(mockSources)
      renderWithMocks()
      await waitForRequestsToFinish()

      await screen.findAllByRole('listitem')
      await user.click(
        screen.getByRole('button', { name: 'First Improvement' })
      )

      const titleInput = screen.getByLabelText('Improvement title')
      await user.clear(titleInput)
      await user.type(titleInput, 'Updated Title')

      await user.click(screen.getByRole('button', { name: 'Save' }))

      await waitFor(() => {
        expect(mockShowErrorNoty).toHaveBeenCalledWith(
          'Cannot save Improvement. Please try again.'
        )
      })
    })

    it('shows a confirmation noty when the delete button is clicked', async () => {
      const user = userEvent.setup()
      mockGetExternalSources(mockSources)
      renderWithMocks()
      await waitForRequestsToFinish()

      await screen.findAllByRole('listitem')
      await user.click(
        screen.getByRole('button', { name: 'First Improvement' })
      )

      await user.click(
        screen.getByRole('button', { name: 'Delete improvement' })
      )

      await waitFor(() => {
        expect(mockShowConfirmNoty).toHaveBeenCalledWith(
          'Delete improvement',
          expect.anything()
        )
      })
    })
  })
})
