import { SettingsAiAnswersForm } from '../SettingsAiAnswersForm'
import AiImprovements from '../AiImprovements/AiImprovements'
import React from 'react'
import { Redirect, Route, Switch } from 'react-router-dom'

const SettingsAiAnswersRoutes = () => {
  return (
    <Switch>
      <Route exact path="/" render={() => <Redirect to="/configure" />} />
      <Route exact path="/configure" render={() => <SettingsAiAnswersForm />} />
      <Route exact path="/improvements" render={() => <AiImprovements />} />
    </Switch>
  )
}

export default SettingsAiAnswersRoutes
