import Card from 'hsds/components/card'
import Text from 'hsds/components/text'
import { focusRing } from 'hsds/utils/mixins'
import styled from 'styled-components'

export const AiImprovementCardUI = styled(Card)`
  display: flex;
  flex-direction: column;
  gap: 5px;

  padding: 15px 25px;

  will-change: box-shadow;
  transition: box-shadow 0.2s ease-in-out;

  &:hover {
    box-shadow: var(--hsds-token-shadow-200);
  }

  ${focusRing}
  --hsds-focus-ring-offset: -3px;
  --hsds-focus-ring-radius: 6px;

  &:focus {
    outline: none;
    text-decoration: none;
  }
`

export const AiImprovementNameTextUI = styled(Text)`
  color: var(--hsds-token-color-text-dark);
`

export const AiImprovementStausTextUI = styled(Text)`
  margin-top: 5px;
`

export const AIImprovementAuthorSectionUI = styled.div`
  display: flex;
  align-items: center;
  gap: 5px;
`
