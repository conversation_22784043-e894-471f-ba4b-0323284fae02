import DeveloperToolsPromo from '../../../DeveloperToolsPromo'
import withResetStyles from '../../../withResetStyles'
import withScrollToTop from '../../../withScrollToTop'
import BeaconId from '../BeaconId'
import { PageCardUI, PageCardInnerWrapperUI } from './AndroidInstructions.css'
import { SettingsPageUI } from '@beacons/react/components/SettingsPage/SettingsPage'
import compose from '@common/utils/compose'
import CopyCode from 'hsds/components/copy-code'
import FormGroup from 'hsds/components/form-group'
import FormLabel from 'hsds/components/form-label'
import Page from 'hsds/components/page'
import PropTypes from 'prop-types'
import React, { PureComponent as Component } from 'react'

class BaseAndroidInstructions extends Component {
  static propTypes = {
    beaconId: PropTypes.string,
    onCopy: PropTypes.func,
  }

  static defaultProps = {
    onCopy: () => undefined,
  }
  static displayName = 'AndroidInstructions'

  getCode() {
    const { beaconId } = this.props

    return `Beacon beacon = new Beacon.Builder()\n    .withBeaconId("${beaconId}")\n    .build();`
  }

  render() {
    const { beaconId, onCopy } = this.props

    return (
      <SettingsPageUI>
        <PageCardUI>
          <PageCardInnerWrapperUI>
            <Page.Section>
              <Page.Header
                render={({ Title }) => (
                  <Title>Install Beacon for Android</Title>
                )}
              />
              <Page.Content>
                <BeaconId
                  beaconId={beaconId}
                  docsUrl="https://developer.helpscout.com/beacon-2/android/"
                  onCopy={onCopy}
                />

                <FormGroup>
                  <FormLabel label="Java">
                    <CopyCode
                      code={this.getCode()}
                      language="java"
                      onCopy={onCopy}
                      maxWidth={2000}
                    />
                  </FormLabel>
                </FormGroup>
              </Page.Content>
            </Page.Section>
          </PageCardInnerWrapperUI>
          <DeveloperToolsPromo />
        </PageCardUI>
      </SettingsPageUI>
    )
  }
}

const enhance = compose(withResetStyles, withScrollToTop)

export default enhance(BaseAndroidInstructions)
