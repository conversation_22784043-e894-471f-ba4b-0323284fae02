import { TranslationField } from './TranslationField'
import { fireEvent, render, screen } from '@testing-library/react'
import React from 'react'

describe('TranslationField', () => {
  it('should trigger onChange callback when the input is changed', () => {
    const onChangeSpy = jest.fn()
    render(
      <TranslationField
        label="Label"
        onChange={onChangeSpy}
        value="Initial translation"
      />
    )
    fireEvent.change(screen.getByRole('textbox'), {
      target: { value: 'Updated translation' },
    })

    expect(onChangeSpy).toHaveBeenCalledWith(
      'Updated translation',
      expect.anything()
    )
  })

  it('should not display remaining characters when more than 140 characters remain', () => {
    const { container } = render(
      <TranslationField
        label="Label"
        onChange={jest.fn()}
        value={Array(139).fill('a').join('')}
      />
    )

    const input = screen.getByRole('textbox')
    input.focus()
    expect(
      container.querySelector('.c-Input__CharValidator')
    ).not.toBeInTheDocument()
  })

  it('displays remaining characters when 140 characters or less remain', () => {
    const { container } = render(
      <TranslationField
        label="Label"
        onChange={jest.fn()}
        value={Array(161).fill('a').join('')}
      />
    )

    const input = screen.getByRole('textbox')
    input.focus()
    expect(
      container.querySelector('.c-Input__CharValidator')
    ).toBeInTheDocument()
  })
})
