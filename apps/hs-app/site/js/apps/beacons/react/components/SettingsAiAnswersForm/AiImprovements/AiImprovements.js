import { useAiAnswersFormContext } from '../AiAnswersFormContext'
import {
  AiImprovementsUI,
  AiImprovementsPageCardUI,
  AddImprovementComposerUI,
  AiImprovementsHeadingTextUI,
  AiImprovementsListSectionUI,
  AIImprovementsHeaderSectionUI,
} from './AiImprovements.css'
import AiImprovementsList from './AiImprovementsList'
import AiImprovementsLoadingSkeleton from './AiImprovementsLoadingSkeleton'
import AiImprovementsSearchInput from './AiImprovementsSearchInput'
import {
  useAiImprovementsContext,
  IMPROVEMENTS_PER_PAGE,
} from './context/AiImprovementsContext'
import { Alert } from 'hsds/components/alert'
import BlankSlate from 'hsds/components/blank-slate'
import Button from 'hsds/components/button'
import Pagination from 'hsds/components/pagination'
import Text from 'hsds/components/text'
import UserCircleDashLarge from 'hsds/icons/user-circle-dash-large'
import React, { useState, useEffect } from 'react'

const AiImprovements = () => {
  const [composerOpen, setComposerOpen] = useState(false)

  const {
    editId,
    isError,
    isLoading,
    totalItems,
    currentPage,
    searchValue,
    handlePageChange,
    currentImprovements,
    handleRefetchSources,
    handleEditIdChange,
  } = useAiImprovementsContext()
  const { beaconId } = useAiAnswersFormContext()

  const hasNoResults = currentImprovements.length === 0 && !!searchValue

  useEffect(() => {
    if (composerOpen && editId) {
      setComposerOpen(false)
    }
  }, [editId, composerOpen])

  const handleComposerOpen = () => {
    if (editId) {
      handleEditIdChange(null)
    }
    setComposerOpen(true)
  }

  const renderContent = () => {
    if (isLoading) {
      return <AiImprovementsLoadingSkeleton />
    }

    if (isError) {
      return (
        <Alert status="error">
          There was an error fetching improvements, please try again.
        </Alert>
      )
    }

    return (
      <>
        <AIImprovementsHeaderSectionUI>
          <div>
            <AiImprovementsHeadingTextUI size="md" as="h1" weight={500}>
              Improvements
            </AiImprovementsHeadingTextUI>
            <Text shade="muted">
              Fill knowledge gaps with any additional info you&apos;d like AI
              Answers to know.
            </Text>
          </div>
          <Button onClick={handleComposerOpen}>Add</Button>
        </AIImprovementsHeaderSectionUI>
        <AiImprovementsSearchInput />
        {composerOpen && (
          <AddImprovementComposerUI
            beaconId={beaconId}
            onAdd={handleRefetchSources}
            onClose={() => setComposerOpen(false)}
          />
        )}
        {hasNoResults ? (
          <BlankSlate
            icon={UserCircleDashLarge}
            data-testid="ai-improvements-search-blank-slate"
            message={`Couldn't find anything for "${searchValue}". Care to try again?`}
          />
        ) : (
          <>
            <AiImprovementsListSectionUI>
              <AiImprovementsList />
              <Pagination
                showNavigation
                subject="improvement"
                totalItems={totalItems}
                activePage={currentPage}
                onChange={handlePageChange}
                rangePerPage={IMPROVEMENTS_PER_PAGE}
              />
            </AiImprovementsListSectionUI>
          </>
        )}
      </>
    )
  }

  return (
    <AiImprovementsUI className="with-adjusted-width">
      <AiImprovementsPageCardUI>{renderContent()}</AiImprovementsPageCardUI>
    </AiImprovementsUI>
  )
}

export default AiImprovements
