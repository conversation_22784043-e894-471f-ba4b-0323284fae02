import { useMemo } from 'react'
import { useUpgradeBannerHeight } from 'shared/hooks/useUpgradeBannerHeight'

export function useNavHeightTheme(customHeight = 0) {
  const growthKitUpgradeBannerHeight = useUpgradeBannerHeight()

  return useMemo(() => {
    const hasLoginAs = document.body.classList.contains('has-login-as')

    return {
      navHeight:
        54 +
        (hasLoginAs ? 28 : 0) +
        growthKitUpgradeBannerHeight +
        customHeight,
    }
  }, [growthKitUpgradeBannerHeight, customHeight])
}
