import template from '../templates/noBeacons.hbs'
import NoBeaconsView from '@beacons/react/views/NoBeaconsView'
import { ReactMounter } from '@helpscout/brigade'
import Marionette from 'marionette'
import React from 'react'

const NoBeaconsLayout = Marionette.Layout.extend({
  template,

  initialize() {
    Object.assign(this, {
      imagePath: hsGlobal.imagePath,
    })
  },

  components() {
    return {
      '#noBeaconsView': <NoBeaconsView imagePath={this.imagePath} />,
    }
  },
})

export default ReactMounter(NoBeaconsLayout)
