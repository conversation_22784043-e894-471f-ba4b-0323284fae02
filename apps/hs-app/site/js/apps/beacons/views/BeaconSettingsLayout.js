import { embedPreview } from '../components/helpers'
import template from '../templates/beaconSettingsLayout.hbs'
import getUserAvatarDataForBeaconPreview from '../utils/getUserAvatarDataForBeaconPreview'
import BeaconDropdownView from '../views/BeaconDropdownView'
import $ from 'jquery'
import Marionette from 'marionette'
import _ from 'underscore'

module.exports = Marionette.Layout.extend({
  template,

  regions: {
    dropdownRegion: '.js-beacon-dropdown',
    settingsRegion: '.js-beacon-settings',
  },

  initialize({ cdnDomain, navTab, beaconPreview }) {
    Object.assign(this, {
      cdnDomain,
      navTab,
      beaconPreview,
      showAiAnswers: window.hsGlobal.features.isAiAnswersEnabled,
      isAiAnswersTidbitsEnabled:
        window.hsGlobal.features.isAiAnswersTidbitsEnabled,
    })
  },

  onShow() {
    // The dropdown is not shown if there is an installedSiteId (Docs Beacon), we show just a title instead
    if (
      this.collection.models.length > 1 &&
      !this.model.get('installedSiteId')
    ) {
      this.dropdownRegion.show(
        new BeaconDropdownView({
          model: this.model,
          collection: this.collection,
        })
      )
    }

    // Prevent the window from being scrolled down when navigating to this page from
    // the bottom of a longer page.
    $(window).scrollTop(0)

    // TODO: Move embedPreview logic into init
    embedPreview(this.cdnDomain)

    const avatars = getUserAvatarDataForBeaconPreview(
      this.model.get('customAvatarsUserIds'),
      this.model.get('availableAvatars')
    )

    this.beaconPreview.init({
      beaconId: this.model.get('id'),
      demoModeEnabled: true,
      docsSiteId: this.model.get('docsSiteId'),
      docsSearchEnabled: this.model.get('docsSearchEnabled'),
      messagingMailboxId: this.model.get('messagingMailboxId'),
      contactFormEnabled: this.model.get('contactFormEnabled'),
      displayStyle: this.model.get('displayStyle'),
      avatars,
    })
  },

  onClose() {
    this.beaconPreview.destroy()
  },

  serializeData() {
    return _.extend(
      Marionette.Layout.prototype.serializeData.apply(this, arguments),
      {
        navTab: this.navTab,
        isDocsSiteBeacon: Boolean(this.model.get('installedSiteId')),
        showAiAnswers: this.showAiAnswers,
        isAiAnswersTidbitsEnabled: this.isAiAnswersTidbitsEnabled,
      }
    )
  },
})
