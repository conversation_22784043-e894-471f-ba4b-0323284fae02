<div class="c-app-layout">
    <div class="c-app-layout__container">
        <div class="c-app-layout__col">
            <section id="sidebar-lt" class="c-app-layout__sidebar c-app-layout__sidebar--left">
                {{#if isDocsSiteBeacon}}
                    <div class="sidebar-heading simple-title">
                        <span id="bcName">{{name}}</span>
                    </div>
                {{else}}
                    <div class="js-beacon-dropdown"></div>
                {{/if}}

                <ul class="nav nav-stacked">
                    <li {{#ifCond navTab '==' 'overview'}}class="active" {{/ifCond}}>
                      <a href="/settings/beacons/{{id}}/report/insights" data-cy="Beacon.Layout.Overview"><i>{{> ./icons/arrow-left.hbs}}</i>Overview</a>
                    </li>
                    <li {{#ifCond navTab '==' 'customize'}}class="active" {{/ifCond}}>
                        <a href="/settings/beacons/{{id}}/customize" data-cy="Beacon.Layout.Customize"><i>{{> ./icons/palette.hbs}}</i>Customize</a>
                    </li>
                    <li {{#ifCond navTab '==' 'messaging'}}class="active" {{/ifCond}}>
                        <a href="/settings/beacons/{{id}}/messaging" data-cy="Beacon.Layout.Contact"><i>{{> ./icons/envelope-closed.hbs}}</i>Contact</a>
                    </li>
                    {{#unless isDocsSiteBeacon}}
                        <li {{#ifCond navTab '==' 'docs'}}class="active" {{/ifCond}}>
                            <a href="/settings/beacons/{{id}}/docs" data-cy="Beacon.Layout.Docs"><i>{{> ./icons/page-simple-search.hbs}}</i>Docs</a>
                        </li>
                      {{#if showAiAnswers}}
                        <li {{#ifCond navTab '==' 'answers'}}class="active" {{/ifCond}}>
                          <a href="/settings/beacons/{{id}}/answers{{#if isAiAnswersTidbitsEnabled}}/configure{{/if}}" data-cy="Beacon.Layout.AiAnswers"><i>{{> ./icons/spark.hbs}}</i>AI Answers</a>
                        </li> 
                      {{/if}}
                    {{/unless}}

                    <li {{#ifCond navTab '==' 'translate'}}class="active" {{/ifCond}}>
                        <a href="/settings/beacons/{{id}}/translate" data-cy="Beacon.Layout.Translate"><i>{{> ./icons/translate.hbs}}</i>Translate</a>
                    </li>
                    {{#unless isDocsSiteBeacon}}
                        <li {{#ifCond navTab '==' 'embed'}}class="active" {{/ifCond}}>
                            <a href="/settings/beacons/{{id}}/embed/web" data-cy="Beacon.Layout.Installation"><i>{{> ./icons/code-square.hbs}}</i>Installation</a>
                        </li>
                    {{/unless}}
                </ul>

                {{#unless isDocsSiteBeacon}}
                    <a href="/settings/beacons" class="c-button c-button-action" data-cy="Beacon.Layout.AllBeacons">All Beacons</a>
                {{/unless}}
            </section>
        </div>
        <div class="c-app-layout__content">
            <section class="main-col">
                <div class="beacon-builder-settings blue-hs-app">
                    <div class="beacon-builder-settings__formWrapper js-beacon-settings"></div>
                    <div class="beacon-builder-settings__beaconWrapper {{#ifCond navTab '==' 'overview'}}hide-beacon{{/ifCond}}">
                        <div class="c-BeaconBuilderPreview">
                            <div class="c-BeaconBuilderPreview__content">
                                <div id="beacon-container" class="c-BeaconBuilderPreview__beaconEmbedWrapper"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </section>
        </div>
    </div>
</div>
