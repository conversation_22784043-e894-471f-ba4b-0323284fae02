import CustomFieldsCollection from '../../common/collections/CustomFieldsCollection'
import Controller from '../../common/components/Controller'
import App from '../App'
import BeaconRefsCollection from '../collections/BeaconRefsCollection'
import BeaconModel from '../models/BeaconModel'
import createStore from '../react/store/createStore'
import BeaconOnboardingView from '../views/BeaconOnboardingView'
import BeaconSettingsCustomizeView from '../views/BeaconSettingsCustomizeView'
import BeaconSettingsDocsView from '../views/BeaconSettingsDocsView'
import BeaconSettingsEmbedView from '../views/BeaconSettingsEmbedView'
import BeaconSettingsLayout from '../views/BeaconSettingsLayout'
import BeaconSettingsMessagingView from '../views/BeaconSettingsMessagingView'
import BeaconSettingsTranslateView from '../views/BeaconSettingsTranslateView'
import NewBeaconLayout from '../views/NewBeaconLayout'
import NoBeaconsLayout from '../views/NoBeaconsLayout'
import BeaconPreview from './BeaconPreview'
import BeaconReportView from '@beacons/views/BeaconReportView'
import BeaconSettingsAiAnswersView from '@beacons/views/BeaconSettingsAiAnswersView'
import BeaconWithReportingLayout from '@beacons/views/BeaconWithReportingLayout'

/* global $ _ appData hsGlobal */
import HS from 'hs-core'

const getCustomFields = (isCustomFieldsEnabled, mailboxes, cb) => {
  const customFields = {}

  if (!isCustomFieldsEnabled) {
    return cb(customFields)
  }

  const promises = []

  mailboxes.forEach(mailbox => {
    customFields[mailbox.id] = new CustomFieldsCollection([], {
      mailboxId: mailbox.id,
    })
    promises.push(customFields[mailbox.id].fetch())
  })

  Promise.all(promises).then(() => {
    _.each(customFields, (value, key) => {
      if (value && typeof value.toJSON === 'function') {
        customFields[key] = value.toJSON()
      }
    })

    cb(customFields)
  })
}

const setLayoutClass = enabled => {
  if (enabled) {
    $('body').addClass('one-col')
  } else {
    $('body').removeClass('one-col')
  }
}

function redirectToStandaloneMessages() {
  const messagesSubPath = window.location.pathname.split('/messages')[1]
  const overviewRegex = /\/overview\//
  const editRegex = /\/edit\/.+\//
  const newRegex = /\/new\/.+\//

  const getId = regex => messagesSubPath.split(regex)[1]
  const isPageMatching = matcher => messagesSubPath.match(matcher)
  const redirectTo = path => window.location.replace(path)

  if (isPageMatching(overviewRegex)) {
    return redirectTo(`/messages/${getId(overviewRegex)}`)
  }
  if (isPageMatching(editRegex)) {
    return redirectTo(`/messages/${getId(editRegex)}/edit`)
  }
  if (isPageMatching(newRegex)) {
    const id = getId(newRegex)
    return redirectTo(id ? `/messages/${id}/edit` : '/messages')
  }
  return redirectTo('/messages')
}

module.exports = Controller.extend({
  /**
   * A React-y way to update instance state.
   */
  setState(nextState = {}) {
    this.state = {
      ...this.state,
      ...nextState,
    }
  },

  index() {
    let view

    const beaconsCollection = new BeaconRefsCollection()

    beaconsCollection
      .fetch()
      .then(() => {
        if (beaconsCollection.length > 0) {
          view = new BeaconWithReportingLayout({
            beaconsCollection,
          })
        } else {
          view = new NoBeaconsLayout()
        }

        App.mainRegion.show(view)
      })
      .always(() => {
        HS.Utils.Main.cursorDefault()
      })

    HS.Utils.Main.cursorLoading()
  },

  /**
   * This method figures out what the default attributes of BeaconModel should be
   * when the Beacon Onboarding FF is enabled. Defaults are based on what features
   * are currently enabled and available.
   */
  getModelDefaults() {
    const { mailboxes, sites } = appData

    const defaults = {}

    if (mailboxes && mailboxes.length) {
      defaults.contactFormEnabled = true
      defaults.messagingChatEnabled = true
    }

    if (sites && sites.length) {
      defaults.docsSearchEnabled = true
    }

    return defaults
  },

  new() {
    const layout = new NewBeaconLayout({
      cdnDomain: appData.cdnDomain,
      hasOverlay: true,
      isCentered: true,
    })
    const { mailboxes, sites } = appData
    const navigate = App.appRouter.navigate
    const beaconPreview = new BeaconPreview()
    const urlParams = new URLSearchParams(window.location.search)
    const isFromSetupGuide = urlParams.get('ref') === 'onboarding'
    const modelDefaults = this.getModelDefaults()
    const model = new BeaconModel(modelDefaults)

    HS.Utils.Main.cursorLoading()
    let beaconCount = 0
    const beaconsCollection = new BeaconRefsCollection()
    beaconsCollection
      .fetch()
      .then(() => {
        beaconCount = beaconsCollection.length
      })
      .always(() => {
        HS.Utils.Main.cursorDefault()

        // beaconCount is helpful so we can provide a nice message
        // to users that exceed their count, but not necessary.
        // If a user cannot create a beacon and tries, the API
        // will respond with an error.
        // As such, load the view regardless if the fetch worked.
        const view = new BeaconOnboardingView({
          beaconCount,
          beaconLimit: appData.beaconLimit,
          beaconPreview,
          cdnDomain: appData.cdnDomain,
          canManageAccount: hsGlobal.memberPermissions.manageAccount,
          isFromSetupGuide,
          mailboxes,
          model,
          navigate,
          sites,
        })

        layout.listenTo(view, 'beacon:ready', () =>
          layout.trigger('beacon:ready')
        )
        App.mainRegion.show(layout)
        layout.formRegion.show(view)
        layout.listenTo(view, 'beacon:created', () =>
          layout.trigger('beacon:created')
        )

        setLayoutClass(true)
      })
  },

  newEmbed() {
    return HS.Utils.Main.redirectToUrl(`/settings/beacons`)
  },

  report(id) {
    const beaconModel = new BeaconModel({ id })

    setLayoutClass(true)

    $.when(beaconModel.fetch())
      .then(() => {
        const navigate = App.appRouter.navigate
        const view = new BeaconReportView({
          model: beaconModel,
          navigate,
        })
        App.mainRegion.show(view)
      })
      .always(() => {
        HS.Utils.Main.cursorDefault()
      })

    HS.Utils.Main.cursorLoading()
  },

  settings(id, navTab, embedTab) {
    const beaconsCollection = new BeaconRefsCollection()
    const beaconModel = new BeaconModel({ id })
    const beaconPreview = new BeaconPreview()

    setLayoutClass(true)

    const createBeaconStore = () => {
      const preloadedState = {
        beacon: beaconModel.toJSON(),
        companyData: {},
      }

      const extraArgument = {
        beaconPreview,
        flashError: message => HS.Utils.Main.error(message),
        flashSuccess: message => HS.Utils.Main.success(message),
      }

      return createStore(preloadedState, extraArgument)
    }

    $.when(beaconsCollection.fetch(), beaconModel.fetch())
      .then(() => {
        // We're using an old version of jQuery that doesn't support .catch()
        // so instead, pass a 'success' and a 'failure' function to .then(),
        // just so we can ignore failures and let the page load. This ensures compatability
        // betweeen old jQuery deferred and 'new' Promises
        return beaconModel.fetchAvailableAvatars().then(
          () => {
            /* ignore success */
          },
          () => {
            /* ignore failure */
          }
        )
      })
      .then(() => {
        beaconsCollection.forEach(model => {
          if (model.id === beaconModel.id) {
            model.set('dropdownActive', true)
          }
        })

        const view = new BeaconSettingsLayout({
          cdnDomain: appData.cdnDomain,
          navTab,
          model: beaconModel,
          collection: beaconsCollection,
          beaconPreview,
        })

        const navigate = App.appRouter.navigate

        let settingsView

        const renderView = settingsView => {
          App.mainRegion.show(view)
          view.settingsRegion.show(settingsView)
        }

        switch (navTab) {
          case 'overview': {
            // Invalid tab anymore, redirect back to the index
            App.appRouter.navigate(`/${id}/customize`, { trigger: true })

            return
          }

          case 'customize':
            settingsView = new BeaconSettingsCustomizeView({
              model: beaconModel,
              navigate,
              isBeaconUnbranded: appData.isBeaconUnbranded,
              beaconPreview,
            })
            renderView(settingsView)
            return

          case 'docs':
            if (beaconModel.get('installedSiteId')) {
              App.appRouter.navigate(`/${id}/customize`, { trigger: true })
            }

            settingsView = new BeaconSettingsDocsView({
              model: beaconModel,
              navigate,
              sites: appData.sites.filter(site => {
                const publicCollections = site.collections.filter(
                  collection => {
                    return collection.isPrivate !== true
                  }
                )
                return !!publicCollections.length
              }),
              beaconPreview,
            })
            renderView(settingsView)
            return

          case 'answers':
            if (!window.hsGlobal.features.isAiAnswersEnabled) {
              App.appRouter.navigate(`/${id}/customize`, { trigger: true })
            }

            settingsView = new BeaconSettingsAiAnswersView({
              model: beaconModel,
              navigate,
              beaconPreview,
              mailboxes: appData.mailboxes,
            })
            renderView(settingsView)
            return

          case 'messaging':
            getCustomFields(
              appData.isCustomFieldsEnabled,
              appData.mailboxes,
              customFields => {
                settingsView = new BeaconSettingsMessagingView({
                  beaconPreview,
                  customFields,
                  canManageAccount: hsGlobal.memberPermissions.manageAccount,
                  document: window.document,
                  isCustomFieldsEnabled: appData.isCustomFieldsEnabled,
                  mailboxes: appData.mailboxes,
                  member: appData.member,
                  model: beaconModel,
                  navigate,
                })
                renderView(settingsView)
              }
            )
            return

          case 'messages': {
            redirectToStandaloneMessages()
            return
          }

          case 'translate': {
            settingsView = new BeaconSettingsTranslateView({
              store: createBeaconStore(),
            })
            renderView(settingsView)
            return
          }

          case 'embed': {
            if (beaconModel.get('installedSiteId')) {
              App.appRouter.navigate(`/${id}/customize`, { trigger: true })
            }

            settingsView = new BeaconSettingsEmbedView({
              store: createBeaconStore(),
            })
            renderView(settingsView)
            return
          }

          default:
            // Invalid tab, redirect back to the index
            App.appRouter.navigate(`/${id}/customize`, { trigger: true })

            return
        }
      })
      .always(() => {
        HS.Utils.Main.cursorDefault()
      })

    HS.Utils.Main.cursorLoading()
  },

  settingsOverview(id) {
    this.settings(id, 'overview')
  },

  settingsCustomize(id) {
    this.settings(id, 'customize')
  },

  settingsDocs(id) {
    this.settings(id, 'docs')
  },

  settingsAnswers(id) {
    this.settings(id, 'answers')
  },

  settingsMessages(id) {
    this.settings(id, 'messages')
  },

  settingsMessaging(id) {
    this.settings(id, 'messaging')
  },

  settingsTranslate(id) {
    this.settings(id, 'translate')
  },

  settingsEmbed(id) {
    this.settings(id, 'embed')
  },
})
