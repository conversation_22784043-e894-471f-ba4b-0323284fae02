import AppRouter from '../../common/components/AppRouter'
import { combineAppRoutes } from '@helpscout/brigade'

const marionetteRoutes = {
  '(/)': 'index',
  'new(/)': 'new',
  'new/:id(/)': 'newEmbed',
  ':id/customize(/)': 'settingsCustomize',
  ':id/docs(/)': 'settingsDocs',
  ':id/answers(/)': 'settingsAnswers',
  ':id/embed(/)': 'settingsEmbed',
  ':id/messages(/)': 'settingsMessages',
  ':id/messaging(/)': 'settingsMessaging',
  ':id/translate(/)': 'settingsTranslate',
  ':id/report(/)': 'report',
}

const reactRoutes = {
  ':id/messages': 'settingsMessages',
  ':id/embed': 'settingsEmbed',
  ':id/report': 'report',
  ':id/answers': 'settingsAnswers',
}

module.exports = AppRouter.extend({
  appRoutes: combineAppRoutes({ marionetteRoutes, reactRoutes }),
})
