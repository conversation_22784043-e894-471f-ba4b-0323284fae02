/* global App */
import ReactApp from '../react/App'
import { ReactView } from '@helpscout/brigade'
import Marionette from 'marionette'
import React from 'react'

const TOGGLE_MESSAGE_KEY = 'toggleMessage'

const AccountDropdownView = Marionette.ItemView.extend({
  className: 'c-AccountDropdown',

  initialize({ pusher, presence, hsAppClient }) {
    Object.assign(this, { pusher, presence, hsAppClient })

    this.handleOnUpdateChatAvailabilityStatus =
      this.handleOnUpdateChatAvailabilityStatus.bind(this)
    this.handleOnSelectLogOut = this.handleOnSelectLogOut.bind(this)

    this.renderOnRouterChange()
    this.listenForChatStatus()

    this.mailboxes = []

    this.fetchMailboxes().then(({ mailboxes }) => {
      this.mailboxes = mailboxes
      this.render()
    })

    this.presence.setChatStatus(
      this.model.get('chatStatus'),
      this.model.get('chatCount')
    )

    const toggleMessage = HS.Utils.SessionStorage.get(TOGGLE_MESSAGE_KEY)
    if (toggleMessage) {
      HS.Utils.Main.success(toggleMessage)
      HS.Utils.SessionStorage.deleteKey(TOGGLE_MESSAGE_KEY)
    }
  },

  listenForChatStatus() {
    const pusherChannel = this.pusher.channelName(`users.${this.model.id}`)

    this.listenTo(
      this.model,
      'change:chatStatus',
      this.handleStatusChange.bind(this)
    )
    this.listenTo(
      this.model,
      'change:chatCount',
      this.handleStatusChange.bind(this)
    )

    this.pusher.subscribe(pusherChannel, 'agent-chat-count-changed', data =>
      this.handleChatCountChangedEvent(data)
    )

    this.pusher.subscribe(pusherChannel, 'agent-chat-status-changed', data => {
      this.handleChatStatusChangedEvent(data)
    })
  },

  handleChatCountChangedEvent({ data: { count: chatCount } }) {
    this.model.set({ chatCount })
  },

  handleChatStatusChangedEvent({ data: { chatStatus } }) {
    this.model.set({ chatStatus })
  },

  handleStatusChange() {
    this.presence.setChatStatus(
      this.model.get('chatStatus'),
      this.model.get('chatCount')
    )
    this.render()
  },

  fetchMailboxes() {
    return HS.Utils.Ajax.get({ url: `${hsGlobal.apiPath}mailboxes` })
  },

  template() {
    const isLightUser = this.model.get('isLightUser')
    const accountOwnerName = this.model.get('accountOwnerName')
    const isAdmin = this.model.get('isAdmin')
    const chatEnabledMailboxes = this.getChatEnabledMailboxes()
    const isChatEnabled =
      hsGlobal.features.isChatEnabled &&
      hsGlobal.memberPermissions.chatWithCustomers &&
      Object.keys(chatEnabledMailboxes).length > 0
    const isDisconnected = this.model.get('isDisconnected')
    const showReferralShareLink = this.model.get('showReferralShareLink')
    const chatAvailabilityStatusData = this.model.get('chatStatus') || {}
    const photoUrl = this.model.get('photoUrl')
    const userId = this.model.get('id')

    return (
      <ReactApp
        {...this.model.toJSON()}
        accountOwnerName={accountOwnerName}
        chatAvailabilityStatusData={chatAvailabilityStatusData}
        flashError={() =>
          HS.Utils.Main.error(
            'There was a issue saving your status, please try again.'
          )
        }
        flashSuccess={() =>
          HS.Utils.Main.success(`Your status was successfully updated`)
        }
        isAccountAdmin={isAdmin}
        isChatEnabled={isChatEnabled}
        isDisconnected={isDisconnected}
        isLightUser={isLightUser}
        isProfileRouteActive={isProfileRouteActive(userId)}
        isPlanRouteActive={isPlanRouteActive()}
        mailboxes={chatEnabledMailboxes}
        memberPermissions={hsGlobal.memberPermissions}
        onUpdateChatAvailabilityStatus={
          this.handleOnUpdateChatAvailabilityStatus
        }
        onSelectLogOut={this.handleOnSelectLogOut}
        onSelectReferHelpScout={this.handleOnSelectReferHelpScout}
        // Required type-conversion: When undefined the server returns
        // an un expected value of `false` instead of an empty string.
        photoUrl={photoUrl ? photoUrl : ''}
        rootPath={hsGlobal.path}
        showReferralShareLink={showReferralShareLink}
      />
    )
  },

  getChatEnabledMailboxes() {
    const chatMailboxIds = hsGlobal.chatMailboxIds
    const chatEnabledMailboxes = {}

    this.mailboxes.forEach(mailbox => {
      const mailboxId = mailbox.id
      const isChatEnabled = chatMailboxIds.find(id => id === mailboxId)

      if (!isChatEnabled) {
        return
      }

      chatEnabledMailboxes[mailboxId] = mailbox
    })

    return chatEnabledMailboxes
  },

  handleOnUpdateChatAvailabilityStatus(statusData) {
    return this.hsAppClient
      .put('beacon-chat/mailbox-status', statusData)
      .then(() => {
        this.model.set('chatStatus', statusData)
        return Promise.resolve()
      })
  },

  handleOnSelectReferHelpScout(_item, _e) {
    window.open('https://www.helpscout.com/referral/', '_blank')
  },

  logoutRedirect() {
    window.location = '/members/process/logout?jump=' + window.location.href
  },

  handleOnSelectLogOut(_item) {
    this.presence
      .logout()
      .then(this.logoutRedirect)
      .catch(error => {
        console.error(error)
        this.logoutRedirect()
      })
  },

  renderOnRouterChange() {
    if (!App || !App.appRouter) return

    App.appRouter.on('route', (_route, _params) => {
      this.render()
    })
  },
})

function routeContains(match = '') {
  const route = window.location.pathname
  return route.indexOf(match) >= 0
}

function isProfileRouteActive(id = '') {
  return routeContains('/users') && routeContains(`/${id}`)
}

function isPlanRouteActive() {
  return (
    routeContains('/members/available-plans') || routeContains('/members/plan')
  )
}

export default ReactView(AccountDropdownView)
