import {
  CHAT_AVAILABILITY_MENU_TYPE,
  REFER_HELP_SCOUT_TYPE,
  LOGOUT_MENU_TYPE,
  LINK_MENU_TYPE,
  LIGHT_USER_MENU_TYPE,
} from '../../App/App'
import AccountDropdown, { LIST_ITEM_NODE } from './AccountDropdown'
import { buildDropdownItems } from './AccountDropdown.utils'
import { render, waitFor } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import React from 'react'

describe('buildDropdownItems', () => {
  test('builds correct Link Menu Items ', () => {
    const items = buildDropdownItems({
      canManageAccount: true,
      hasMultipleMailboxes: false,
      isAccountAdmin: true,
      isChatEnabled: false,
      isLightUser: false,
      isPlanRouteActive: false,
      isProfileRouteActive: true, // <----
      rootPath: '/hhhh',
      showReferralShareLink: true,
      userId: 123,
    })

    items
      .filter(item => item.menuType === LINK_MENU_TYPE)
      .forEach(item => {
        if (item.value === 'profile') {
          // url
          expect(item.href).toBe('/hhhhusers/profile/123')
          // route active field
          expect(item.isRouteActive).toBe(true)
        } else if (item.value === 'plan') {
          // url
          expect(item.href).toBe('/hhhhmembers/plan')
          // route active field
          expect(item.isRouteActive).toBe(false)
        }
      })
  })

  test('builds correct chat availability items ', () => {
    const items = buildDropdownItems({
      canManageAccount: true,
      hasMultipleMailboxes: false,
      isAccountAdmin: true,
      isChatEnabled: true, // <----
      isLightUser: false,
      isPlanRouteActive: false,
      isProfileRouteActive: true,
      rootPath: '/hhhh',
      showReferralShareLink: true,
      userId: 123,
    })

    expect(items[0].type).toBe('group')
    expect(items[0].label).toBe('Chat Availability')
    expect(items[1].type).toBe('divider')

    const chatItems = items[0].items

    expect(chatItems.length).toBe(3)

    chatItems.forEach((item, index) => {
      if (item[index] === 0) {
        expect(item.label).toBe('Available')
        expect(item.value).toBe('available')
        expect(item.meta).toBe('Visible to everyone')
      } else if (item[index] === 1) {
        expect(item.label).toBe('Assign Onlye')
        expect(item.value).toBe('assign')
        expect(item.meta).toBe('Hidden to visitors')
      } else if (item[index] === 2) {
        expect(item.label).toBe('Unavailable')
        expect(item.value).toBe('unavailable')
        expect(item.meta).toBe('Not visible to anyone')
      }
    })
  })

  test('adds custom chat availability item ', () => {
    const items = buildDropdownItems({
      canManageAccount: true,
      hasMultipleMailboxes: true, // <----
      isAccountAdmin: true,
      isChatEnabled: true,
      isLightUser: false,
      isPlanRouteActive: false,
      isProfileRouteActive: true,
      rootPath: '/hhhh',
      showReferralShareLink: true,
      userId: 123,
    })

    expect(items[0].type).toBe('group')
    expect(items[0].label).toBe('Chat Availability')
    expect(items[1].type).toBe('divider')

    const chatItems = items[0].items

    expect(chatItems.length).toBe(4)
    expect(chatItems[3].label).toBe('Custom')
    expect(chatItems[3].value).toBe('custom')
    expect(chatItems[3].meta).toBe('Custom availability by Inbox')
  })

  test('build menu for light user', () => {
    const items = buildDropdownItems({
      canManageAccount: false,
      hasMultipleMailboxes: false,
      isAccountAdmin: false,
      isChatEnabled: false,
      isLightUser: true, // <---
      isPlanRouteActive: false,
      isProfileRouteActive: false,
      rootPath: '/hhhh',
      showReferralShareLink: true,
      userId: 123,
    })
    expect(items[0].menuType).toBe(LIGHT_USER_MENU_TYPE)
    expect(items[0].label).toBe('Light User')
    expect(items[0].meta).toBe('Your permissions are limited')

    expect(items[1].type).toBe('divider')
    expect(items[2].menuType).toBe(LINK_MENU_TYPE)
    expect(items[3].menuType).toBe(REFER_HELP_SCOUT_TYPE)
    expect(items[4].menuType).toBe(LOGOUT_MENU_TYPE)
  })
})

describe('AccountDropdown component', () => {
  test('should render Avatar with initials', () => {
    const { container } = render(<AccountDropdown initials="MH" />)

    expect(container.querySelector('.c-Avatar')).toBeTruthy()
    expect(container.querySelector('.c-Avatar').textContent).toBe('M')
  })

  test('should render Avatar with photo', () => {
    const { container } = render(
      <AccountDropdown initials="MH" photoUrl="http://image.jpeg" />
    )

    expect(container.querySelector('.c-Avatar')).toBeTruthy()
    expect(container.querySelector('.c-Avatar__imageWrapper')).toBeTruthy()
    expect(
      container.querySelector('.c-Avatar__imageWrapper').getAttribute('src')
    ).toBe('http://image.jpeg')
  })

  test('should have the plan, profile, referral and logout items', async () => {
    const { getAllByRole, getByRole } = render(
      <AccountDropdown
        initials="MH"
        dropdownItems={buildDropdownItems({
          canManageAccount: true,
          hasMultipleMailboxes: false,
          isAccountAdmin: true,
          isChatEnabled: false,
          isLightUser: false,
          isPlanRouteActive: false,
          isProfileRouteActive: false,
          rootPath: '/hhhh',
          showReferralShareLink: true,
          userId: 123,
        })}
      />
    )

    await userEvent.click(getByRole('button'))

    await waitFor(() => {
      expect(getAllByRole('option').length).toBe(4)

      getAllByRole('option').forEach((option, index) => {
        if (index === 0) {
          expect(option.textContent).toBe('Your Profile')
        } else if (index === 1) {
          expect(option.textContent).toBe('Your Plan')
        } else if (index === 2) {
          expect(option.textContent).toBe('Refer a Friend 🎁')
        } else if (index === 3) {
          expect(option.textContent).toBe('Log Out')
        }
      })
    })
  })

  test('should have the chat availability items', async () => {
    const { getAllByRole, getByRole } = render(
      <AccountDropdown
        initials="MH"
        dropdownItems={buildDropdownItems({
          canManageAccount: true,
          hasMultipleMailboxes: true,
          isAccountAdmin: true,
          isChatEnabled: true,
          isLightUser: false,
          isPlanRouteActive: false,
          isProfileRouteActive: false,
          rootPath: '/hhhh',
          showReferralShareLink: false,
          userId: 123,
        })}
      />
    )

    await userEvent.click(getByRole('button'))

    await waitFor(() => {
      expect(getAllByRole('option').length).toBe(7)

      getAllByRole('option').forEach((option, index) => {
        if (index === 0) {
          expect(option.textContent.includes('Available')).toBeTruthy()
          expect(
            option.textContent.includes('Visible to everyone')
          ).toBeTruthy()
        } else if (index === 1) {
          expect(option.textContent.includes('Assign Only')).toBeTruthy()
          expect(option.textContent.includes('Hidden to visitors')).toBeTruthy()
        } else if (index === 2) {
          expect(option.textContent.includes('Unavailable')).toBeTruthy()
          expect(
            option.textContent.includes('Not visible to anyone')
          ).toBeTruthy()
        } else if (index === 3) {
          expect(option.textContent.includes('Custom')).toBeTruthy()
          expect(
            option.textContent.includes('Custom availability by Inbox')
          ).toBeTruthy()
        } else if (index === 4) {
          expect(option.textContent).toBe('Your Profile')
        } else if (index === 5) {
          expect(option.textContent).toBe('Your Plan')
        } else if (index === 6) {
          expect(option.textContent).toBe('Log Out')
        }
      })
    })
  })

  test('open url gets called on profile item', async () => {
    const { getAllByRole, getByRole } = render(
      <AccountDropdown
        actions={{
          [CHAT_AVAILABILITY_MENU_TYPE]: () => {},
          [REFER_HELP_SCOUT_TYPE]: () => {},
          [LOGOUT_MENU_TYPE]: () => {},
        }}
        initials="MH"
        dropdownItems={buildDropdownItems({
          canManageAccount: true,
          hasMultipleMailboxes: false,
          isAccountAdmin: true,
          isChatEnabled: false,
          isLightUser: false,
          isPlanRouteActive: false,
          isProfileRouteActive: false,
          rootPath: '/hhhh',
          showReferralShareLink: true,
          userId: 123,
        })}
      />
    )

    const originalOpen = window.open
    const mockedOpen = jest.fn()
    window.open = mockedOpen

    await userEvent.click(getByRole('button'))

    await userEvent.click(
      getAllByRole('option')[0].querySelector(`.${LIST_ITEM_NODE}`),
      {
        keyboardState: await userEvent.keyboard('{Meta>}'), // simulate control/command on click to trigger window.open
      }
    )

    expect(mockedOpen).toHaveBeenCalledWith('/hhhhusers/profile/123')

    window.open = originalOpen
  })

  test('open url gets called on plan item', async () => {
    const { getAllByRole, getByRole } = render(
      <AccountDropdown
        actions={{
          [CHAT_AVAILABILITY_MENU_TYPE]: () => {},
          [REFER_HELP_SCOUT_TYPE]: () => {},
          [LOGOUT_MENU_TYPE]: () => {},
        }}
        initials="MH"
        dropdownItems={buildDropdownItems({
          canManageAccount: true,
          hasMultipleMailboxes: false,
          isAccountAdmin: true,
          isChatEnabled: false,
          isLightUser: false,
          isPlanRouteActive: false,
          isProfileRouteActive: false,
          rootPath: '/hhhh',
          showReferralShareLink: true,
          userId: 123,
        })}
      />
    )

    const originalOpen = window.open
    const mockedOpen = jest.fn()
    window.open = mockedOpen

    await userEvent.click(getByRole('button'))

    await userEvent.click(
      getAllByRole('option')[1].querySelector(`.${LIST_ITEM_NODE}`),
      {
        keyboardState: await userEvent.keyboard('{Meta>}'), // simulate control/command on click to trigger window.open
      }
    )

    expect(mockedOpen).toHaveBeenCalledWith('/hhhhmembers/plan')

    window.open = originalOpen
  })

  test('actions get called on light user item', async () => {
    const lightUserFn = jest.fn()

    const { getAllByRole, getByRole } = render(
      <AccountDropdown
        actions={{
          [LIGHT_USER_MENU_TYPE]: lightUserFn,
          [REFER_HELP_SCOUT_TYPE]: jest.fn(),
          [LOGOUT_MENU_TYPE]: jest.fn(),
        }}
        initials="MH"
        dropdownItems={buildDropdownItems({
          canManageAccount: false,
          hasMultipleMailboxes: false,
          isAccountAdmin: false,
          isChatEnabled: false,
          isLightUser: true,
          isPlanRouteActive: false,
          isProfileRouteActive: false,
          rootPath: '/hhhh',
          showReferralShareLink: true,
          userId: 123,
        })}
      />
    )

    await userEvent.click(getByRole('button'))

    const availableItem = getAllByRole('option')[0]

    await userEvent.click(availableItem)
    expect(lightUserFn).toHaveBeenCalled()
  })

  test('actions get called on chat items', async () => {
    jest.setTimeout(15000)
    const chatFn = jest.fn()
    const shareFn = jest.fn()
    const logoutFn = jest.fn()

    const { getAllByRole, getByRole } = render(
      <AccountDropdown
        actions={{
          [CHAT_AVAILABILITY_MENU_TYPE]: chatFn,
          [REFER_HELP_SCOUT_TYPE]: shareFn,
          [LOGOUT_MENU_TYPE]: logoutFn,
        }}
        initials="MH"
        dropdownItems={buildDropdownItems({
          canManageAccount: true,
          hasMultipleMailboxes: false,
          isAccountAdmin: true,
          isChatEnabled: true,
          isLightUser: false,
          isPlanRouteActive: false,
          isProfileRouteActive: false,
          rootPath: '/hhhh',
          showReferralShareLink: true,
          userId: 123,
        })}
      />
    )

    await userEvent.click(getByRole('button'))

    const availableItem = getAllByRole('option')[0]

    await userEvent.click(availableItem)
    expect(chatFn).toHaveBeenCalled()

    await userEvent.click(getByRole('button'))

    const assignItem = getAllByRole('option')[1]

    await userEvent.click(assignItem)
    expect(chatFn).toHaveBeenCalled()

    await userEvent.click(getByRole('button'))

    const notAvailableItem = getAllByRole('option')[2]

    await userEvent.click(notAvailableItem)
    expect(chatFn).toHaveBeenCalled()

    await userEvent.click(getByRole('button'))

    const shareLoveItem = getAllByRole('option')[5]

    await userEvent.click(shareLoveItem)
    expect(shareFn).toHaveBeenCalled()

    await userEvent.click(getByRole('button'))

    const logoutItem = getAllByRole('option')[6]

    await userEvent.click(logoutItem)
    expect(logoutFn).toHaveBeenCalled()
  })

  jest.setTimeout(5000)
})
