import Flexy from 'hsds/components/flexy'
import { getColor } from 'hsds/utils/color'
import styled from 'styled-components'

export const AvailabilityHeaderUI = styled(Flexy)`
  border-bottom: 1px solid ${getColor('charcoal.300')};
  height: 50px;
`

export const AvailabilityContentUI = styled('div')`
  padding: 20px;
  width: 100%;

  .c-ChoiceInput {
    cursor: pointer;
  }
`

export const FlexyUI = styled(Flexy)`
  border-bottom: 1px solid ${getColor('charcoal.300')};
  height: 40px;

  &:last-of-type {
    border-bottom: none;
  }

  ${({ withBackgroundShading }) =>
    withBackgroundShading && `background-color: ${getColor('charcoal.100')}`};

  .c-FormGroupChoice {
    margin: 0;
    width: 100%;
  }
`

export const LeftFlexyColumnUI = styled(Flexy.Block)`
  padding-left: 10px;
`

export const StatusColumnUI = styled(Flexy.Item)`
  text-align: center;
  width: 100px;

  .c-StatusDot {
    margin: 0 auto 5px;
  }
`
