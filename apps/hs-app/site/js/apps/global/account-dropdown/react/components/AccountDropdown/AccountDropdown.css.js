import Avatar from 'hsds/components/avatar'
import { getToken } from 'hsds/tokens'
import { getColor } from 'hsds/utils/color'
import styled from 'styled-components'

export const AccountDropdownUI = styled('div')`
  .DropList__MenuList {
    max-height: 650px;
  }

  &.is-light-user {
    .c-Avatar__status {
      &:before {
        content: '';
        width: 14px;
        height: 14px;
        background: ${getColor('cobalt.800')};
        position: absolute;
        right: -3px;
        border-radius: 3px;
        bottom: -3px;
      }
    }
  }
`

export const AccountDropdownTogglerUI = styled('button')`
  border: 0;
  padding: 0;
  outline: 0;
  border-radius: 50%;
  background-color: transparent;

  &:focus {
    outline: 0;

    .c-Avatar__outerBorder {
      border-color: ${getColor('cobalt.400')};
    }
  }
`

export const AvatarUI = styled(Avatar)`
  .is-assign,
  .is-custom,
  .is-unavailable {
    color: ${getColor('cobalt.800')} !important;
  }
`

export const ListItemUI = styled('div')`
  width: 100%;
  /* Add 5 pixels to default DropListItem padding 10 + 5 */
  li:has(&) {
    --hsds-drop-list-item-padding-left: 15px;
    --hsds-drop-list-item-padding-right: 15px;
  }
  /* Add 5 pixels to default DropListItem padding 11 + 5 */
  li:has(&.LIGHT_USER_MENU_TYPE, &.CHAT_AVAILABILITY_MENU_TYPE) {
    --hsds-drop-list-item-padding-top: 16px;
    --hsds-drop-list-item-padding-bottom: 16px;
  }

  /* these are the "selected" styles from a normal DropList item,
  we select the parent li and apply them there when the route is active */
  li:has(&.is-active-route) {
    --hsds-drop-list-item-icon-color: white;
    --hsds-drop-list-item-color: white;
    --hsds-drop-list-item-background-color: ${getToken(
      'dropList.color.listItem.selected.background'
    )};
    --hsds-drop-list-item-title-color: ${getToken(
      'dropList.color.listItem.selected.title'
    )};
    --hsds-drop-list-item-subtitle-color: ${getToken(
      'dropList.color.listItem.selected.subtitle'
    )};

    font-weight: 500;
  }

  li:has(&.LIGHT_USER_MENU_TYPE) {
    --hsds-drop-list-item-background-color: ${getColor('purple.100')};

    &:hover {
      --hsds-drop-list-item-background-color: ${getColor('purple.200')};
    }

    .c-Icon {
      color: ${getToken('dropList.color.listItem.icon.default')};
    }
  }
`

export const ItemContentUI = styled('div')`
  width: 100%;
  line-height: 18px;
`

export const ItemContentTextUI = styled('div')`
  color: ${getToken('dropList.color.listItem.title')};
  font-weight: 500;

  &.ItemContentMetaText {
    color: ${getToken('dropList.color.listItem.subtitle')};
    font-weight: 400;
  }

  .is-selected & {
    color: ${getToken('dropList.color.listItem.selected.title')};
  }

  .is-selected &.ItemContentMetaText {
    color: ${getToken('dropList.color.listItem.selected.subtitle')};
  }
`
