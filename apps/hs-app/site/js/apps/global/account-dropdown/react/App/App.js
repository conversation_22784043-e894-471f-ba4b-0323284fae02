import { CHAT_STATUS_ASSIGN, CHAT_STATUS_CUSTOM } from '../../constants'
import AccountDropdown from '../components/AccountDropdown'
import { buildDropdownItems } from '../components/AccountDropdown/AccountDropdown.utils'
import CustomAvailabilityModal from '../components/CustomAvailabilityModal'
import LightUserPermissionModal from '../components/LightUserPermissionModal'
import { getCurrentTheme } from '@common/utils/theme'
import { Provider as HSDSProvider } from 'hsds/components/hsds'
import PropTypes from 'prop-types'
import React, { useState, Fragment } from 'react'

export const LIGHT_USER_MENU_TYPE = 'LIGHT_USER_MENU_TYPE'
export const CHAT_AVAILABILITY_MENU_TYPE = 'CHAT_AVAILABILITY_MENU_TYPE'
export const REFER_HELP_SCOUT_TYPE = 'REFER_HELP_SCOUT_TYPE'
export const LINK_MENU_TYPE = 'LINK_MENU_TYPE'
export const LOGOUT_MENU_TYPE = 'LOGOUT_MENU_TYPE'

function App({
  accountOwnerName,
  chatAvailabilityStatusData = { status: CHAT_STATUS_ASSIGN, mailboxes: {} },
  flashError = () => {},
  flashSuccess = () => {},
  id: userId,
  initials = '',
  isChatEnabled = false,
  isAccountAdmin,
  isLightUser,
  isDisconnected,
  isPlanRouteActive = false,
  isProfileRouteActive = false,
  mailboxes = {},
  memberPermissions,
  onSelectLogOut = () => {},
  onSelectReferHelpScout = () => {},
  onUpdateChatAvailabilityStatus = () => {},
  photoUrl = '',
  rootPath,
  showReferralShareLink = false,
}) {
  const [isCustomModalOpen, setIsCustomModalOpen] = useState(false)
  const [isLightUserModalOpen, setIsLightUserModalOpen] = useState(false)
  const hasMultipleMailboxes = Object.keys(mailboxes).length > 1
  const themeName = getCurrentTheme()

  function onSelectChatAvailabilityItem(item) {
    if (item.value === CHAT_STATUS_CUSTOM) {
      setIsCustomModalOpen(true)
      return
    }

    onUpdateChatAvailabilityStatus({ status: item.value })
      .then(flashSuccess)
      .catch(flashError)
  }

  function onSelectLightUser() {
    setIsLightUserModalOpen(true)
  }

  function handleOnCloseLightUserModal() {
    setIsLightUserModalOpen(false)
  }

  function handleOnCloseCustomModal() {
    setIsCustomModalOpen(false)
  }

  function handleOnSaveCustomStatus(statusData) {
    return onUpdateChatAvailabilityStatus(statusData)
      .then(() => {
        setIsCustomModalOpen(false)
        flashSuccess()
      })
      .catch(flashError)
  }

  function renderAccountDropdown() {
    const items = buildDropdownItems({
      canManageAccount: memberPermissions.manageAccount,
      hasMultipleMailboxes,
      isAccountAdmin,
      isChatEnabled,
      isLightUser,
      isPlanRouteActive,
      isProfileRouteActive,
      rootPath,
      showReferralShareLink,
      userId,
    })

    return (
      <AccountDropdown
        actions={{
          [CHAT_AVAILABILITY_MENU_TYPE]: onSelectChatAvailabilityItem,
          [REFER_HELP_SCOUT_TYPE]: onSelectReferHelpScout,
          [LOGOUT_MENU_TYPE]: onSelectLogOut,
          [LIGHT_USER_MENU_TYPE]: onSelectLightUser,
        }}
        chatStatus={chatAvailabilityStatusData.status}
        dropdownItems={items}
        initials={initials}
        isChatEnabled={isChatEnabled}
        isDisconnected={isDisconnected}
        isLightUser={isLightUser}
        photoUrl={photoUrl}
        shouldRefocusOnClose={!isCustomModalOpen}
        accountOwnerName={accountOwnerName}
      />
    )
  }

  function renderCustomAvailabilityModal() {
    return (
      <CustomAvailabilityModal
        chatAvailabilityStatusData={chatAvailabilityStatusData}
        isOpen={isCustomModalOpen}
        mailboxes={mailboxes}
        onClose={handleOnCloseCustomModal}
        onSave={handleOnSaveCustomStatus}
      />
    )
  }

  function renderLightUsersModal() {
    return (
      <LightUserPermissionModal
        isOpen={isLightUserModalOpen}
        onClose={handleOnCloseLightUserModal}
        accountOwnerName={accountOwnerName}
        memberPermissions={memberPermissions}
      />
    )
  }

  return (
    <HSDSProvider scope="hsds-react" themeName={themeName}>
      <Fragment>
        {renderAccountDropdown()}
        {hasMultipleMailboxes ? renderCustomAvailabilityModal() : null}
        {isLightUser ? renderLightUsersModal() : null}
      </Fragment>
    </HSDSProvider>
  )
}

App.propTypes = {
  accountOwnerName: PropTypes.string,
  chatAvailabilityStatusData: PropTypes.shape({
    status: PropTypes.string.isRequired,
    mailboxes: PropTypes.oneOfType([PropTypes.object, PropTypes.array]),
  }),
  flashError: PropTypes.func,
  flashSuccess: PropTypes.func,
  id: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
  initials: PropTypes.string,
  isAccountAdmin: PropTypes.bool,
  isChatEnabled: PropTypes.bool,
  isDisconnected: PropTypes.bool,
  isLightUser: PropTypes.bool,
  isPlanRouteActive: PropTypes.bool,
  isProfileRouteActive: PropTypes.bool,
  mailboxes: PropTypes.oneOfType([PropTypes.object, PropTypes.array]),
  memberPermissions: PropTypes.object,
  onUpdateChatAvailabilityStatus: PropTypes.func,
  onSelectLogOut: PropTypes.func,
  onSelectReferHelpScout: PropTypes.func,
  photoUrl: PropTypes.string,
  rootPath: PropTypes.string,
  showReferralShareLink: PropTypes.bool,
}

export default App
