import {
  CHAT_STATUS_ASSIGN,
  CHAT_STATUS_AVAILABLE,
  CHAT_STATUS_CUSTOM,
  CHAT_STATUS_UNAVAILABLE,
} from '../../../constants'
import {
  CHAT_AVAILABILITY_MENU_TYPE,
  LINK_MENU_TYPE,
  REFER_HELP_SCOUT_TYPE,
  LOGOUT_MENU_TYPE,
  LIGHT_USER_MENU_TYPE,
} from '../../App/App'

export function buildDropdownItems({
  canManageAccount,
  hasMultipleMailboxes,
  isAccountAdmin,
  isChatEnabled,
  isPlanRouteActive,
  isProfileRouteActive,
  rootPath,
  showReferralShareLink,
  userId,
  isLightUser,
}) {
  let items = []

  if (isChatEnabled) {
    items = items.concat(getChatAvailabilityItems(hasMultipleMailboxes))
  }

  if (isLightUser) {
    items = items.concat([
      {
        label: 'Light User',
        meta: 'Your permissions are limited',
        menuType: LIGHT_USER_MENU_TYPE,
        value: 'light-user',
      },
      {
        type: 'divider',
      },
    ])
  }

  items = items.concat({
    isRouteActive: isProfileRouteActive,
    label: 'Your Profile',
    href: `${rootPath}users/profile/${userId}`,
    menuType: LINK_MENU_TYPE,
    value: 'profile',
  })

  if (isAccountAdmin || canManageAccount) {
    items = items.concat({
      isRouteActive: isPlanRouteActive,
      label: 'Your Plan',
      href: `${rootPath}members/plan`,
      menuType: LINK_MENU_TYPE,
      value: 'plan',
    })
  }

  if (showReferralShareLink) {
    items = items.concat({
      value: 'share',
      menuType: REFER_HELP_SCOUT_TYPE,
    })
  }

  items = items.concat({
    label: 'Log Out',
    menuType: LOGOUT_MENU_TYPE,
    value: 'log-out',
  })

  return items
}

function getChatAvailabilityItems(hasMultipleMailboxes) {
  const chatStatusItems = [
    {
      items: [
        {
          label: 'Available',
          menuType: CHAT_AVAILABILITY_MENU_TYPE,
          meta: 'Visible to everyone',
          value: CHAT_STATUS_AVAILABLE,
        },
        {
          label: 'Assign Only',
          menuType: CHAT_AVAILABILITY_MENU_TYPE,
          meta: 'Hidden to visitors',
          value: CHAT_STATUS_ASSIGN,
        },
        {
          label: 'Unavailable',
          menuType: CHAT_AVAILABILITY_MENU_TYPE,
          meta: 'Not visible to anyone',
          value: CHAT_STATUS_UNAVAILABLE,
        },
      ],
      label: 'Chat Availability',
      type: 'group',
    },
    {
      type: 'divider',
    },
  ]

  if (hasMultipleMailboxes) {
    chatStatusItems[0].items.push({
      label: 'Custom',
      menuType: CHAT_AVAILABILITY_MENU_TYPE,
      meta: 'Custom availability by Inbox',
      value: CHAT_STATUS_CUSTOM,
    })
  }

  return chatStatusItems
}

export function openUrl(e, url, onClick) {
  if (e) e.preventDefault()
  if (onClick) onClick()

  if (!url) {
    return
  } else if (e && e.metaKey) {
    window.open(url)
  } else {
    window.location = url
  }
}

export function getSelectedItem({ dropdownItems, chatStatus, isChatEnabled }) {
  if (isChatEnabled) {
    return dropdownItems[0].items.find(item => item.value === chatStatus)
  }

  return null
}
