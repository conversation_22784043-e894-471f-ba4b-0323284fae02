import { CHAT_STATUS_CUSTOM, CHAT_STATUS_UNAVAILABLE } from '../../../constants'
import {
  AvailabilityContentUI,
  AvailabilityHeaderUI,
  FlexyUI,
  LeftFlexyColumnUI,
  StatusColumnUI,
} from './CustomAvailabilityModal.css'
import { ChoiceGroup } from 'hsds/components/choice'
import Flexy from 'hsds/components/flexy'
import KeypressListener from 'hsds/components/keypress-listener'
import Modal from 'hsds/components/modal'
import Radio from 'hsds/components/radio'
import StatusDot from 'hsds/components/status-dot'
import Text from 'hsds/components/text'
import { Keys } from 'hsds/utils/keyboard'
import cloneDeep from 'lodash.clonedeep'
import PropTypes from 'prop-types'
import React, { Component } from 'react'
import { capitalize } from 'underscore.string'

class CustomAvailabilityModal extends Component {
  static propTypes = {
    chatAvailabilityStatusData: PropTypes.shape({
      status: PropTypes.string.isRequired,
      mailboxes: PropTypes.oneOfType([PropTypes.object, PropTypes.array]),
    }),
    isOpen: PropTypes.bool,
    mailboxes: PropTypes.oneOfType([PropTypes.object, PropTypes.array]),
    onClose: PropTypes.func,
    onSave: PropTypes.func,
  }

  static defaultProps = {
    chatAvailabilityStatusData: {},
    isOpen: false,
    mailboxes: {},
    onClose: () => {},
    onSave: () => {},
  }

  state = {
    pendingChatAvailabilityPerMailboxes: {},
    pendingSelectedStatus: '',
    isCustomModalSaveButtonDisabled: false,
    isSaveButtonDisabled: false,
  }

  UNSAFE_componentWillMount() {
    this.buildState(this.props)
  }

  UNSAFE_componentWillReceiveProps(nextProps) {
    this.buildState(nextProps)
  }

  buildState(props) {
    const {
      chatAvailabilityStatusData: { mailboxes: statusDataByMailbox, status },
      mailboxes,
    } = props

    const state = {
      pendingSelectedStatus: status,
      isCustomModalSaveButtonDisabled: false,
      isSaveButtonDisabled: false,
    }

    if (status === CHAT_STATUS_CUSTOM) {
      state['pendingChatAvailabilityPerMailboxes'] =
        cloneDeep(statusDataByMailbox)
    } else {
      state['pendingChatAvailabilityPerMailboxes'] =
        this.getUpdatedAvailabilityPerMailboxes(status, mailboxes)
    }

    this.setState(state)
  }

  handleOnSelectAllStatusInCustomModal = status => {
    const { mailboxes } = this.props

    this.setState({
      pendingSelectedStatus: status,
      pendingChatAvailabilityPerMailboxes:
        this.getUpdatedAvailabilityPerMailboxes(status, mailboxes),
    })
  }

  getUpdatedAvailabilityPerMailboxes(status, mailboxes) {
    const updatedChatAvailabilityPerMailboxes = {}

    Object.keys(mailboxes).forEach(mailboxId => {
      updatedChatAvailabilityPerMailboxes[mailboxId] = status
    })

    return updatedChatAvailabilityPerMailboxes
  }

  handleOnSelectMailboxStatus = (mailboxId, selectedStatus) => {
    const { pendingChatAvailabilityPerMailboxes } = this.state

    const updatedMailboxStatuses = {
      ...pendingChatAvailabilityPerMailboxes,
      [mailboxId]: selectedStatus,
    }

    this.setState({
      pendingSelectedStatus: CHAT_STATUS_CUSTOM,
      pendingChatAvailabilityPerMailboxes: updatedMailboxStatuses,
    })
  }

  handleOnSaveCustomStatus = () => {
    const { onSave } = this.props
    const chatStatusData = this.getPreparedData()

    this.setState({ isSaveButtonDisabled: true })

    return onSave(chatStatusData).catch(_error => {
      this.setState({ isSaveButtonDisabled: false })
    })
  }

  getPreparedData() {
    const { pendingChatAvailabilityPerMailboxes, pendingSelectedStatus } =
      this.state

    // This helps prevent `custom` being set as the status
    // when user has individually selected the same status
    // for each mailbox and not used the select all radio.
    const sharedMailboxStatus = this.getSharedMailboxesStatus()
    if (sharedMailboxStatus) {
      return {
        status: sharedMailboxStatus,
      }
    }

    return {
      mailboxes: pendingChatAvailabilityPerMailboxes,
      status: pendingSelectedStatus,
    }
  }

  getSharedMailboxesStatus() {
    const { pendingChatAvailabilityPerMailboxes } = this.state
    let statusComparator

    const mailboxesHaveSameStatus = Object.keys(
      pendingChatAvailabilityPerMailboxes
    ).every((key, index) => {
      const mailboxStatus = pendingChatAvailabilityPerMailboxes[key]
      if (index === 0) {
        // Set the status to compare against
        statusComparator = mailboxStatus
        return true
      }

      return mailboxStatus === statusComparator
    })

    return mailboxesHaveSameStatus ? statusComparator : null
  }

  renderAvailabilityRowHeader() {
    return (
      <AvailabilityHeaderUI gap="none">
        {/* This value is related to FF isRenameMailboxToInboxEnabled. Remove after the FF is removed. */}
        <LeftFlexyColumnUI>
          {capitalize(hsGlobal.features.inboxNameNoun)}
        </LeftFlexyColumnUI>
        <Flexy.Block>
          <Flexy gap="none">
            <StatusColumnUI>
              <StatusDot status="available" />
              Available
            </StatusColumnUI>
            <StatusColumnUI>
              <StatusDot status="assign" />
              Assign Only
            </StatusColumnUI>
            <StatusColumnUI>
              <StatusDot status="unavailable" />
              Unavailable
            </StatusColumnUI>
          </Flexy>
        </Flexy.Block>
      </AvailabilityHeaderUI>
    )
  }

  renderSelectAllAvailabilityRow() {
    const opts = {
      faint: true,
      initialValue: this.state.pendingSelectedStatus,
      key: 'select-all',
      label: 'Select all',
      onChange: this.handleOnSelectAllStatusInCustomModal,
      withBackgroundShading: true,
    }

    return this.renderAvailabilityRow(opts)
  }

  renderMailboxAvailabilityRows() {
    const { mailboxes } = this.props

    return Object.keys(mailboxes).map(mailboxId => {
      const dataForRow = this.getDataForMailboxRow(mailboxId)
      return this.renderAvailabilityRow(dataForRow)
    })
  }

  getDataForMailboxRow(mailboxId) {
    const { mailboxes } = this.props
    const { pendingChatAvailabilityPerMailboxes } = this.state
    const mailboxData = mailboxes[mailboxId]

    const mailboxChatAvailability =
      pendingChatAvailabilityPerMailboxes[mailboxId] || CHAT_STATUS_UNAVAILABLE

    return {
      initialValue: mailboxChatAvailability,
      key: `mailbox-${mailboxId}`,
      label: mailboxData.name,
      onChange: status => this.handleOnSelectMailboxStatus(mailboxId, status),
    }
  }

  renderAvailabilityRow({
    faint,
    initialValue,
    key,
    label,
    onChange,
    withBackgroundShading,
  }) {
    return (
      <FlexyUI
        gap="none"
        key={key}
        withBackgroundShading={withBackgroundShading}
      >
        <LeftFlexyColumnUI>
          <Text faint={faint} truncate>
            {label}
          </Text>
        </LeftFlexyColumnUI>
        <Flexy.Block>
          <ChoiceGroup
            align="horizontal"
            selectionLimits="radio"
            name="mailbox-chat-status"
            onChange={onChange}
            value={initialValue}
          >
            <Flexy gap="none">
              <StatusColumnUI>
                <Radio
                  id={`status-${key}-available`}
                  value="available"
                  checked={initialValue === 'available'}
                />
              </StatusColumnUI>
              <StatusColumnUI>
                <Radio
                  id={`status-${key}-assign`}
                  value="assign"
                  checked={initialValue === 'assign'}
                />
              </StatusColumnUI>
              <StatusColumnUI>
                <Radio
                  id={`status-${key}-unavailable`}
                  value="unavailable"
                  checked={initialValue === 'unavailable'}
                />
              </StatusColumnUI>
            </Flexy>
          </ChoiceGroup>
        </Flexy.Block>
      </FlexyUI>
    )
  }

  render() {
    const { isOpen, onClose } = this.props

    return (
      <Modal
        isOpen={isOpen}
        onClose={onClose}
        data-cy="custom-chat-availability-modal"
        version={2}
        title="Custom Chat Availability"
      >
        <KeypressListener
          keyCode={Keys.ENTER}
          handler={this.handleOnSaveCustomStatus}
          type="keydown"
        />
        <Modal.Body isSeamless scrollable={false} version={2}>
          <AvailabilityContentUI>
            {this.renderAvailabilityRowHeader()}
            {this.renderSelectAllAvailabilityRow()}
            {this.renderMailboxAvailabilityRows()}
          </AvailabilityContentUI>
        </Modal.Body>
        <Modal.ActionFooter
          onCancel={onClose}
          onPrimaryClick={this.handleOnSaveCustomStatus}
          primaryButtonDisabled={this.state.isSaveButtonDisabled}
          primaryButtonText="Save & Update"
        />
      </Modal>
    )
  }
}

export default CustomAvailabilityModal
