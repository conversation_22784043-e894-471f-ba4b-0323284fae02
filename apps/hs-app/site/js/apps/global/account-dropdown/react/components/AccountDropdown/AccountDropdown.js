import {
  CHAT_AVAILABILITY_MENU_TYPE,
  REFER_HELP_SCOUT_TYPE,
  LIGHT_USER_MENU_TYPE,
  LINK_MENU_TYPE,
  LOGOUT_MENU_TYPE,
} from '../../App/App'
import {
  AccountDropdownUI,
  ItemContentUI,
  ItemContentTextUI,
  ListItemUI,
} from './AccountDropdown.css'
import AccountDropdownToggler from './AccountDropdown.toggler'
import { getSelectedItem, openUrl } from './AccountDropdown.utils'
import classNames from 'classnames'
import DropList from 'hsds/components/drop-list'
import Flexy from 'hsds/components/flexy'
import Icon from 'hsds/components/icon'
import StatusDot from 'hsds/components/status-dot'
import ChevronRight from 'hsds/icons/chevron-right-tiny'
import PropTypes from 'prop-types'
import React, { useState, useEffect } from 'react'

export const LIST_ITEM_NODE = 'ListItemUI'

function AccountDropdown({
  actions = {
    [CHAT_AVAILABILITY_MENU_TYPE]: () => {},
    [REFER_HELP_SCOUT_TYPE]: () => {},
    [LOGOUT_MENU_TYPE]: () => {},
    [LIGHT_USER_MENU_TYPE]: () => {},
  },
  chatStatus,
  dropdownItems = [],
  initials = '',
  isChatEnabled = false,
  isDisconnected,
  photoUrl = '',
  shouldRefocusOnClose = true,
  isLightUser,
}) {
  const [isOpen, setIsOpen] = useState(false)

  const [selection, setSelection] = useState(
    getSelectedItem({
      dropdownItems,
      chatStatus,
      isChatEnabled,
    })
  )

  useEffect(() => {
    setSelection(
      getSelectedItem({
        dropdownItems,
        chatStatus,
        isChatEnabled,
      })
    )
  }, [isChatEnabled, chatStatus, dropdownItems])

  function handleOnSelect(item) {
    const actionFn = actions[item.menuType]

    if (actionFn) {
      actionFn(item)
    }

    // the parameters are incorrect for getSelectedItem here, leaving as-is for now - Luke Lancaster, Dec 20, 2022
    setSelection(getSelectedItem(dropdownItems, chatStatus))
  }

  /** We use onListItemSelectEvent for the actions below instead of the onSelect
   * because for tracking we need the actual list item node that was selected and
   * to properly open a URL (specially if the metaKey was used) we need the Event.
   * Both of which are only provided here
   */
  function handleListItemSelect({ event, listItemNode }) {
    if (!listItemNode) return
    event.stopPropagation()

    const selectedItemContent =
      listItemNode.querySelector(`.${LIST_ITEM_NODE}`) ||
      listItemNode.closest(`.${LIST_ITEM_NODE}`)

    if (!selectedItemContent) return

    if (selectedItemContent.classList.contains(LINK_MENU_TYPE)) {
      const isRouteActive =
        selectedItemContent.classList.contains('is-active-route')
      const { href } = selectedItemContent.dataset

      if (href && !isRouteActive) {
        openUrl(event, href)
      }
    }
  }

  function getListItemContent(item, isSelected) {
    const { menuType } = item

    if (menuType === LIGHT_USER_MENU_TYPE) {
      return (
        <ItemContentUI className="ItemContentUI ItemContentUI__light-user">
          <Flexy align="middle" gap="md">
            <StatusDot
              outerBorderColor="white"
              outerBorderWidth={2}
              status="custom"
            />
            <Flexy.Block>
              <ItemContentTextUI className="ItemContentTextUI ItemContentLabel">
                {item.label}
              </ItemContentTextUI>
              <ItemContentTextUI className="ItemContentTextUI ItemContentMetaText">
                {item.meta}
              </ItemContentTextUI>
            </Flexy.Block>
            <Icon icon={ChevronRight} size={24} />
          </Flexy>
        </ItemContentUI>
      )
    }

    if (item.menuType === CHAT_AVAILABILITY_MENU_TYPE) {
      return (
        <ItemContentUI className="ItemContentUI ItemContentUI__chat-availability">
          <Flexy align="middle" gap="md">
            <StatusDot
              overDarkBg={isSelected}
              outerBorderColor="white"
              outerBorderWidth={2}
              status={item.value}
              variant={item.value === 'custom' ? 'square-outline' : undefined}
            />
            <Flexy.Block>
              <ItemContentTextUI className="ItemContentLabel ItemContentTextUI">
                {item.label}
              </ItemContentTextUI>
              <ItemContentTextUI className="ItemContentMetaText ItemContentTextUI">
                {item.meta}
              </ItemContentTextUI>
            </Flexy.Block>
          </Flexy>
        </ItemContentUI>
      )
    }

    if (menuType === REFER_HELP_SCOUT_TYPE) {
      return (
        <div className="ReferralItem">
          Refer a Friend{' '}
          <span role="img" aria-label="gift emoji">
            🎁
          </span>
        </div>
      )
    }

    return item.label || item.value
  }

  function renderListItem({ item, isSelected }) {
    const listItemContent = getListItemContent(item, isSelected)
    const { href, isRouteActive, menuType } = item

    return (
      <ListItemUI
        className={classNames(
          LIST_ITEM_NODE,
          isRouteActive && 'is-active-route',
          menuType
        )}
        data-menutype={menuType}
        data-href={href || null}
      >
        {listItemContent}
      </ListItemUI>
    )
  }

  return (
    <AccountDropdownUI
      className={classNames(
        'AccountDropdown',
        isChatEnabled && 'with-chat-enabled',
        isLightUser && 'is-light-user'
      )}
      data-cy="AccountDropdown"
      data-tour-step="AccountDropdown"
    >
      <DropList
        focusTogglerOnMenuClose={shouldRefocusOnClose}
        items={dropdownItems}
        onOpenedStateChange={isMenuOpen => {
          setIsOpen(isMenuOpen)
        }}
        onSelect={handleOnSelect}
        onListItemSelectEvent={handleListItemSelect}
        renderCustomListItemContent={renderListItem}
        selection={selection}
        clearOnSelect={true}
        tippyOptions={{ offset: [11, 5], placement: 'bottom-start' }}
        toggler={
          <AccountDropdownToggler
            chatStatus={chatStatus}
            initials={initials}
            isDisconnected={isDisconnected}
            photoUrl={photoUrl}
            showStatusSymbol={(isChatEnabled || isLightUser) && !isOpen}
            isLightUser={isLightUser}
          />
        }
      />
    </AccountDropdownUI>
  )
}

AccountDropdown.propTypes = {
  actions: PropTypes.shape({
    [CHAT_AVAILABILITY_MENU_TYPE]: PropTypes.func,
    [REFER_HELP_SCOUT_TYPE]: PropTypes.func,
    [LOGOUT_MENU_TYPE]: PropTypes.func,
    [LIGHT_USER_MENU_TYPE]: PropTypes.func,
  }),
  accountOwnerName: PropTypes.string,
  chatStatus: PropTypes.string,
  dropdownItems: PropTypes.array,
  initials: PropTypes.string,
  isChatEnabled: PropTypes.bool,
  isDisconnected: PropTypes.bool,
  isLightUser: PropTypes.bool,
  photoUrl: PropTypes.string,
  shouldRefocusOnClose: PropTypes.bool,
}

export default AccountDropdown
