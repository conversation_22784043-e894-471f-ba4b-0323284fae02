import { hsApiClient } from '@common/utils/clients'
import { getCurrentTheme } from '@common/utils/theme'
import {
  isExperimentVariationEnabled,
  FEATURE_KEYS,
} from '@helpscout/experiment-kit'
import {
  SetupGuideProvider,
  ModalSetupGuide,
  ChooseChannelModal,
  selectUpdateSetupGuideAction,
  selectIsChannelModalOpen,
  selectCloseChannelModal,
  useSetupGuideStore,
} from '@helpscout/growth-kit'
import { Provider as HSDSProvider } from 'hsds/components/hsds'
import React, { useCallback, useMemo } from 'react'
import { render } from 'react-dom'

// Copied from packages/shared/utils/StringUtils/pathJoin/pathJoin.ts because
// we can't use shared packages in hs-app
const pathJoin = (...segments) => {
  if (segments.length === 0) return ''

  const validSegments = segments.filter(
    segment => segment && segment.length > 0
  )

  if (validSegments.length === 0) return ''
  if (validSegments.length === 1) {
    return validSegments[0].replace(/\/+/g, '/')
  }

  const lastSegment = validSegments[validSegments.length - 1]
  const hasTrailingSlash = lastSegment.endsWith('/')

  const cleanedSegments = validSegments
    .map((segment, index) => {
      if (index === 0) {
        return segment.replace(/\/+$/, '')
      } else {
        return segment.replace(/^\/+|\/+$/g, '')
      }
    })
    .filter(segment => segment.length > 0)

  if (cleanedSegments.length === 0) return ''

  let result = cleanedSegments.join('/')

  result = result.replace(/(:)\/([^/])/g, '$1//$2')

  if (hasTrailingSlash && !result.endsWith('/')) {
    result += '/'
  }

  return result
}

const SETUP_GUIDE_INITIAL_OPEN_KEY = 'setupGuide:initialOpen'

export function isSetupGuideModalityEnabled() {
  if (hsGlobal.setupGuide?.isRemoved) {
    return false
  }

  return isExperimentVariationEnabled({
    featureKey: FEATURE_KEYS.SETUP_GUIDE_MODALITY,
  })
}

export function initModalSetupGuideExperiment() {
  if (!hsGlobal.setupGuide) {
    console.error(
      `Failed to initialize ModalSetupGuideExperiment: hsGlobal.setupGuide not found`
    )
    return
  }

  const root = document.getElementById('SetupGuideProgress')
  if (!root) {
    // It is expected that the SetupGuideProgress element is not always present.
    // For example, it is not present if the user is roadblocked.
    return
  }

  render(<SetupGuideExperiment />, root)
}

function SetupGuideExperiment() {
  const initialOpenValue = HS.Utils.SessionStorage?.get(
    SETUP_GUIDE_INITIAL_OPEN_KEY
  )
  const shouldInitiallyOpen = initialOpenValue !== 'false'

  // Set initial open to true if it hasn't been set yet
  if (initialOpenValue === null) {
    HS.Utils.SessionStorage?.set(SETUP_GUIDE_INITIAL_OPEN_KEY, 'true')
  }

  const handleAfterRemove = useCallback(() => {
    const setupGuide = document.getElementById('SetupGuideProgress')
    if (setupGuide) {
      setupGuide.remove()
    }
  }, [])

  const handleOnRemove = useCallback(async () => {
    // Mark as closed when user removes the setup guide
    HS.Utils.SessionStorage?.set(SETUP_GUIDE_INITIAL_OPEN_KEY, 'false')

    return hsApiClient()
      .post('setup-guide/remove/')
      .catch(error => {
        console.error(`Failed to remove setup guide: ${error.message}`, error)
      })
  }, [])

  const sessionStorage = useMemo(
    () => ({
      set: (key, value) => HS.Utils.SessionStorage?.set(key, value),
      get: (key, def) => HS.Utils.SessionStorage?.get(key) ?? def ?? null,
      deleteKey: key => HS.Utils.SessionStorage?.deleteKey(key),
      SETUP_GUIDE_INITIAL_OPEN_KEY,
    }),
    []
  )

  const themeName = useMemo(() => getCurrentTheme(), [])

  return (
    <HSDSProvider scope="hsds-react" themeName={themeName}>
      <SetupGuideProvider
        {...hsGlobal}
        callbacks={{ afterRemove: handleAfterRemove, onRemove: handleOnRemove }}
        sessionStorage={sessionStorage}
      >
        <RealtimeSetupGuide shouldInitiallyOpen={shouldInitiallyOpen} />
        <ChannelModalManager />
      </SetupGuideProvider>
    </HSDSProvider>
  )
}

function ChannelModalManager() {
  const isOpen = useSetupGuideStore(selectIsChannelModalOpen)
  const closeModal = useSetupGuideStore(selectCloseChannelModal)
  // For some reason the selectDefaultMailboxId doesn't work 🤔
  const defaultMailboxId = useSetupGuideStore(state => state.defaultMailboxId)
  const publicUrl = hsGlobal?.assetBasePath || ''
  const imagePath = pathJoin(publicUrl, 'images/')

  return (
    <ChooseChannelModal
      isOpen={isOpen}
      onClose={closeModal}
      defaultMailboxId={defaultMailboxId}
      imagePath={imagePath}
    />
  )
}

// eslint-disable-next-line react/prop-types
function RealtimeSetupGuide({ shouldInitiallyOpen }) {
  const updateSetupGuide = useSetupGuideStore(selectUpdateSetupGuideAction)

  const handleSetupGuideEvent = useCallback(
    ({ data }) => {
      updateSetupGuide(data)
    },
    [updateSetupGuide]
  )

  if (window.PusherClient && hsGlobal.pusher) {
    const channelName = PusherClient.channelName('setup-guide')
    PusherClient.subscribe(channelName, 'setup-guide', handleSetupGuideEvent)
  }

  return <ModalSetupGuide initialOpen={shouldInitiallyOpen} />
}
