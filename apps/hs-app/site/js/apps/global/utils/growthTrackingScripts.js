import Backbone from 'hs-backbone'
import { track } from '@common/utils/mixpanel'

/**
 * PageVisitTracker tracks page views on page load and client-side navigation.
 *
 * @example
 * const tracker = new PageVisitTracker({
 *   backbonePageVisitsToTrackOnClientSideNavigation: ['/contact-us'],
 *   pageVisitEventsToTrackOnLoad: { about: '/about', contactUs: '/contact-us' }
 * })
 * tracker.run()
 */
export class PageVisitTracker {
  /**
   * Typically we track page views on page load, but in some cases we want to
   * track page views on client-side navigation as well. This is a list of page
   * paths that we want to track on client-side navigation using a technique
   * specific to Backbone.
   */
  backbonePageVisitsToTrackOnClientSideNavigation

  /**
   * This is the list of page paths that we want to track on page load.
   */
  pageVisitEventsToTrackOnLoad

  /**
   * This is the list of page paths that we want to track on page load.
   */
  pageVisitsToTrackOnLoad

  /**
   * Construct a new PageVisitTracker.
   *
   * @param backbonePageVisitsToTrackOnClientSideNavigation - a list of page
   *        paths that we want to track on click-side navigation using a
   *        technique specific to Backbone.
   * @param pageVisitEventsToTrackOnLoad - a list of key-value pairs where the
   *        key is the pageVisited property value and the value is the page
   *        path.
   *
   *  @example
   *  const tracker = new PageVisitTracker({
   *    backbonePageVisitsToTrackOnClientSideNavigation: ['/contact-us'],
   *    pageVisitEventsToTrackOnLoad: { about: '/about', contactUs: '/contact-us' }
   *  })
   */
  constructor({
    backbonePageVisitsToTrackOnClientSideNavigation = [],
    pageVisitEventsToTrackOnLoad = [],
  } = {}) {
    this.backbonePageVisitsToTrackOnClientSideNavigation =
      backbonePageVisitsToTrackOnClientSideNavigation
    this.pageVisitEventsToTrackOnLoad = replacePlaceholdersInEventPathnames(
      pageVisitEventsToTrackOnLoad
    )
    this.pageVisitsToTrackOnLoad = Object.values(
      this.pageVisitEventsToTrackOnLoad
    )
  }

  /**
   * Run the tracker. It will immediately track the current page and then track
   * client-side navigation.
   */
  run() {
    this.#trackPageVisitedEvent()
  }

  #track(pathname) {
    const pageVisited = Object.keys(this.pageVisitEventsToTrackOnLoad).find(
      key => this.pageVisitEventsToTrackOnLoad[key] === pathname
    )
    if (pageVisited) {
      track('pageVisited', { pageVisited })
    }
  }

  #trackPageVisitedEvent(event) {
    let pathname = window.location.pathname
    pathname = pathname.endsWith('/') ? pathname.slice(0, -1) : pathname
    if (this.pageVisitsToTrackOnLoad.includes(pathname)) {
      this.#track(pathname)
    }

    if (
      this.backbonePageVisitsToTrackOnClientSideNavigation.includes(pathname)
    ) {
      this.#trackBackboneClientSideNavigation(pathname)
    }
  }

  #trackBackboneClientSideNavigation(pathname) {
    const [_slash, root] = pathname.split('/')

    if (!root) {
      return
    }

    Backbone.history.on('route', (_router, name, _args) => {
      const nextPathname = `/${root}/${name}`
      if (
        this.backbonePageVisitsToTrackOnClientSideNavigation.includes(
          nextPathname
        )
      ) {
        this.#track(nextPathname)
      }
    })
  }

  // If needed we should be able to add an event listener for window.onpopstate
  // to track other (including React) client-side navigation.
}

/**
 * TrialAccountPageVisitTracker is a subclass of PageVisitTracker that tracks
 * a specific set of page views that we are interested in for trial accounts.
 *
 * @example
 * if (isTrialAccount) {
 *   const tracker = new TrialAccountPageVisitTracker()
 *   tracker.run()
 * }
 */
export class TrialAccountPageVisitTracker extends PageVisitTracker {
  constructor() {
    const backbonePageVisitEventsToTrackOnClientSideNavigation = {
      reportsAllChannels: '/reports/all-channels',
      reportsCompany: '/reports/company',
      reportsEmail: '/reports/email',
      reportsHappiness: '/reports/happiness',
      reportsPhone: '/reports/phone',
    }

    const backbonePageVisitsToTrackOnClientSideNavigation = Object.values(
      backbonePageVisitEventsToTrackOnClientSideNavigation
    )

    const pageVisitEventsToTrackOnLoad = {
      apps: '/apps',
      beacons: '/beacons',
      customers: '/customers',
      docs: '/docs/{collectionId}',
      inboxes: '/inboxes/{inboxSlug}/views',
      memberPlan: '/members/plan',
      messages: '/messages',
      profile: '/users/profile/{memberId}',
      settingsCompany: '/settings/company',
      settingsDocs: '/settings/docs',
      settingsInboxes: '/settings/inboxes',
      settingsTags: '/settings/tags',
      teams: '/teams',
      users: '/users',
      ...backbonePageVisitEventsToTrackOnClientSideNavigation,
    }

    super({
      backbonePageVisitsToTrackOnClientSideNavigation,
      pageVisitEventsToTrackOnLoad,
    })
  }
}

/**
 * runGrowthTrackingScripts runs the scripts that are used for application-wide
 * behaviour tracking.
 */
export const runGrowthTrackingScripts = () => {
  if (hsGlobal.isTrialAccount) {
    const trialAccountPageVisitTracker = new TrialAccountPageVisitTracker()
    trialAccountPageVisitTracker.run()
  }
}

function replacePlaceholdersInEventPathnames(eventsWithPathnames) {
  return Object.keys(eventsWithPathnames).reduce((acc, key) => {
    const value = eventsWithPathnames[key]
    if (value.indexOf('{memberId}') >= 0) {
      if (window.hsGlobal?.memberId) {
        acc[key] = value.replace('{memberId}', window.hsGlobal.memberId)
      }
      return acc // key-value pair omitted unless {memberId} was replaced
    }

    if (value.indexOf('{inboxSlug}') >= 0) {
      if (window.appData?.shared?.mailbox?.slug) {
        acc[key] = value.replace(
          '{inboxSlug}',
          window.appData.shared.mailbox.slug
        )
      }
      return acc // key-value pair omitted unless {inboxSlug} was replaced
    }

    if (value.indexOf('{collectionId}') >= 0) {
      if (window.appData?.collection?.id) {
        acc[key] = value.replace('{collectionId}', window.appData.collection.id)
      }
      return acc // key-value pair omitted unless {collectionId} was replaced
    }

    acc[key] = value
    return acc
  }, {})
}
