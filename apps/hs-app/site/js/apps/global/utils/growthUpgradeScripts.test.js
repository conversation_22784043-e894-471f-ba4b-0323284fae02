import { runGrowthUpgradeScripts } from './growthUpgradeScripts'

describe('runGrowthUpgradeScripts', () => {
  let insertUpgradeBannerSpy
  let mockGrowthKitSDK
  let mockHsGlobal

  beforeEach(() => {
    insertUpgradeBannerSpy = jest.fn()
    mockGrowthKitSDK = {
      insertUpgradeBanner: insertUpgradeBannerSpy,
    }
    mockHsGlobal = {
      contactsServedLimit: undefined,
      memberPermissions: {
        manageAccount: true,
      },
    }
  })

  test('should not call insert upgrade banner if contactsServedLimit is not defined', () => {
    runGrowthUpgradeScripts({
      GrowthKitSDK: mockGrowthKitSDK,
      hsGlobal: mockHsGlobal,
      shouldInsertBanner: true,
    })
    expect(insertUpgradeBannerSpy).not.toHaveBeenCalled()
  })

  test('Should still call insert upgradeBanner if half of the contacts limit has not been met', () => {
    mockHsGlobal.contactsServedLimit = {
      contactsUsed: 10,
      limit: 50,
    }
    runGrowthUpgradeScripts({
      GrowthKitSDK: mockGrowthKitSDK,
      hsGlobal: mockHsGlobal,
      shouldInsertBanner: true,
    })
    expect(insertUpgradeBannerSpy).toHaveBeenCalled()
    expect(insertUpgradeBannerSpy).toHaveBeenCalledWith({
      account: {
        isFreePlan: true,
      },
      contactsHelped: {
        actual: 10,
        maximum: 50,
        remaining: 40,
        reset: undefined,
        threshold: 25,
      },
      permissions: {
        canReadMore: true,
        canUpgrade: true,
      },
    })
  })

  test('canUpgrade should be false if memberPermissions.manageAccount is false', () => {
    mockHsGlobal.memberPermissions.manageAccount = false
    mockHsGlobal.contactsServedLimit = {
      contactsUsed: 10,
      limit: 50,
    }
    runGrowthUpgradeScripts({
      GrowthKitSDK: mockGrowthKitSDK,
      hsGlobal: mockHsGlobal,
      shouldInsertBanner: true,
    })
    expect(insertUpgradeBannerSpy).toHaveBeenCalled()
    expect(insertUpgradeBannerSpy).toHaveBeenCalledWith({
      account: {
        isFreePlan: true,
      },
      contactsHelped: {
        actual: 10,
        maximum: 50,
        remaining: 40,
        reset: undefined,
        threshold: 25,
      },
      permissions: {
        canReadMore: false,
        canUpgrade: false,
      },
    })
  })

  test('Should see the upgradeBanner if the account has used 50% or more of the contacts limit', () => {
    mockHsGlobal.contactsServedLimit = {
      contactsUsed: 30,
      limit: 50,
    }
    runGrowthUpgradeScripts({
      GrowthKitSDK: mockGrowthKitSDK,
      hsGlobal: mockHsGlobal,
      shouldInsertBanner: true,
    })
    expect(insertUpgradeBannerSpy).toHaveBeenCalled()
    expect(insertUpgradeBannerSpy).toHaveBeenCalledWith({
      account: {
        isFreePlan: true,
      },
      contactsHelped: {
        actual: 30,
        maximum: 50,
        remaining: 20,
        reset: undefined,
        threshold: 25,
      },
      permissions: {
        canReadMore: true,
        canUpgrade: true,
      },
    })
  })

  test('Should not call insertUpgradeBanner if shouldInsertBanner is false', () => {
    runGrowthUpgradeScripts({
      GrowthKitSDK: mockGrowthKitSDK,
      hsGlobal: mockHsGlobal,
      shouldInsertBanner: false,
    })
    expect(insertUpgradeBannerSpy).not.toHaveBeenCalled()
  })
})
