export function runGrowthUpgradeScripts({
  GrowthKitSDK,
  hsGlobal,
  shouldInsertBanner,
}) {
  if (!shouldInsertBanner) {
    return
  }

  const { contactsServedLimit, memberPermissions } = hsGlobal
  const isFreePlan = <PERSON><PERSON><PERSON>(contactsServedLimit)

  // don't show the banner if the user is on a paid plan
  if (!isFreePlan) {
    return
  }

  GrowthKitSDK.insertUpgradeBanner({
    account: {
      isFreePlan,
    },
    contactsHelped: {
      actual: contactsServedLimit.contactsUsed ?? 0,
      maximum: contactsServedLimit.limit,
      remaining:
        contactsServedLimit.limit - (contactsServedLimit.contactsUsed ?? 0),
      reset: contactsServedLimit.resetDate,
      threshold: Math.round(contactsServedLimit.limit / 2),
    },
    permissions: {
      canReadMore: <PERSON><PERSON><PERSON>(memberPermissions.manageAccount),
      canUpgrade: <PERSON><PERSON><PERSON>(memberPermissions.manageAccount),
    },
  })
}
