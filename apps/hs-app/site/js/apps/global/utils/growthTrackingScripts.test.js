import Backbone from 'hs-backbone'
import { jest } from '@jest/globals'
import * as mixpanel from '@common/utils/mixpanel'
import {
  PageVisitTracker,
  TrialAccountPageVisitTracker,
} from './growthTrackingScripts'

describe('PageVisitTracker', () => {
  let originalLocation

  beforeEach(() => {
    jest.clearAllMocks()
    jest.spyOn(window, 'location', 'get').mockReturnValue({ pathname: '/test' })
    originalLocation = window.location
    delete window.location
    window.location = { pathname: '/test' }
    jest.spyOn(mixpanel, 'track')
  })

  afterEach(() => {
    window.location = originalLocation
    jest.restoreAllMocks()
  })

  it('tracks page visits on load', () => {
    window.location.pathname = '/foo'
    const tracker = new PageVisitTracker({
      pageVisitEventsToTrackOnLoad: { foo: '/foo' },
      backbonePageVisitsToTrackOnClientSideNavigation: [],
    })
    tracker.run()
    expect(mixpanel.track).toHaveBeenCalledWith('pageVisited', { pageVisited: 'foo' })
  })

  it('does not track if pathname not in list', () => {
    window.location.pathname = '/bar'
    const tracker = new PageVisitTracker({
      pageVisitEventsToTrackOnLoad: { foo: '/foo' },
      backbonePageVisitsToTrackOnClientSideNavigation: [],
    })
    tracker.run()
    expect(mixpanel.track).not.toHaveBeenCalled()
  })

  it('tracks client-side navigation via Backbone', () => {
    window.location.pathname = '/baz'
    const onSpy = jest
      .spyOn(Backbone.history, 'on')
      .mockImplementation((event, cb) => {
        cb({}, 'qux', [])
      })
    const tracker = new PageVisitTracker({
      pageVisitEventsToTrackOnLoad: {
        baz: '/baz',
        bazQux: '/baz/qux',
      },
      backbonePageVisitsToTrackOnClientSideNavigation: ['/baz', '/baz/qux'],
    })
    tracker.run()
    expect(onSpy).toHaveBeenCalledWith('route', expect.any(Function))
    expect(mixpanel.track).toHaveBeenCalledWith('pageVisited', { pageVisited: 'bazQux' })
    onSpy.mockRestore()
  })
})

describe('TrialAccountPageVisitTracker', () => {
  let originalHsGlobal
  let originalAppData
  let originalLocation

  beforeEach(() => {
    jest.clearAllMocks()
    originalHsGlobal = window.hsGlobal
    originalAppData = window.appData
    originalLocation = window.location
    delete window.location
    window.location = { pathname: '/test' }
    window.hsGlobal = { memberId: '123' }
    window.appData = {
      collection: { id: '456' },
      shared: { mailbox: { slug: 'abc' } },
    }
    jest.spyOn(mixpanel, 'track')
  })

  afterEach(() => {
    window.hsGlobal = originalHsGlobal
    window.appData = originalAppData
    window.location = originalLocation
    jest.restoreAllMocks()
  })

  it('replaces placeholders in paths', () => {
    const tracker = new TrialAccountPageVisitTracker()
    expect(
      tracker.pageVisitsToTrackOnLoad.some(
        p =>
          p.includes('{memberId}') ||
          p.includes('{inboxSlug}') ||
          p.includes('{collectionId}')
      )
    ).toBe(false)
    expect(tracker.pageVisitsToTrackOnLoad).toContain('/users/profile/123')
    expect(tracker.pageVisitsToTrackOnLoad).toContain('/inboxes/abc/views')
    expect(tracker.pageVisitsToTrackOnLoad).toContain('/docs/456')
  })

  it('tracks page visits on load', () => {
    window.location.pathname = '/users/profile/123'
    const tracker = new TrialAccountPageVisitTracker()
    tracker.run()
    expect(mixpanel.track).toHaveBeenCalledWith('pageVisited', {
      pageVisited: 'profile',
    })
  })
})
