/* global hsGlobal */
import { track } from '@common/utils/mixpanel'

function bindToTopNavigation() {
  $('#SetupGuideProgress').click(function () {
    trackLinkClick(this, 'setup-guide', 'Setup Guide Progress')
  })
}

function bindToDashboard() {
  $('#setupGuide').on('click', 'a', function () {
    trackLinkClick(this, 'setup-guide')
  })

  $('#setupGuide').on('click', 'button', function () {
    trackButtonClick(this, 'setup-guide')
  })
}

function trackLinkClick(target, context, customLabel = null) {
  const props = {
    href: $(target).attr('href'),
  }

  trackClick(target, context, 'Clicked Link', props, customLabel)
}

function trackButtonClick(target, context, customLabel = null) {
  trackClick(target, context, 'Clicked Button', {}, customLabel)
}

function trackClick(target, context, name, props = {}, customLabel = null) {
  const $target = $(target)
  const labelAttr = $target.attr('label')
  const titleAttr = $target.attr('title')
  const text = $target.text()
  const label = customLabel || labelAttr || titleAttr || text

  track(
    name,
    $.extend({}, props, {
      context: context,
      label: label,
      labelAttr: labelAttr,
      titleAttr: titleAttr,
      text: text,
      accountStatus: hsGlobal.subscriptionState,
    })
  )
}

module.exports = {
  bindToTopNavigation: bindToTopNavigation,
  bindToDashboard: bindToDashboard,
}
