import { loadBeacon } from './beacon/beacon'
import BrowserNotification from './notification/BrowserNotification'
import BrowserNotificationHandler from './notification/BrowserNotificationHandler'
import ChatNotificationHandler from './notification/ChatNotificationHandler'
import presence from './presence'
import PresenceModal from './presenceModal/init'
import { isTabVisible } from './utils/browser'
import { isInChatApp } from './utils/chat'
import { cleanUpLocalStorage } from './utils/cleanUpLocalStorage'
import {
  initModalSetupGuideExperiment,
  isSetupGuideModalityEnabled,
} from './utils/growthSetupGuideModalityExperiment'
import { runGrowthTrackingScripts } from './utils/growthTrackingScripts'
import { runGrowthUpgradeScripts } from './utils/growthUpgradeScripts'
import logoutListener from './utils/logoutListener'
import WorkerSoundPlayer from './worker-sound-player/index'
import { hsApiClient } from '@common/utils/clients'
import { canPerformBulkActions } from '@common/utils/permissions'
import { GrowthKitSDK } from '@helpscout/growth-kit'
import $ from 'jquery'
import Push from 'push.js'

let SearchAutocompleteApp
let AccountDropdownApp
let NotificationHistoryApp
let ReferralShareApp
let ConnectionFailureApp
let SetupGuideApp
let subscribeToConvoRestoreEvents
let TagCacheRemoval
let tagCacheRemovalModel
let mixpanelTracker
window.appData = window.appData || {}
const skipLoggedInApps = (appData && appData.skipLoggedInApps) || false

// initialize runGrowthUpgradeScripts
try {
  runGrowthUpgradeScripts({
    GrowthKitSDK,
    hsGlobal,
    shouldInsertBanner: appData && appData?.existingMailbox !== false,
  })
} catch (error) {
  // silently fail and don't show the banner
  console.error('Error initializing runGrowthUpgradeScripts', error)
}

// initialize runGrowthTrackingScripts
try {
  runGrowthTrackingScripts()
} catch (error) {
  // silently fail and don't track
  console.error('Error initializing runGrowthTrackingScripts', error)
}

if (!skipLoggedInApps) {
  SearchAutocompleteApp = require('./search-autocomplete/App')
  AccountDropdownApp = require('./account-dropdown/App')
  NotificationHistoryApp = require('./notification-history/App')
  ReferralShareApp = require('./referral-share/App')
  SetupGuideApp = require('./setup-guide/App')
  ConnectionFailureApp = require('./connection-failure/App')
  subscribeToConvoRestoreEvents = require('./restore-convos/restore-convos')
  HS.Utils.SetupAbTest = require('./ab-testing/setup-ab-test')
  TagCacheRemoval = require('./tag-cache-removal/tag-cache-removal')
  mixpanelTracker = require('./mixpanel-tracker')
}

async function init() {
  const queryParams = HS.Utils.Main.getQueryParams()
  let searchOptions = { canPerformBulkActions: canPerformBulkActions() }
  if (window.location.pathname.indexOf('/') === 0) {
    searchOptions = {
      ...searchOptions,
      autoOpen: queryParams && queryParams.hsSearch === 'true',
      initialQuery: queryParams && queryParams.query,
    }
  }

  const notificationsOptions = {
    infiniteScroll: false,
  }

  const hsAppClient = hsApiClient()

  // Feb 2025: We identified a problem where the browser notification/tag cache flags in localstorage
  // don't get deleted, and over time localstorage fills up and throws QuotaExceededErrors.
  cleanUpLocalStorage()

  if (!skipLoggedInApps) {
    AccountDropdownApp.start({
      pusher: PusherClient,
      presence,
      hsAppClient,
    })

    require('./latest-updates')
    require('./inbox-update-available')
    require('./conversation-modal')

    document.addEventListener('conversationModal.show', function (event) {
      const conversationModal = require('conversation-modal/dist')
      conversationModal.show(event.detail)
    })

    SearchAutocompleteApp.start(searchOptions)
    NotificationHistoryApp.start(notificationsOptions)
    ConnectionFailureApp.start()
    ReferralShareApp.start()

    const setupGuideModalityEnabled = await isSetupGuideModalityEnabled()
    if (setupGuideModalityEnabled) {
      initModalSetupGuideExperiment()
    } else {
      SetupGuideApp.start({ pusher: PusherClient })
    }
    subscribeToConvoRestoreEvents()
    loadBeacon()
    tagCacheRemovalModel = new TagCacheRemoval()

    const soundPlayer = new WorkerSoundPlayer(hsGlobal.assetBasePath)

    const browserNotification = new BrowserNotification(
      HS.Utils.LocalStorage,
      Push
    )

    // Subscribe to chat notification real-time events
    const chatNotificationHandler = new ChatNotificationHandler(
      HS.Utils.LocalStorage,
      PusherClient,
      hsGlobal.pusher.namespace,
      browserNotification,
      HS.Plugins.Flash,
      soundPlayer,
      isTabVisible,
      isInChatApp,
      appData.mailbox && appData.mailbox.id
    )
    chatNotificationHandler.subscribe(hsGlobal.memberId)

    // Subscribe to browser notification real-time events
    const browserNotificationHandler = new BrowserNotificationHandler(
      PusherClient,
      browserNotification
    )
    browserNotificationHandler.subscribe(
      hsGlobal.pusher.namespace,
      hsGlobal.memberId
    )

    if (hsGlobal.isTrialAccount) {
      mixpanelTracker && mixpanelTracker.bindToTopNavigation()
    }
  }

  logoutListener(() => {
    const logout = () => window.location.reload()

    presence.logout().then(logout).catch(logout)
  })

  if (PusherClient && hsGlobal.pusher) {
    new PresenceModal(
      window.PusherClient,
      hsGlobal.pusher.namespace,
      hsGlobal.memberId,
      presence,
      hsAppClient
    )
  }
}

$(document).ready(init)
