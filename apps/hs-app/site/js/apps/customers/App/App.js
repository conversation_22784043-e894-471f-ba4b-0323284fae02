import { createStore } from '../store'
import AppRouter from './AppRouter'
import Routes from './Routes'
import PropTypes from 'prop-types'
import React from 'react'
import { Provider as ReduxProvider } from 'react-redux'
import HsApp from 'shared/components/HsApp'

export class App extends React.PureComponent {
  static defaultProps = {
    preloadedState: {
      basename: '',
    },
  }

  static propTypes = {
    preloadedState: PropTypes.shape({
      basename: PropTypes.string,
    }),
  }

  render() {
    const { preloadedState, redirect } = this.props
    const store = createStore(preloadedState)

    return (
      <ReduxProvider store={store}>
        <AppRouter>
          <HsApp isFullscreen={false} role="main">
            <Routes redirect={redirect} />
          </HsApp>
        </AppRouter>
      </ReduxProvider>
    )
  }
}

export default App
