import Page from 'hsds/components/page'
import { getColor } from 'hsds/utils/color'
import PropTypes from 'prop-types'
import React, { Component } from 'react'
import styled from 'styled-components'

export const PageCardUI = styled(Page.Card)`
  min-height: 600px;
`

export const PageUI = styled(Page)`
  margin-bottom: 0;
  padding-bottom: var(--hsds-page-margin-bottom);
`

export const ProfileBannerUI = styled('div')`
  background-color: ${getColor('cobalt.300')};
  background-image: url(${hsGlobal.imagePath}profile/profileheader-bg.jpg);
  background-position: center center;
  background-repeat: repeat-x;
  background-size: 900px 90px;
  height: 90px;
  left: 0;
  position: absolute;
  right: 0;
  top: 0;
  z-index: 1;

  @media only screen and (-webkit-min-device-pixel-ratio: 1.25),
    only screen and (min--moz-device-pixel-ratio: 1.25),
    only screen and (-o-min-device-pixel-ratio: 1.25/1),
    only screen and (min-device-pixel-ratio: 1.25),
    only screen and (min-resolution: 200dpi),
    only screen and (min-resolution: 1.25dppx) {
    background-image: url(${hsGlobal.imagePath}profile/<EMAIL>);
  }
`

export class LayoutPage extends Component {
  render() {
    const {
      children,
      withProfileBanner = false,
      renderBelowCard,
      ...rest
    } = this.props

    const pageProps = {
      ...rest,
      fullPage: true,
      renderInsideLayout: () => {
        return withProfileBanner && <ProfileBannerUI />
      },
    }

    return (
      <PageUI {...pageProps}>
        <PageCardUI>
          <Page.Section>
            <Page.Content>{children}</Page.Content>
          </Page.Section>
        </PageCardUI>
        {renderBelowCard && renderBelowCard()}
      </PageUI>
    )
  }
}

LayoutPage.propTypes = {
  withProfileBanner: PropTypes.bool,
  renderBelowCard: PropTypes.func,
  children: PropTypes.any,
}

export default LayoutPage
