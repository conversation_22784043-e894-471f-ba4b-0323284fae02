import CustomerProfile from '../../components/CustomerProfile'
import DeleteProfile from '../../components/DeleteProfile'
import { ProfileScreenUI, LayoutPageUI } from './ProfileScreen.css'
import React from 'react'

export class ProfileScreen extends React.PureComponent {
  componentDidMount() {
    window.scrollTo(0, 0)
  }

  render() {
    const {
      match: {
        params: { id },
      },
    } = this.props

    return (
      <ProfileScreenUI className="ProfileScreenUI">
        <LayoutPageUI
          withProfileBanner
          renderBelowCard={() => <DeleteProfile maxConversations={100} />}
        >
          <CustomerProfile profileId={id} />
        </LayoutPageUI>
      </ProfileScreenUI>
    )
  }
}

export default props => <ProfileScreen {...props} />
