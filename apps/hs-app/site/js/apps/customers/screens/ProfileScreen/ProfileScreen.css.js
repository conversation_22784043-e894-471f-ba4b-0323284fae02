import LayoutPage from '../../components/LayoutPage'
import { PageCardUI } from '../../components/LayoutPage/LayoutPage'
import styled from 'styled-components'

export const ProfileScreenUI = styled('div')`
  display: block;
  width: 100%;
`

export const LayoutPageUI = styled(LayoutPage)`
  position: relative;
  --hsds-page-margin-bottom: 0;
  --hsds-page-margin-top: 20px !important;

  ${PageCardUI} {
    z-index: 2;
    padding: 0;
    position: relative;
  }
`
