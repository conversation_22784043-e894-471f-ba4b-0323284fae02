import HS from 'hs-core'
import App from '../App'
import Model from '../../common/models/SiteSettingsModel'
import template from '../templates/newSite.hbs'
import ViewValidator from '../../common/utils/ViewValidator'
import SlugUtils from '../../common/utils/SlugUtils'
module.exports = Marionette.ItemView.extend({
  template: template,
  model: new Model(),
  ui: {
    title: '#title',
    subDomain: '#subDomain',
    save: '.submit',
    costWarning: '#maxSiteCostWarning',
  },

  events: {
    'click .submit': 'save',
    'click .cancel': 'cancel',
    'keypress input#title': 'onKeyPress',
    'keypress input#subDomain': 'onKeyPress',
    'keyup #title': 'updateSubDomain',
  },

  initialize: function () {
    this.model = new Model({ collectionId: this.options.collectionId })
    this.model.set('visibility', 'public')
    this.allowNew = this.options.allowNew
    this.planName = this.options.planName
    this.viewValidator = new ViewValidator(this, 'play')
  },

  onClose: function () {
    this.viewValidator.unbind()
  },

  onShow: function () {
    this.ui.title.focus()
    this.ui.costWarning.toggle(this.shouldShowCostWarning())
  },

  onKeyPress: function (e) {
    if (e.which === 13) {
      this.save()
    }
  },

  updateSubDomain: function () {
    const title = this.ui.title.val()
    const subDomain = SlugUtils.getSlug(title)
    this.ui.subDomain.val(subDomain)
    this.model.set('subDomain', subDomain)
  },

  resetButton: function () {
    if (this.ui.save && this.ui.save.button) {
      this.ui.save.button('reset')
    }
  },

  shouldShowCostWarning: function () {
    if (this.options.includedSites === null) {
      return false
    }

    return this.options.siteCount >= this.options.includedSites
  },

  save: function (e) {
    e && e.preventDefault()

    if (!this.allowNew) {
      HS.Plugins.Flash.error(
        `You've reached the maximum number of Docs sites available <a href='/members/available-plans'>Upgrade</a>`,
        {
          escapeHTML: false,
        }
      )
      return
    }

    const title = $.trim(this.ui.title.val())
    const subDomain = this.ui.subDomain.val()
    if (!title) {
      this.viewValidator.invalid(this, 'title', 'Please enter a Site Name')
      this.model.trigger('invalid', { title: 'Please enter a Site Name' })
    } else if (!subDomain) {
      this.viewValidator.invalid(this, 'subDomain', 'Please enter a Subdomain')
      this.model.trigger('invalid', { subDomain: 'Please enter a Subdomain' })
    } else {
      this.viewValidator.valid(this, 'title')
      this.viewValidator.valid(this, 'subdomain')
      this.ui.save.button('loading')
      this.model.set({
        title: title,
        subDomain: subDomain,
      })
      this.model
        .save(this.model.toJSON(), {
          url: `${this.model.url()}?reload=true`,
          validate: false,
          success: $.proxy(function (model) {
            this.close()
            HS.Utils.Main.success('Created new site')
            this.trigger('new:site', model)
            App.trigger('new:site', model)
            App.trigger('collections-updated')
          }, this),
          error: function () {
            HS.Utils.Main.error('Error creating site')
          },
        })
        .always($.proxy(this.resetButton, this))
    }
  },
})
