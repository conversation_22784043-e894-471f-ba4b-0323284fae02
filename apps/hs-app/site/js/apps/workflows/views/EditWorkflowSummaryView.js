import HS from 'hs-core'
import App from '../App'
import Constants from '../../common/components/WorkflowsConstants'
import WorkflowModel from '../models/WorkflowModel'
import AbstractWorkflowInputView from '../views/AbstractWorkflowInputView'
import WorkflowSummaryDetailsView from '../views/WorkflowSummaryDetailsView'
import template from '../templates/editWorkflowSummary.hbs'
import { capitalize } from 'underscore.string'

module.exports = AbstractWorkflowInputView.extend({
  template: template,
  regions: {
    details: '#summ',
  },
  events: {
    'click #status-btn': 'toggleStatus',
    'click #copyWorkflow': 'copyWorkflow',
    'click #moveCopyWorkflow a.move-workflow': 'copyWorkflowTo',
    'click #view-workflow-list': 'loadingMessage',
  },
  initialize: function (opts) {
    this.mailboxes = opts.mailboxes
    this.cachedWorkflow = opts.cachedWorkflow
    this.changingStatus = false
    this.changingApplyPrevious = false
  },
  onRender: function () {
    this.details.show(
      new WorkflowSummaryDetailsView({
        model: this.model,
        isFolderOnly: this.isFolderOnly(),
        mode: 'edit',
        disableCopyToFolder: this.disableCopyToFolder(),
      })
    )
    this.$('#model-name').editable($.proxy(this.saveWorkflowName, this), {
      submit: 'Save',
      cancel: 'Cancel',

      // The following comment from the old code
      // The following onblur:ignore is set on purpose.
      // Do not change without talking to Denny first.
      onblur: 'ignore',
      escapeHtml: true,
    })
  },
  onShow: function () {
    HS.Utils.Main.initPopovers()
  },
  serializeData: function () {
    // Inbox names are related to isRenameMailboxToInboxEnabled FF.
    // Remove them when the FF is disabled.
    const { inboxNameNoun } = hsGlobal.features

    return _.extend(this.model.toJSON(), {
      mailboxes: this.mailboxes.toJSON(),
      currentMailboxId: this.cachedWorkflow.attributes.mailboxId,
      path: hsGlobal.path,
      pending:
        this.model.get('status') == Constants.WorkflowStatus.PENDINGREVIEW,
      articleId: '524595b2e4b0ffbdeed0f265',
      capitalizedInboxNameNoun: capitalize(inboxNameNoun),
      displayCopyToFolderWarning: this.displayCopyToFolderWarning(),
      disableCopyToFolder: this.disableCopyToFolder(),
    })
  },
  isFolderOnly: function () {
    if (!this.cachedWorkflow || !this.cachedWorkflow.get('actions')) {
      return false
    }
    return this.cachedWorkflow.isFolderOnly()
  },
  toggleStatus: function (e) {
    if (this.disableCopyToFolder()) {
      return
    }

    const workflowModel = new WorkflowModel({
      id: this.model.id,
      mailboxId: this.model.get('mailboxId'),
      status: this.model.get('status') == 0 ? 1 : 0,
    })

    if (this.changingStatus) {
      e.preventDefault()
      e.stopImmediatePropagation()
      return false
    }
    this.changingStatus = true
    const data = {
      applyToExistingTickets: this.model.get('applyToExistingTickets'),
      status: workflowModel.get('status'),
    }
    const promise = workflowModel.activate(
      data,
      _.bind(this.activeStatusChanged, this)
    )
    App.trigger('promise:start', promise)
  },
  activeStatusChanged: function () {
    App.trigger('workflow:saved')
    const newStatus = this.model.get('status')
      ? Constants.WorkflowStatus.ACTIVE
      : Constants.WorkflowStatus.INACTIVE
    this.model.set({
      status: newStatus,
      active: !newStatus,
      invalid: false,
    })
    this.render()
    HS.Utils.Main.initPopovers()
    this.changingStatus = false
  },

  saveWorkflowName: function (newName) {
    const that = this
    const executeSave = (workflowModel, newName) => {
      const promise = workflowModel.save(
        { name: newName },
        {
          success: function () {
            App.trigger('workflow:saved', workflowModel)
            that.model.set('name', newName)
            that.render()
          },
          error: $.proxy(that.onSaveError, that),
        }
      )
      App.trigger('promise:start', promise)
    }
    if (
      this.cachedWorkflow &&
      this.cachedWorkflow.get('id') == that.model.get('id')
    ) {
      executeSave(this.cachedWorkflow.clone(), newName)
    } else {
      const workflowModel = new WorkflowModel({
        id: that.model.get('id'),
        mailboxId: that.model.get('mailboxId'),
      })
      workflowModel.fetch({
        success: function (serverModel) {
          that.cachedWorkflow = serverModel
          executeSave(serverModel, newName)
        },
      })
    }
    return newName
  },
  copyWorkflow: function () {
    const mailboxId = this.model.get('mailboxId')
    const workflowId = this.model.id
    const promise = HS.Utils.Ajax.post(
      `/api/v0/mailboxes/${mailboxId}/workflows/${workflowId}/duplicate`,
      newWorkflow => {
        App.trigger('workflow:saved')
        const destUrl = `/${mailboxId}/new/${newWorkflow.id}/conditions/`
        App.appRouter.navigate(destUrl, { trigger: true })
      }
    )
    App.trigger('promise:start', promise)
  },
  copyWorkflowTo: function (e) {
    const newMailbox = $(e.target).data()
    const mailboxId = this.model.get('mailboxId')
    const workflowId = this.model.id
    const isSameMailbox =
      parseInt(mailboxId, 10) === parseInt(newMailbox.id, 10)
    const alertMessage = this.getAlertMessage()
    if (!isSameMailbox && alertMessage) {
      HS.Utils.Main.confirm(
        alertMessage,
        () => this.proceedCopyingWorkflowTo(newMailbox),
        null,
        {
          ok: 'Continue',
        }
      )
    } else {
      this.proceedCopyingWorkflowTo(newMailbox)
    }
  },
  proceedCopyingWorkflowTo: function (newMailbox) {
    const promise = HS.Utils.Ajax.post(
      `/api/v0/mailboxes/${this.model.get('mailboxId')}/workflows/${
        this.model.id
      }/copy-to/${newMailbox.id}`,
      newWorkflow => {
        const destUrl = `settings/workflows/${newMailbox.id}/new/${newWorkflow.id}/conditions/`
        window.location.href = hsGlobal.path + destUrl
      }
    )
    App.trigger('promise:start', promise)
  },
  displayCopyToFolderWarning() {
    return this.cachedWorkflow.hasCopyToFolderAction()
  },
  disableCopyToFolder() {
    const { isDisableCopyToFolderEnabled } = hsGlobal.features
    return (
      isDisableCopyToFolderEnabled &&
      this.cachedWorkflow.hasCopyToFolderAction()
    )
  },
  /**
   * Will look at all actions and determine if any of them are linked to an existing Custom Field.
   */
  hasCustomField() {
    return this.cachedWorkflow.attributes.actions.some(
      action =>
        action.type === Constants.ActionType.SET_FIELD &&
        'fieldId' in action.value &&
        parseInt(action.value.fieldId, 10) > 0,
      false
    )
  },
  /**
   * Will look at all actions and determine if any of them are linked to an existing Saved Reply
   */
  hasSavedReply() {
    return this.cachedWorkflow.attributes.actions.some(action => {
      return (
        action.type === Constants.ActionType.EMAIL_CUSTOMERS &&
        'savedReplyId' in action.value &&
        (parseInt(action.value.savedReplyId, 10) > 0 ||
          `action.value.savedReplyId`.includes(','))
      )
    }, false)
  },
  /**
   * Builds a message to show to the user when a Workflow is being copied to a different Mailbox and
   * it references any Saved Replies and/or Custom Fields (which cannot be copied between Mailbox).
   *
   * @returns {string|null}
   */
  getAlertMessage() {
    if (this.hasCustomField() && this.hasSavedReply()) {
      return (
        'Saved replies and custom fields cannot be copied between mailboxes. ' +
        'The contents of your saved reply will be copied as text and any custom fields will be ignored.'
      )
    } else if (this.hasCustomField()) {
      return (
        'Custom fields cannot be copied between mailboxes. ' +
        'Any custom fields will be ignored.'
      )
    } else if (this.hasSavedReply()) {
      return (
        'Saved replies cannot be copied between mailboxes. ' +
        'The contents of your saved reply will be copied as text.'
      )
    } else {
      return null
    }
  },
  loadingMessage: function (e) {
    $(e.target).button('loading')
  },
})
