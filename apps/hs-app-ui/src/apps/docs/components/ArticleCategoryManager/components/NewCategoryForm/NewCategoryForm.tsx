import { noop } from 'lodash'
import { SyntheticEvent, useRef, useState } from 'react'

import { SerializedError } from '@reduxjs/toolkit'

import {
  getErrorMessageForCategoryName,
  sanitizeCategoryName,
  validateCategoryName,
} from './utils'
import { QueryError } from 'apps/docs/services/utils/buildAxiosClientAdapter'

import { useOnError, useOnSuccess } from 'apps/docs/hooks'
import type { CreateFnData } from 'apps/docs/hooks/useCreateCategory'

import { TEST_IDS, TEXT } from 'apps/docs/constants'

import { NewButtonUI } from './NewCategoryForm.css'

import { SaveButton } from './components'
import { VerticalSpaceBetween } from 'apps/docs/components'
import { Article, Category } from 'apps/docs/schema'
import Button from 'hsds/components/button'
import Input from 'hsds/components/input'

export type Props = {
  /** The categories associated with the article. */
  associatedCategories: Article['categories']

  /** A function that can be used to create a new category. */
  create: (data: CreateFnData) => () => void

  /** Flag controlling whether a category can be created. */
  isCreatable?: boolean

  /** Flag indicating if a category is being created. */
  isCreating?: boolean

  /** The result when creation succeeds. */
  isCreatingDataResult?: Category

  /** Flag indicating if a category failed to be created. */
  isCreatingDidFail?: boolean

  /** Flag indicating if a category succeeded to be created. */
  isCreatingDidSucceed?: boolean

  /** The result when creation fails. */
  isCreatingErrorResult?: QueryError | SerializedError

  /** A function that can be used to update an article's categories. */
  updateCategories: (categories: Article['categories']) => () => void
}

/**
 * A form used to create new categories.
 *
 * @constructor
 */
export function NewCategoryForm({
  associatedCategories,
  create,
  isCreatable = false,
  isCreating = false,
  isCreatingDataResult,
  isCreatingDidFail = false,
  isCreatingDidSucceed = false,
  isCreatingErrorResult,
  updateCategories,
}: Props) {
  // Reference to function used to abort request to create new category.
  const abortRef = useRef(noop)

  // Reference to input.
  const inputRef = useRef<HTMLInputElement>()
  const setInputRef = (ref: HTMLInputElement | HTMLTextAreaElement | null) =>
    (inputRef.current = ref as HTMLInputElement)

  const [isFormVisible, setIsFormVisible] = useState(false)
  const [categoryName, setCategoryName] = useState('')
  const [errorMessage, setErrorMessage] = useState('')

  // Declare behaviour for handling error when creating a new category.
  useOnError({
    callback: error => {
      setErrorMessage(getErrorMessageForCategoryName(error) ?? '')
      inputRef.current?.focus()
    },
    error: isCreatingErrorResult,
    isError: isCreatingDidFail,
    isLoading: isCreating,
  })

  // Declare behaviour for handling success when creating a new category.
  useOnSuccess({
    callback: data => {
      resetForm()

      abortRef.current = updateCategories([
        ...associatedCategories,
        (data as Category).id,
      ])

      setIsFormVisible(false)
    },
    data: isCreatingDataResult,
    isLoading: isCreating,
    isSuccess: isCreatingDidSucceed,
  })

  /**
   * Gets the value that can be provided to the state property on an Input
   * component.
   */
  function getInputState() {
    return errorMessage ? 'error' : undefined
  }

  /**
   * Handles press of "cancel" button, aborting in-flight create/update
   * request, resetting the form to its pristine state, and hiding the form.
   */
  function handleCancel() {
    abortRef.current && abortRef.current()
    resetForm()
    setIsFormVisible(false)
  }

  /**
   * Handles "change" event from Input component, updating the category name.
   */
  function handleChange(newCategoryName: string) {
    setCategoryName(newCategoryName)
  }

  /**
   * Handles press of the "new" button, showing the form.
   */
  function handleNew() {
    setIsFormVisible(true)
  }

  /**
   * Handles press of the "save" button and the "submit" event from the form,
   * creating the new category.
   */
  function handleSave(e?: SyntheticEvent) {
    e?.preventDefault()
    const sanitizedCategoryName = sanitizeCategoryName(categoryName)
    const maybeErrorMessage = validateCategoryName(sanitizedCategoryName)
    if (maybeErrorMessage) {
      setErrorMessage(maybeErrorMessage)
      return
    }

    abortRef.current = create({ name: sanitizedCategoryName })
  }

  /**
   * Resets the form to its pristine state by clearing errors and unsetting the
   * category name.
   */
  function resetForm() {
    setErrorMessage('')
    setCategoryName('')
  }

  return (
    <div>
      {!isFormVisible && (
        <NewButtonUI
          data-cy={TEST_IDS.ARTICLE_CATEGORY_MANAGER.NEW_CATEGORY_BUTTON}
          data-testid={TEST_IDS.ARTICLE_CATEGORY_MANAGER.NEW_CATEGORY_BUTTON}
          disabled={!isCreatable}
          linked
          inlined
          size="sm"
          color="blue"
          onClick={handleNew}
        >
          {TEXT.BUTTON.NEW_CATEGORY}
        </NewButtonUI>
      )}

      {isFormVisible && (
        <VerticalSpaceBetween gap="10px">
          <form
            autoComplete="off"
            data-cy={TEST_IDS.ARTICLE_CATEGORY_MANAGER.NEW_CATEGORY_FORM}
            data-testid={TEST_IDS.ARTICLE_CATEGORY_MANAGER.NEW_CATEGORY_FORM}
            onSubmit={handleSave}
          >
            <Input
              action={<SaveButton disabled={isCreating} onClick={handleSave} />}
              aria-label="Category Name"
              autoFocus
              data-cy={TEST_IDS.ARTICLE_CATEGORY_MANAGER.CATEGORY_NAME_INPUT}
              data-testid={
                TEST_IDS.ARTICLE_CATEGORY_MANAGER.CATEGORY_NAME_INPUT
              }
              disabled={isCreating}
              errorMessage={errorMessage}
              inputRef={setInputRef}
              name="categoryName"
              onChange={handleChange}
              state={getInputState()}
              type="text"
              value={categoryName}
            />
          </form>
          <Button
            data-cy={TEST_IDS.ARTICLE_CATEGORY_MANAGER.CANCEL_BUTTON}
            data-testid={TEST_IDS.ARTICLE_CATEGORY_MANAGER.CANCEL_BUTTON}
            disabled={isCreating}
            linked
            onClick={handleCancel}
            size="sm"
            color="grey"
          >
            {TEXT.BUTTON.CANCEL}
          </Button>
        </VerticalSpaceBetween>
      )}
    </div>
  )
}
