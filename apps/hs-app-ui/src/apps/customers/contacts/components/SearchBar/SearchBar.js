import PropTypes from 'prop-types'
import { useRef, useState } from 'react'
import { capitalize } from 'underscore.string'

import {
  CLEAR_ALL,
  MAILBOX_FILTER_ACTIONS,
  getSelectedItems,
} from './SearchBar.utils'

import {
  DropListButtonUI,
  IconButtonUI,
  IconUI,
  InputWrapperUI,
  SearchBarUI,
  TagWrapperUI,
} from './SearchBar.css'

import DropList from 'hsds/components/drop-list'
import Icon from 'hsds/components/icon'
import Input from 'hsds/components/input'
import Spinner from 'hsds/components/spinner'
import Tag from 'hsds/components/tag'
import { TagList } from 'hsds/components/tag'
import ChevronDown from 'hsds/icons/chevron-down-tiny'
import CircleCross from 'hsds/icons/cross-circle'
import SearchMedium from 'hsds/icons/search'

export function SearchBar({
  disabled = false,
  handleSearchBlur = () => {},
  handleSearchFocus = () => {},
  isLoading = false,
  items = [],
  minLength = 2,
  selectedItems = [],
  onSearchValueChange = () => {},
  onSelectedItemsChange = () => {},
  value = '',
}) {
  const inputEl = useRef(null)
  const [searchValue, setSearchValue] = useState(value)

  function handleOnInputChange(value) {
    setSearchValue(value)
    dispatchSearchValue(value)
  }

  function dispatchSearchValue(value) {
    // * minLength need to be higher than 1 if we want to limit dispatching the value change
    // * always dispatch if value length is 0
    if (minLength > 1 && value.length > 0 && value.length < minLength) {
      return
    }

    onSearchValueChange(value)
  }

  function clearSearch() {
    setSearchValue('')
    dispatchSearchValue('')
    inputEl && inputEl.current.inputNode.focus()
  }

  function handleOnSelect(selection, clickedItem) {
    if (
      clickedItem.value === CLEAR_ALL &&
      selection.length &&
      !clickedItem.remove
    ) {
      clearSelection()
    } else {
      onSelectedItemsChange({
        mailboxId: clickedItem.id,
        action: !clickedItem.remove
          ? MAILBOX_FILTER_ACTIONS.ADD
          : MAILBOX_FILTER_ACTIONS.REMOVE,
      })
    }
  }

  function clearSelection() {
    onSelectedItemsChange({ action: MAILBOX_FILTER_ACTIONS.REMOVE_ALL })
  }

  function handleRemoveTag(mailbox) {
    onSelectedItemsChange({
      mailboxId: mailbox.id,
      action: MAILBOX_FILTER_ACTIONS.REMOVE,
    })
  }

  function renderCurrentIcon() {
    return searchValue ? (
      <IconButtonUI
        aria-label="Clear Search"
        color="grey-light"
        size="sm"
        onClick={clearSearch}
        data-testid="ClearIcon"
        data-cy="CustomerList.SearchBar.ClearIcon"
        icon={CircleCross}
      />
    ) : (
      <IconUI>
        {isLoading ? (
          <Spinner
            data-testid="SpinnerIcon"
            data-cy="CustomerList.SearchBar.SpinnerIcon"
            size="md"
          />
        ) : (
          <Icon
            data-testid="SearchIcon"
            data-cy="CustomerList.SearchBar.SearchIcon"
            icon={SearchMedium}
            size="24"
          />
        )}
      </IconUI>
    )
  }

  function renderSuffixDropdown() {
    const { inboxNamePlural, inboxNameNoun } = window.hsGlobal.features
    const allMailboxesItem = {
      className: 'allmailboxes-item',
      isDisabled: !selectedItems.length,
      label: `All ${capitalize(inboxNamePlural)}`,
      value: CLEAR_ALL,
    }

    return (
      <DropList
        autoSetComboboxAt={15}
        closeOnSelection={false}
        focusTogglerOnMenuClose={false}
        items={[allMailboxesItem, { type: 'divider' }].concat(items)}
        onSelect={handleOnSelect}
        tippyOptions={{ offset: [0, 10], placement: 'bottom-end' }}
        toggler={
          <DropListButtonUI
            size="md"
            color="grey"
            outlined
            text={
              <div className="DropListButton__content">
                <span>{capitalize(inboxNameNoun)}</span>
                <Icon icon={ChevronDown} size={24} />{' '}
              </div>
            }
          />
        }
        selection={
          selectedItems.length
            ? getSelectedItems(items, selectedItems)
            : allMailboxesItem
        }
        withMultipleSelection
      />
    )
  }

  function renderTags() {
    const tags = getSelectedItems(items, selectedItems).map(
      ({ value, label }) => (
        <Tag key={value} filled color="blue" id={value}>
          {label}
        </Tag>
      )
    )

    return (
      <TagList
        clearAll
        data-testid="TagList"
        showAll
        isRemovable
        onRemove={handleRemoveTag}
        onRemoveAll={clearSelection}
        size="md"
      >
        {tags}
      </TagList>
    )
  }

  return (
    <SearchBarUI
      data-testid="CustomerListSearchBar"
      data-cy="CustomerList.SearchBar"
    >
      <InputWrapperUI>
        <Input
          aria-label="Search customers"
          data-testid="Input"
          autoComplete="off"
          placeholder="Search"
          name="customerList-search"
          suffix={renderSuffixDropdown()}
          inlineSuffix={renderCurrentIcon()}
          tabIndex={0}
          onBlur={handleSearchBlur}
          onChange={handleOnInputChange}
          onFocus={handleSearchFocus}
          value={searchValue}
          disabled={disabled}
          ref={inputEl}
        />
      </InputWrapperUI>
      <TagWrapperUI>{renderTags()}</TagWrapperUI>
    </SearchBarUI>
  )
}

SearchBar.propTypes = {
  disabled: PropTypes.bool,
  handleSearchBlur: PropTypes.func,
  handleSearchFocus: PropTypes.func,
  isLoading: PropTypes.bool,
  items: PropTypes.arrayOf(
    PropTypes.shape({
      value: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
      label: PropTypes.string,
    })
  ).isRequired,
  selectedItems: PropTypes.arrayOf(
    PropTypes.oneOfType([PropTypes.string, PropTypes.number])
  ),
  minLength: PropTypes.number,
  onSearchValueChange: PropTypes.func,
  onSelectedItemsChange: PropTypes.func,
  value: PropTypes.string,
}

export default SearchBar
