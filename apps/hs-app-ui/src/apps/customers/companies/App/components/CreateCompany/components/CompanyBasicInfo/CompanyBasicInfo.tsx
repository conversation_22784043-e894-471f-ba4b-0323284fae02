import { useState } from 'react'

import {
  AddressInputUI,
  CompanyBasicInfoUI,
  CompanyBasicInfoWrapperUI,
} from './CompanyBasicInfo.css'

import { OptionEditor } from '../../../OptionEditor'
import { Notes } from './components/Notes'

const CompanyBasicInfo = () => {
  const [address, setAddress] = useState('')

  const handleAddressChange = (value: string) => {
    setAddress(value)
  }

  return (
    <CompanyBasicInfoUI>
      <CompanyBasicInfoWrapperUI>
        {/* Websites */}
        <OptionEditor
          title="Websites"
          hasCheckbox={true}
          checkboxLabel="Contact syncing"
          optionLabel="Primary"
        />

        {/* Phone */}
        <OptionEditor title="Phone" />

        {/* Address */}
        <AddressInputUI
          type="text"
          className=""
          value={address}
          label="Address"
          onChange={handleAddressChange}
          onKeyDown={(
            event: React.KeyboardEvent<HTMLInputElement | HTMLTextAreaElement>
          ) => {
            // This will prevent triggering global shortcuts (or similar things)
            // set somewhere up in the DOM
            if (event.key === 'Delete' || event.key === 'Backspace') {
              event.stopPropagation()
            }
          }}
          multiline={true}
        />

        {/* Notes */}
        <Notes notes={'lorem ipsum dolor sit amet'} />
      </CompanyBasicInfoWrapperUI>
    </CompanyBasicInfoUI>
  )
}

export { CompanyBasicInfo }
