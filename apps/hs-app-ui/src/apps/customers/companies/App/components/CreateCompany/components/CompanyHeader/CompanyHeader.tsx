import { ChangeEvent, useEffect, useState } from 'react'

import {
  AvatarUI,
  AvatarUIWrapperUI,
  CircleSeparatorUI,
  CompanyNameFieldUI,
  CreateCompanyHeaderUI,
  InfoWrapperUI,
  LocationTextUI,
  WebsiteLinkUI,
} from './CompanyHeader.css'

import { COLOR_BLUE } from 'hsds/components/button'
import Camera from 'hsds/icons/camera'

interface CreateCompanyHeaderProps {
  companyName: string
  onCompanyNameChange: (name: string) => void
  onImageUpload: (file: File) => void
  website?: string
  address?: string
}

interface SaveCompanyNameValue {
  value: { value: string }[]
}

const CompanyHeader = ({
  companyName,
  onCompanyNameChange,
  onImageUpload,
  website,
  address,
}: CreateCompanyHeaderProps) => {
  const [previewImage, setPreviewImage] = useState<string | null>(null)

  useEffect(() => {
    const input = document.createElement('input')
    input.type = 'file'
    input.accept = 'image/*'
    input.style.display = 'none'
    input.onchange = e =>
      handleImageChange(e as unknown as ChangeEvent<HTMLInputElement>)
    document.body.appendChild(input)

    return () => {
      document.body.removeChild(input)
    }
  }, [])

  const handleImageChange = (event: ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (file) {
      onImageUpload(file)
      const reader = new FileReader()
      reader.readAsDataURL(file)
      reader.onloadend = () => {
        setPreviewImage(reader.result as string)
      }
    }
  }

  function handleOnSaveCompanyName({ value }: SaveCompanyNameValue) {
    const updatedCompanyName = value?.[0]?.value.trim()

    if (!updatedCompanyName) {
      return
    }
    onCompanyNameChange(updatedCompanyName)
  }

  const avatarProps = {
    size: 'xxxl' as const,
    image: previewImage,
    actionable: true,
    actionIcon: Camera,
    shape: 'square' as const,
    onActionClick: () => {
      const input = document.querySelector(
        'input[type="file"]'
      ) as HTMLInputElement
      if (input) {
        input.click()
      }
    },
    'aria-label': `${previewImage ? 'Edit' : 'View'} company image`,
  }

  return (
    <CreateCompanyHeaderUI>
      <AvatarUIWrapperUI>
        <AvatarUI {...avatarProps} />
      </AvatarUIWrapperUI>

      <InfoWrapperUI>
        <CompanyNameFieldUI
          placeholder="Type to add the company name"
          name="company-name"
          label=""
          value={companyName}
          onCommit={handleOnSaveCompanyName}
          // onInputChange
          maxRows={1}
        />
        <div>
          {website && (
            <>
              <WebsiteLinkUI
                className="profile-card-website"
                linked
                color={COLOR_BLUE}
                title={`Website ${website}`}
                href={website}
                target="_blank"
                rel="noopener noreferrer"
              >
                {website}
              </WebsiteLinkUI>
              {address && <CircleSeparatorUI>•</CircleSeparatorUI>}
            </>
          )}
          {address && <LocationTextUI size="14">{address}</LocationTextUI>}
        </div>
      </InfoWrapperUI>
    </CreateCompanyHeaderUI>
  )
}

export { CompanyHeader }
