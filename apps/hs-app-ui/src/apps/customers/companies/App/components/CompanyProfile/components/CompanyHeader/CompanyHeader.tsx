import { ChangeEvent, useMemo, useState } from 'react'
import React from 'react'
import { useHistory } from 'react-router'

import { getCacheBustedUrl } from '../../../../../utils'

import {
  AvatarUI,
  AvatarUIWrapperUI,
  CircleSeparatorUI,
  CompanyNameTextUI,
  CreateCompanyHeaderUI,
  CustomerAvatarUI,
  InfoWrapperUI,
  LocationTextUI,
  WebsiteLinkUI,
} from './CompanyHeader.css'

import { useCompanyLogo } from './useCompanyLogo'
import { AvatarList } from 'hsds/components/avatar'
import { COLOR_BLUE } from 'hsds/components/button'
import Pencil from 'hsds/icons/pencil'
import Store from 'hsds/icons/store'

interface CreateCompanyHeaderProps {
  companyName?: string
  organizationId: number
  website?: string
  address?: string
  logoUrl?: string
  contactsCount?: number
  customerData?: Array<{
    firstName?: string
    lastName?: string
    image?: string
    defaultAvatarUrl?: string
    photoUrl?: string
    id: number
  }>
  defaultBrandColorName?: string
  brandColor?: string
}

const CompanyHeader = ({
  companyName = '',
  organizationId,
  logoUrl,
  website,
  address,
  customerData,
  contactsCount,
  defaultBrandColorName,
  brandColor,
}: CreateCompanyHeaderProps) => {
  const history = useHistory()
  const { previewImage, handleImageChange } = useCompanyLogo({
    organizationId,
    initialLogoUrl: logoUrl,
  })

  const cacheBustedImage = useMemo(
    () => getCacheBustedUrl(previewImage),
    [previewImage]
  )

  const [isSquare, setIsSquare] = useState(true)

  const MAX_CUSTOMER_AVATARS = 6

  const avatarBackgroundColor = logoUrl ? brandColor : undefined

  const avatarProps = {
    size: 'xxxxl' as const,
    backgroundColor: avatarBackgroundColor,
    image: cacheBustedImage,
    actionable: true,
    actionIcon: Pencil,
    fallbackIcon: Store,
    className: defaultBrandColorName,
    shape: 'square' as const,
    onActionClick: () => {
      const input = document.createElement('input')
      input.type = 'file'
      input.accept = 'image/*'
      input.onchange = e =>
        handleImageChange(e as unknown as ChangeEvent<HTMLInputElement>)
      input.click()
    },
    'aria-label': `${previewImage ? 'Edit' : 'View'} company image`,
  }

  const redirectToCustomerProfile = (customerId: number) => {
    history.push(`/customers/${customerId}`)
  }

  return (
    <CreateCompanyHeaderUI>
      {/* This is a hiddend image that we need to check if the image is square */}
      <img
        src={cacheBustedImage}
        alt=""
        aria-hidden="true"
        tabIndex={-1}
        style={{ display: 'none' }}
        onLoad={e => {
          const img = e.currentTarget
          setIsSquare(img.naturalWidth === img.naturalHeight)
        }}
      />
      <AvatarUIWrapperUI
        brandColor={brandColor}
        defaultBrandColorName={defaultBrandColorName}
        isSquare={isSquare}
      >
        <AvatarUI {...avatarProps} />
      </AvatarUIWrapperUI>

      <InfoWrapperUI>
        <CompanyNameTextUI size="20">{companyName}</CompanyNameTextUI>
        <div>
          {website && (
            <>
              <WebsiteLinkUI
                className="profile-card-website"
                linked
                color={COLOR_BLUE}
                title={`Website ${website}`}
                href={website}
                target="_blank"
                rel="noopener noreferrer"
              >
                {website}
              </WebsiteLinkUI>
              {address && <CircleSeparatorUI>•</CircleSeparatorUI>}
            </>
          )}
          {address && <LocationTextUI size="14">{address}</LocationTextUI>}
        </div>
      </InfoWrapperUI>

      <AvatarList
        max={MAX_CUSTOMER_AVATARS}
        totalItemsLength={contactsCount}
        size="md"
        stack="horizontal"
      >
        {customerData?.map(
          (
            customer: NonNullable<typeof customerData>[number],
            index: number
          ) => (
            <CustomerAvatarUI
              name={`${customer.firstName || ''} ${
                customer.lastName || ''
              }`.trim()}
              image={customer.photoUrl || customer.defaultAvatarUrl}
              key={`${index}-${customer.firstName}-${customer.lastName}`}
              onClick={() => redirectToCustomerProfile(customer.id)}
              role="button"
              tabIndex={0}
              aria-label={`View profile for ${customer.firstName || ''} ${
                customer.lastName || ''
              }`.trim()}
              onKeyDown={(e: React.KeyboardEvent<HTMLDivElement>) => {
                if (e.key === 'Enter' || e.key === ' ') {
                  redirectToCustomerProfile(customer.id)
                }
              }}
            />
          )
        )}
      </AvatarList>
    </CreateCompanyHeaderUI>
  )
}

export { CompanyHeader }
