/* eslint-disable react/display-name */
import cx from 'classnames'
import React from 'react'

import { convertEmailsToStrings, dedupEmails, formatDate } from './utils'

import {
  AvatarUI,
  ButtonUI,
  CellHeaderTextUI,
  FlexyBlockUI,
  IconHeaderUI,
  PrimaryTextUI,
  SecondaryTextUI,
  SkeletonAvatarUI,
  SkeletonTextUI,
  SortIconUI,
  TruncateUI,
} from './Cell.css'

import FilteredList from 'hsds/components/filtered-list'
import Flexy from 'hsds/components/flexy'
import VisuallyHidden from 'hsds/components/visually-hidden'
import type { IconType } from 'hsds/icons/baseIcon'
import CaretDown from 'hsds/icons/caret-down'
import CaretUp from 'hsds/icons/caret-up'

export const SORT_ORDER_DESCENDING = 'desc'

type CellProps = {
  [key: string]: any // Allow any other props passed down by the table
}

export type EmailObj = {
  id: number
  location: string
  value: string
  createdAt: string
  default: boolean
  updatedAt: string
}

type CountCellProps = CellProps & {
  customerCount?: number
  conversationCount?: number
}

type GetHeaderCellOptions = {
  icon?: IconType
  sortCallback?: (sortOn: string | boolean, sortedInfo: SortedInfo) => void
  sortOn?: string | boolean
}

type HeaderCellInnerProps = {
  title?: string
}

type SortedInfo = {
  columnKey?: string | boolean
  order?: string
}

type GetSkeletonCellOptions = {
  withAvatar?: boolean
}

type AvatarSize =
  | 'xs'
  | 'sm'
  | 'md'
  | 'lg'
  | 'xl'
  | 'xxl'
  | 'xxxl'
  | 'xxxxl'
  | 'xxs'
  | 'smmd'

type SkeletonCellInnerProps = {
  size?: AvatarSize
  width?: string
}

type DateCellProps = {
  lastSeenAt?: string
  updatedAt?: string
}

type CustomerCellProps = CellProps & {
  firstName?: string
  lastName?: string
  emails?: EmailObj[]
  jobTitle?: string
  organization?: string
  photoUrl?: string
  defaultAvatarUrl?: string
  photoType?: string
  lastSeenAt?: string
  updatedAt?: string
}

type EmailCellProps = {
  emails?: EmailObj[]
}

export const getHeaderCell = ({
  icon,
  sortCallback = () => {},
  sortOn = false,
}: GetHeaderCellOptions = {}) => {
  return (
    { title }: HeaderCellInnerProps = {},
    sortedInfo: SortedInfo = {}
  ): JSX.Element => {
    const innerContent = icon ? (
      <span>
        <VisuallyHidden>{title}</VisuallyHidden>
        <IconHeaderUI
          data-testid="IconHeaderUI"
          icon={icon}
          data-cy="CustomerList.Header.IconHeader"
        />
      </span>
    ) : (
      <span>{title}</span>
    )
    const isCurrentSortColumn = sortOn === sortedInfo.columnKey

    let direction: 'up' | 'down'
    if (!isCurrentSortColumn) {
      direction = 'up'
    } else {
      direction = sortedInfo.order === SORT_ORDER_DESCENDING ? 'down' : 'up'
    }

    const weight = isCurrentSortColumn ? 700 : 500
    const classNames = cx(
      icon && 'is-icon-only',
      !isCurrentSortColumn && 'is-hidden'
    )

    const content = (
      <CellHeaderTextUI weight={weight}>
        {innerContent}
        <SortIconUI
          data-testid="SortIconUI"
          className={classNames}
          icon={direction === 'up' ? CaretUp : CaretDown}
          size="14"
          data-cy="CustomerList.Header.SortIcon"
        />
      </CellHeaderTextUI>
    )

    if (!sortOn) {
      return content
    }

    return (
      <ButtonUI
        color="grey"
        linked
        inlined
        onClick={() => {
          if (typeof sortOn === 'string' || typeof sortOn === 'boolean') {
            // Type guard
            sortCallback(sortOn, sortedInfo)
          }
        }}
      >
        {content}
      </ButtonUI>
    )
  }
}

export const getSkeletonCell =
  ({ withAvatar = false }: GetSkeletonCellOptions = {}) =>
  ({
    size = 'sm',
    width = '100%',
  }: SkeletonCellInnerProps = {}): JSX.Element => {
    const text = <SkeletonTextUI data-testid="SkeletonTextUI" width={width} />

    if (!withAvatar) {
      return text
    }

    return (
      <Flexy just="center">
        <Flexy.Item>
          <SkeletonAvatarUI data-testid="SkeletonAvatarUI" size={size} />
        </Flexy.Item>
        <FlexyBlockUI>{text}</FlexyBlockUI>
      </Flexy>
    )
  }

function renderAvatar(image?: string, defaultAvatarUrl?: string): JSX.Element {
  return (
    <AvatarUI
      image={image || defaultAvatarUrl}
      size="sm"
      fallbackImage={defaultAvatarUrl}
    />
  )
}

function renderFullName(
  firstName?: string,
  lastName?: string
): JSX.Element | null {
  if (!firstName && !lastName) {
    return null
  }

  return (
    <PrimaryTextUI data-testid="PrimaryTextUI" size={14} weight={700}>
      {firstName} {lastName}
    </PrimaryTextUI>
  )
}

export const CountCell: React.FC<CountCellProps> = ({
  conversationCount,
} = {}) => <SecondaryTextUI>{conversationCount}</SecondaryTextUI>

export const CustomerCell: React.FC<CustomerCellProps> = ({
  firstName,
  lastName,
  photoUrl: image,
  defaultAvatarUrl,
} = {}) => {
  return (
    <Flexy just="center">
      <Flexy.Item>{renderAvatar(image, defaultAvatarUrl)}</Flexy.Item>
      <FlexyBlockUI>{renderFullName(firstName, lastName)}</FlexyBlockUI>
    </Flexy>
  )
}

export const EmailCell: React.FC<EmailCellProps> = ({ emails = [] } = {}) => {
  const uniqueEmails = dedupEmails(emails)
  const emailStrings = convertEmailsToStrings(uniqueEmails)

  return emailStrings.length ? (
    <FilteredList
      items={emailStrings}
      renderItem={item => (
        <TruncateUI splitter="@" showTooltipOnTruncate={true}>
          {item}
        </TruncateUI>
      )}
      limit={2}
    />
  ) : null
}

export const DateCell: React.FC<DateCellProps> = ({
  lastSeenAt,
  updatedAt,
} = {}) => (
  <SecondaryTextUI data-timestamp={lastSeenAt || updatedAt}>
    {formatDate(lastSeenAt || updatedAt)}
  </SecondaryTextUI>
)
