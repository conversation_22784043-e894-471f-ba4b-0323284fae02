import { MemoryRouter, Route } from 'react-router-dom'

import { render, screen } from '@testing-library/react'

import { CompanyTabNavigation } from './CompanyTabNavigation'

describe('CompanyTabNavigation', () => {
  const renderWithRouter = (
    initialPath: string,
    conversationsCount = 0,
    contactsCount = 0
  ) => {
    return render(
      <MemoryRouter initialEntries={[initialPath]}>
        <Route path="/companies/:id/:activeTab?">
          <CompanyTabNavigation
            conversationsCount={conversationsCount}
            contactsCount={contactsCount}
          />
        </Route>
      </MemoryRouter>
    )
  }

  it('renders all tabs with correct labels', () => {
    renderWithRouter('/companies/123/info')

    expect(screen.getByTestId('CustomersNavigation')).toBeInTheDocument()
    expect(screen.getByTestId('CustomersNavigation-info')).toHaveTextContent(
      'Profile'
    )
    expect(
      screen.getByTestId('CustomersNavigation-conversations')
    ).toHaveTextContent('Conversations')
    expect(
      screen.getByTestId('CustomersNavigation-contacts')
    ).toHaveTextContent('Contacts')
  })

  it('shows badge counts for conversations and contacts', () => {
    renderWithRouter('/companies/123/info', 5, 3)

    expect(
      screen.getByTestId('CustomersNavigation-conversations')
    ).toHaveTextContent('5')
    expect(
      screen.getByTestId('CustomersNavigation-contacts')
    ).toHaveTextContent('3')
  })

  it('shows badge counts for conversations and contacts with commas for numbers greater than 999', () => {
    renderWithRouter('/companies/123/info', 1000, 1000)

    expect(
      screen.getByTestId('CustomersNavigation-conversations')
    ).toHaveTextContent('1,000')
    expect(
      screen.getByTestId('CustomersNavigation-contacts')
    ).toHaveTextContent('1,000')
  })

  it('does not show badge for basic info tab', () => {
    renderWithRouter('/companies/123/info', 5, 3)

    const basicInfoTab = screen.getByTestId('CustomersNavigation-info')
    expect(basicInfoTab).not.toHaveTextContent(/[0-9]/)
  })

  it('marks the current tab as active', () => {
    renderWithRouter('/companies/123/conversations')

    const conversationsTab = screen.getByTestId(
      'CustomersNavigation-conversations'
    )
    expect(conversationsTab).toHaveAttribute('aria-current', 'page')
  })

  it('defaults to info tab when no tab is specified', () => {
    renderWithRouter('/companies/123')

    const infoTab = screen.getByTestId('CustomersNavigation-info')
    expect(infoTab).toHaveAttribute('aria-current', 'page')
  })
})
