import { getColor } from 'hsds/utils/color'

export const CONVO_STATUS_CLASSNAMES: Record<string, string> = {
  open: 'row-open',
  pending: 'row-pending',
  closed: 'row-closed',
  spam: 'row-spam',
}

export const skin = {
  fontColorHeader: getColor('charcoal.600'),
  fontColorBody: getColor('charcoal.800'),
  fontColorAlternate: getColor('charcoal.800'),
  bgColor: getColor('color.background.surface'),
  bgHeader: getColor('color.background.surface'),
  bgAlternate: getColor('color.background.surface'),
  bgColorHover: getColor('cobalt.100'),
  bgSelected: getColor('cobalt.100'),
  bgSelectedHover: getColor('cobalt.200'),
  borderTableBody: `1px solid ${getColor('charcoal.400')}`,
  borderTableHeader: `1px solid ${getColor('charcoal.400')}`,
  borderRows: `1px solid ${getColor('charcoal.400')}`,
  borderColumns: 'none',
}
