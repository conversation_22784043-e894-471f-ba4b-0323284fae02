import { CompanyConversationsWrapperUI } from './CompanyConversations.css'

import { CompanyConversationsList } from './components/CompanyConversationsList/CompanyConversationsList'

interface CompanyConversationsProps {
  conversationsData?: {
    items: any[]
    pages: number
    count: number
  }
  pagination: {
    page: number
    pageLimit: number
    totalItems: number
    goToPage: (page: number) => void
  }
  isLoading: boolean
}

export const CompanyConversations = ({
  conversationsData,
  pagination,
  isLoading,
}: CompanyConversationsProps) => {
  return (
    <CompanyConversationsWrapperUI>
      <CompanyConversationsList
        conversationsData={conversationsData}
        pagination={pagination}
        isLoading={isLoading}
      />
    </CompanyConversationsWrapperUI>
  )
}
