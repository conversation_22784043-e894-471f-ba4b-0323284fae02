/* eslint-disable react/display-name */
import cx from 'classnames'
import React from 'react'

import {
  AvatarUI,
  BadgeCountUI,
  ButtonUI,
  CellHeaderTextUI,
  FlexyBlockUI,
  IconHeaderUI,
  SecondaryTextUI,
  SkeletonAvatarUI,
  SkeletonTextUI,
  SortIconUI,
  TruncatePrimaryUI,
  TruncateUI,
} from './Cell.css'

import Flexy from 'hsds/components/flexy'
import Icon from 'hsds/components/icon'
import VisuallyHidden from 'hsds/components/visually-hidden'
import type { IconType } from 'hsds/icons/baseIcon'
import CaretDown from 'hsds/icons/caret-down'
import CaretUp from 'hsds/icons/caret-up'
import Paperclip from 'hsds/icons/paperclip-tiny'

export const SORT_ORDER_DESCENDING = 'desc'

export type CellProps = {
  [key: string]: any
}

export type EmailObj = {
  id: number
  location: string
  value: string
  createdAt: string
  default: boolean
  updatedAt: string
}

type GetHeaderCellOptions = {
  icon?: IconType
  sortCallback?: (sortOn: string | boolean, sortedInfo: SortedInfo) => void
  sortOn?: string | boolean
}

type HeaderCellInnerProps = {
  title?: string
}

type SortedInfo = {
  columnKey?: string | boolean
  order?: string
}

type GetSkeletonCellOptions = {
  withAvatar?: boolean
}

type AvatarSize =
  | 'xs'
  | 'sm'
  | 'md'
  | 'lg'
  | 'xl'
  | 'xxl'
  | 'xxxl'
  | 'xxxxl'
  | 'xxs'
  | 'smmd'

type SkeletonCellInnerProps = {
  size?: AvatarSize
  width?: string
}

type AssignedCellProps = CellProps & {
  assignee?: {
    id: number
    first?: string
    last?: string
    type?: string
    email?: string
  }
  status?: string
}

type CustomerCellProps = CellProps & {
  status?: string
  customers?: {
    first?: string
    last?: string
    emails?: EmailObj[]
    photoUrl?: string
    defaultAvatarUrl?: string
    phone?: string[]
  }[]
}

type NumberCellProps = CellProps & {
  number?: string
  status?: string
}

type WaitingCellProps = CellProps & {
  customerWaitingSince?: {
    friendly: string
    time: string
  }
  status?: string
}

type ThreadCountCellProps = CellProps & {
  threads?: number
}

type AttachmentCountCellProps = CellProps & {
  attachments?: boolean
}

export const getHeaderCell = ({
  icon,
  sortCallback = () => {},
  sortOn = false,
}: GetHeaderCellOptions = {}) => {
  return (
    { title }: HeaderCellInnerProps = {},
    sortedInfo: SortedInfo = {}
  ): JSX.Element => {
    const innerContent = icon ? (
      <span>
        <VisuallyHidden>{title}</VisuallyHidden>
        <IconHeaderUI data-testid="IconHeaderUI" icon={icon} />
      </span>
    ) : (
      <span>{title}</span>
    )
    const isCurrentSortColumn = sortOn === sortedInfo.columnKey

    let direction: 'up' | 'down'
    if (!isCurrentSortColumn) {
      direction = 'up'
    } else {
      direction = sortedInfo.order === SORT_ORDER_DESCENDING ? 'down' : 'up'
    }

    const weight = isCurrentSortColumn ? 700 : 500
    const classNames = cx(
      icon && 'is-icon-only',
      !isCurrentSortColumn && 'is-hidden'
    )

    const content = (
      <CellHeaderTextUI weight={weight}>
        {innerContent}
        <SortIconUI
          data-testid="SortIconUI"
          className={classNames}
          icon={direction === 'up' ? CaretUp : CaretDown}
          size="14"
        />
      </CellHeaderTextUI>
    )

    if (!sortOn) {
      return content
    }

    return (
      <ButtonUI
        color="grey"
        linked
        inlined
        onClick={() => {
          if (typeof sortOn === 'string' || typeof sortOn === 'boolean') {
            // Type guard
            sortCallback(sortOn, sortedInfo)
          }
        }}
      >
        {content}
      </ButtonUI>
    )
  }
}

export const getSkeletonCell =
  ({ withAvatar = false }: GetSkeletonCellOptions = {}) =>
  ({
    size = 'sm',
    width = '100%',
  }: SkeletonCellInnerProps = {}): JSX.Element => {
    const text = <SkeletonTextUI data-testid="SkeletonTextUI" width={width} />

    if (!withAvatar) {
      return text
    }

    return (
      <Flexy just="center">
        <Flexy.Item>
          <SkeletonAvatarUI data-testid="SkeletonAvatarUI" size={size} />
        </Flexy.Item>
        <FlexyBlockUI>{text}</FlexyBlockUI>
      </Flexy>
    )
  }

function renderAvatar(image?: string, defaultAvatarUrl?: string): JSX.Element {
  return (
    <AvatarUI
      image={image || defaultAvatarUrl}
      size="sm"
      fallbackImage={defaultAvatarUrl}
    />
  )
}

function renderFullName(
  firstName?: string,
  lastName?: string
): JSX.Element | null {
  if (!firstName && !lastName) {
    return null
  }

  return (
    <TruncatePrimaryUI>
      {firstName} {lastName}
    </TruncatePrimaryUI>
  )
}

export const CustomerCell: React.FC<CustomerCellProps> = ({
  customers = [],
  status,
} = {}) => {
  const mainCustomer = customers[0] || {}
  const { first, last, photoUrl: image, defaultAvatarUrl } = mainCustomer

  return (
    <Flexy just="center">
      <Flexy.Item>{renderAvatar(image, defaultAvatarUrl)}</Flexy.Item>
      <FlexyBlockUI className={cx({ 'is-inactive': status !== 'active' })}>
        {renderFullName(first, last)}
      </FlexyBlockUI>
    </Flexy>
  )
}

export const AssignedCell: React.FC<AssignedCellProps> = ({
  assignee,
  status,
} = {}) => {
  const assigneeName =
    assignee?.first && assignee?.last
      ? `${assignee?.first} ${assignee?.last}`
      : assignee?.email

  return (
    <TruncateUI
      showTooltipOnTruncate
      tooltipProps={{
        renderTitleAsHtml: false,
      }}
      className={cx({
        'is-inactive': status !== 'active',
        'is-bold': true,
      })}
    >
      {assigneeName}
    </TruncateUI>
  )
}

export const NumberCell: React.FC<NumberCellProps> = ({
  number,
  status,
} = {}) => (
  <SecondaryTextUI className={cx({ 'is-inactive': status !== 'active' })}>
    {number}
  </SecondaryTextUI>
)

export const WaitingCell: React.FC<WaitingCellProps> = ({
  customerWaitingSince,
  status,
} = {}) => (
  <SecondaryTextUI className={cx({ 'is-inactive': status !== 'active' })}>
    {customerWaitingSince?.friendly}
  </SecondaryTextUI>
)

export const ThreadCountCell: React.FC<ThreadCountCellProps> = ({
  threads,
} = {}) => {
  if (threads === 0) {
    return null
  }

  return <BadgeCountUI>{threads}</BadgeCountUI>
}

export const AttachmentCountCell: React.FC<AttachmentCountCellProps> = ({
  attachments,
}: AttachmentCountCellProps) => {
  if (!attachments) {
    return null
  }

  return <Icon size="24" icon={Paperclip} />
}

export { ConversationCell } from './ConversationCell'
