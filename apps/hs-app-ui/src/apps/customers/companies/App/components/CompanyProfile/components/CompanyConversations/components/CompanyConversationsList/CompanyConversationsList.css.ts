import styled from 'styled-components'

import { getColor } from 'hsds/utils/color'

import { Pagination } from 'hsds/components/pagination'
import Table from 'hsds/components/table'
import Text from 'hsds/components/text'

export const PaginationUI = styled(Pagination)`
  margin-top: 24px;
`

export const EmptyStateTextUI = styled(Text)`
  margin-top: 24px;
  text-align: center;
`

export const CompanyConversationsTableUI = styled(Table)`
  .c-Table__Row {
    height: 78px;
  }

  .c-Table__Row.row-closed {
    background-color: ${getColor('charcoal.100')};
  }
`
