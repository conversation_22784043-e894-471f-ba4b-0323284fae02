import { fireEvent, render, screen, waitFor } from '@testing-library/react'

import { useSaveCompany } from '../../../../../hooks/useSaveCompany'

import { Company } from '../../../../../types/companies'
import { CompanyBasicInfo } from './CompanyBasicInfo'

// Mock the useSaveCompany hook
jest.mock('../../../../../hooks/useSaveCompany', () => ({
  useSaveCompany: jest.fn(),
}))

const mockCompany: Company = {
  id: 1,
  name: 'Test Company',
  website: 'https://test.com',
  description: 'Test description',
  note: 'Test note',
  domain: 'test.com',
  phones: ['+1 ***********', '****** 222 222'],
  customerCount: 5,
  conversationCount: 10,
  defaultBrandColorName: 'blue',
}

describe('CompanyBasicInfo', () => {
  beforeEach(() => {
    // Reset the mock implementation before each test
    jest.clearAllMocks()
  })

  it('renders company information correctly', () => {
    render(<CompanyBasicInfo company={mockCompany} />)

    expect(screen.getByLabelText('Company Name')).toHaveValue('Test Company')
    expect(screen.getByLabelText('Description')).toHaveValue('Test description')
    expect(screen.getByLabelText('Notes')).toHaveValue('Test note')
    expect(screen.getByLabelText('Domain')).toHaveValue('test.com')
    expect(screen.getByText('+1 ***********')).toBeInTheDocument()
    expect(screen.getByText('****** 222 222')).toBeInTheDocument()
  })

  it('updates form fields when user types', () => {
    render(<CompanyBasicInfo company={mockCompany} />)

    const nameInput = screen.getByLabelText('Company Name')
    fireEvent.change(nameInput, { target: { value: 'New Company Name' } })
    expect(nameInput).toHaveValue('New Company Name')
  })

  it('calls saveCompany when save button is clicked', async () => {
    const mockSaveCompany = jest.fn()
    ;(useSaveCompany as jest.Mock).mockReturnValue({
      saveCompany: mockSaveCompany,
    })

    render(<CompanyBasicInfo company={mockCompany} />)

    const saveButton = screen.getByText('Update')
    fireEvent.click(saveButton)

    await waitFor(() => {
      expect(mockSaveCompany).toHaveBeenCalledWith({
        organizationId: 1,
        organization: {
          name: 'Test Company',
          website: 'https://test.com',
          note: 'Test note',
          description: 'Test description',
          phones: ['+1 ***********', '****** 222 222'],
        },
      })
    })
  })

  it('handles empty optional fields correctly', async () => {
    const mockSaveCompany = jest.fn()
    ;(useSaveCompany as jest.Mock).mockReturnValue({
      saveCompany: mockSaveCompany,
    })

    const companyWithEmptyFields = {
      ...mockCompany,
      website: '',
      description: '',
      note: '',
      phones: [],
    }

    render(<CompanyBasicInfo company={companyWithEmptyFields} />)

    const saveButton = screen.getByText('Update')
    fireEvent.click(saveButton)

    await waitFor(() => {
      expect(mockSaveCompany).toHaveBeenCalledWith({
        organizationId: 1,
        organization: {
          name: 'Test Company',
          website: undefined,
          note: undefined,
          description: undefined,
          phones: undefined,
        },
      })
    })
  })

  it('disables save button when company name is blank', async () => {
    render(<CompanyBasicInfo company={mockCompany} />)

    const nameInput = screen.getByLabelText('Company Name')
    const saveButton = screen.getByTestId('save-company-info')

    // Clear the company name
    fireEvent.change(nameInput, { target: { value: '' } })

    await waitFor(() => {
      expect(saveButton).toHaveAttribute('disabled')
    })
  })

  it('enables save button when company name is not blank', async () => {
    render(<CompanyBasicInfo company={mockCompany} />)

    const nameInput = screen.getByLabelText('Company Name')
    const saveButton = screen.getByTestId('save-company-info')

    // Ensure company name has a value
    fireEvent.change(nameInput, { target: { value: 'Test Company' } })

    await waitFor(() => {
      expect(saveButton).not.toHaveAttribute('disabled')
    })
  })
})
