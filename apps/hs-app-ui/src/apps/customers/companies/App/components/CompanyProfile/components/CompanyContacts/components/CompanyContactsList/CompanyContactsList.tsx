import { useHistory } from 'react-router-dom'

import { EmptyStateTextUI, PaginationUI } from './CompanyContactsList.css'

import { skin } from './CompanyContactList.constants'
import { Loading } from './components/Loading/Loading'
import { tableConfig } from './table.config'
import Table from 'hsds/components/table'

// eslint-disable-next-line @typescript-eslint/no-unsafe-assignment, @typescript-eslint/no-explicit-any
const CompanyContactsTable: any = Table

interface CompanyContactsListProps {
  customerData?: {
    items: any[]
    pages: number
    count: number
  }
  pagination: {
    page: number
    pageLimit: number
    totalItems: number
    goToPage: (page: number) => void
  }
  isLoading: boolean
}

export const CompanyContactsList = ({
  customerData,
  pagination,
  isLoading,
}: CompanyContactsListProps) => {
  const history = useHistory()

  if (isLoading) {
    return <Loading />
  }

  if (!isLoading && !customerData?.items?.length) {
    return <EmptyStateTextUI>No contacts found</EmptyStateTextUI>
  }

  const handleRowClick = (_: any, row: { id: number }) => {
    history.push(`/customers/${row?.id}`)
  }

  return (
    <div>
      <CompanyContactsTable
        columnChooserResetLabel="Reset"
        isLoading={isLoading}
        columns={tableConfig().columns}
        data={customerData?.items || []}
        withTallRows={true}
        withFocusableRows
        tableDescription="Company Contacts List"
        onRowClick={handleRowClick}
        skin={skin}
      />
      <PaginationUI
        activePage={pagination.page}
        totalItems={pagination.totalItems}
        rangePerPage={pagination.pageLimit}
        onChange={pagination.goToPage}
      />
    </div>
  )
}
