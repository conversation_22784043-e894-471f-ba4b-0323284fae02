import cx from 'classnames'
import React, { useEffect, useState } from 'react'

import {
  ButtonUI,
  Checkbox,
  CheckboxContainer,
  EditToggleButton,
  InputActionsUI,
  InputUI,
  OptionEditorUI,
  OptionRow,
  TextUI,
} from './OptionEditor.css'

import { useOptionEditor } from './useOptionEditor'
import Button from 'hsds/components/button'

interface OptionEditorProps {
  title: string
  hasCheckbox?: boolean
  checkboxLabel?: string
  optionLabel?: string
  initialData?: string[]
  onChange?: (values: string[]) => void
}

const OptionEditor: React.FC<OptionEditorProps> = ({
  title,
  hasCheckbox = false,
  checkboxLabel,
  optionLabel,
  initialData = [],
  onChange,
}) => {
  const [isChecked, setIsChecked] = useState(false)
  const {
    options,
    editValues,
    setEditValues,
    handleAddOption,
    handleApply,
    handleCancel,
    handleEdit,
    isAnyRowEditing,
    isValueDuplicate,
  } = useOptionEditor({ optionLabel, initialData })

  useEffect(() => {
    const values = options.map(opt => opt.value)
    onChange?.(values)
  }, [options])

  const handleInputChange = (index: number, value: string) => {
    setEditValues(prev => ({ ...prev, [index]: value }))
  }

  return (
    <OptionEditorUI>
      <TextUI size="13">{title}</TextUI>
      {options.map((option, index) => (
        <OptionRow
          key={`title-${index}`}
          className={`is-option ${cx({ 'is-editing': option.isEditing })}`}
        >
          {option.isEditing ? (
            <InputUI
              action={
                <InputActionsUI>
                  <Button
                    size="sm"
                    color="blue"
                    onClick={() => handleApply(index)}
                    disabled={isValueDuplicate(index, editValues[index] || '')}
                  >
                    Apply
                  </Button>
                  <Button
                    outlined
                    size="sm"
                    color="grey"
                    onClick={() => handleCancel(index)}
                  >
                    Cancel
                  </Button>
                </InputActionsUI>
              }
              value={editValues[index] || ''}
              onChange={(value: string) => handleInputChange(index, value)}
              size="md"
              autoFocus
              type="text"
            />
          ) : (
            <>
              <EditToggleButton
                type="button"
                onClick={() => handleEdit(index)}
                disabled={isAnyRowEditing && !option.isEditing}
              >
                {option.value}
              </EditToggleButton>
              {/* {option.label && <Label>{option.label}</Label>} */}
            </>
          )}
        </OptionRow>
      ))}
      <OptionRow>
        <ButtonUI
          size="sm"
          color="blue"
          outlined
          onClick={handleAddOption}
          disabled={isAnyRowEditing}
        >
          Add
        </ButtonUI>
        {hasCheckbox && checkboxLabel && (
          <CheckboxContainer>
            <Checkbox
              type="checkbox"
              checked={isChecked}
              onChange={e => setIsChecked(e.target.checked)}
            />
            {checkboxLabel}
          </CheckboxContainer>
        )}
      </OptionRow>
    </OptionEditorUI>
  )
}

export { OptionEditor }
