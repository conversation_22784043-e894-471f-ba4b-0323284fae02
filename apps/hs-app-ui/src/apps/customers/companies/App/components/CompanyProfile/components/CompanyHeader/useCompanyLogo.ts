import { ChangeEvent, useState } from 'react'

import { trpc } from 'shared/utils/trpc'

import { useNoty } from 'shared/hooks'

interface UseCompanyLogoProps {
  organizationId: number
  initialLogoUrl?: string
}

export const useCompanyLogo = ({
  organizationId,
  initialLogoUrl,
}: UseCompanyLogoProps) => {
  const [previewImage, setPreviewImage] = useState<string | null>(
    initialLogoUrl || null
  )
  const { showSuccessNoty, showErrorNoty } = useNoty()
  const utils = trpc.useUtils()

  const { mutate: saveLogo } = trpc.companies.saveOrganizationLogo.useMutation({
    onSuccess: () => {
      showSuccessNoty('Logo updated successfully')
      void Promise.all([
        utils.companies.getOrganization.invalidate({
          id: organizationId,
          enriched: true,
        }),
        utils.companies.getOrganization.invalidate({
          id: organizationId,
          enriched: false,
        }),
      ])
    },
    onError: error => {
      showErrorNoty(error.message)
    },
  })

  const handleImageChange = (event: ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (file) {
      if (file.size > 5 * 1024 * 1024) {
        showErrorNoty('Logo file must not be larger than 5MB')
        return
      }

      const reader = new FileReader()
      reader.onloadend = () => {
        const base64String = reader.result as string
        setPreviewImage(base64String)
        // tRPC can't directly send File objects
        saveLogo({ organizationId, logo: base64String })
      }
      reader.readAsDataURL(file)
    }
  }

  return {
    previewImage,
    handleImageChange,
  }
}
