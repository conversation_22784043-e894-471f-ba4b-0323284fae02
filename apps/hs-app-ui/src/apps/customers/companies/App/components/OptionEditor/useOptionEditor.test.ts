import { act, renderHook } from '@testing-library/react-hooks'

import { useOptionEditor } from './useOptionEditor'

describe('useOptionEditor', () => {
  it('should initialize with empty options and editValues', () => {
    const { result } = renderHook(() => useOptionEditor({}))

    expect(result.current.options).toEqual([])
    expect(result.current.editValues).toEqual({})
  })

  it('should initialize with provided string values', () => {
    const initialData = ['one', 'two']
    const { result } = renderHook(() => useOptionEditor({ initialData }))

    expect(result.current.options).toEqual([
      { value: 'one', label: undefined, isEditing: false },
      { value: 'two', label: undefined, isEditing: false },
    ])
  })

  describe('handleAddOption', () => {
    it('should add a new empty option in edit mode', () => {
      const { result } = renderHook(() => useOptionEditor({}))

      act(() => {
        result.current.handleAddOption()
      })

      expect(result.current.options).toHaveLength(1)
      expect(result.current.options[0]).toEqual({
        value: '',
        label: undefined,
        isEditing: true,
      })
      expect(result.current.editValues).toEqual({ 0: '' })
    })

    it('should add option with label if its the first option', () => {
      const { result } = renderHook(() =>
        useOptionEditor({ optionLabel: 'Primary' })
      )

      act(() => {
        result.current.handleAddOption()
      })

      expect(result.current.options[0].label).toBe('Primary')
    })
  })

  describe('handleEdit', () => {
    it('should set option to edit mode and initialize edit value with current value', () => {
      const initialData = ['test value']
      const { result } = renderHook(() => useOptionEditor({ initialData }))

      // Edit the option
      act(() => {
        result.current.handleEdit(0)
      })

      expect(result.current.options[0].isEditing).toBe(true)
      expect(result.current.editValues[0]).toBe('test value')
    })

    it('should not allow editing another row when one is already being edited', () => {
      const initialData = ['one', 'two']
      const { result } = renderHook(() => useOptionEditor({ initialData }))

      // Edit first option
      act(() => {
        result.current.handleEdit(0)
      })

      expect(result.current.options[0].isEditing).toBe(true)

      // Try to edit second option
      act(() => {
        result.current.handleEdit(1)
      })

      // Second option should not be in edit mode
      expect(result.current.options[1].isEditing).toBe(false)
    })
  })

  describe('handleApply', () => {
    it('should save the edited value and exit edit mode', () => {
      const { result } = renderHook(() => useOptionEditor({}))

      // Add an option
      act(() => {
        result.current.handleAddOption()
      })

      // Set edit value
      act(() => {
        result.current.setEditValues({ 0: 'test value' })
      })

      // Apply the edit
      act(() => {
        result.current.handleApply(0)
      })

      expect(result.current.options[0]).toEqual({
        value: 'test value',
        label: undefined,
        isEditing: false,
      })
      expect(result.current.editValues).toEqual({})
    })

    it('should remove a row if the value is empty when applying', () => {
      const { result } = renderHook(() => useOptionEditor({}))

      act(() => {
        result.current.handleAddOption()
      })

      act(() => {
        result.current.setEditValues({ 0: '' })
      })

      act(() => {
        result.current.handleApply(0)
      })

      expect(result.current.options).toHaveLength(0)
      expect(result.current.editValues).toEqual({})
    })
  })

  describe('handleCancel', () => {
    it('should remove option if value is empty', () => {
      const { result } = renderHook(() => useOptionEditor({}))

      // Add an option
      act(() => {
        result.current.handleAddOption()
      })

      // Cancel the edit
      act(() => {
        result.current.handleCancel(0)
      })

      expect(result.current.options).toHaveLength(0)
      expect(result.current.editValues).toEqual({})
    })

    it('should revert to previous value if option had value', () => {
      const { result } = renderHook(() => useOptionEditor({}))

      // Add an option with a value
      act(() => {
        result.current.handleAddOption()
      })

      // Set the initial value
      act(() => {
        result.current.setEditValues({ 0: 'initial value' })
      })

      act(() => {
        result.current.handleApply(0)
      })

      // Verify initial state
      expect(result.current.options[0]).toEqual({
        value: 'initial value',
        label: undefined,
        isEditing: false,
      })

      // Start editing
      act(() => {
        result.current.handleEdit(0)
      })

      act(() => {
        result.current.setEditValues({ 0: 'new value' })
      })

      // Cancel edit
      act(() => {
        result.current.handleCancel(0)
      })

      // Verify final state
      expect(result.current.options).toHaveLength(1)
      expect(result.current.options[0]).toEqual({
        value: 'initial value',
        label: undefined,
        isEditing: false,
      })
      expect(result.current.editValues).toEqual({})
    })
  })

  describe('isValueDuplicate', () => {
    it('should detect duplicate values case-insensitively', () => {
      const initialData = ['one', 'Two']
      const { result } = renderHook(() => useOptionEditor({ initialData }))

      // Check for exact match
      expect(result.current.isValueDuplicate(0, 'one')).toBe(false)
      expect(result.current.isValueDuplicate(1, 'one')).toBe(true)
      expect(result.current.isValueDuplicate(0, 'One')).toBe(false)
      expect(result.current.isValueDuplicate(1, 'One')).toBe(true)
    })
  })
})
