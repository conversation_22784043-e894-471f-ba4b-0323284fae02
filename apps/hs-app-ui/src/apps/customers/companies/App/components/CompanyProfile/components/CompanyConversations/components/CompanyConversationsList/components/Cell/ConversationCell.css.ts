import styled from 'styled-components'

import { getColor } from 'hsds/utils/color'

export const ConversationCellUI = styled.div`
  gap: 4px;
  position: relative;
  display: flex;
  flex-direction: column;
  justify-content: center;
  height: 100%;
  width: 100%;
  overflow: hidden;
  white-space: nowrap;
  --fader-color: white;

  &:hover .SubjectUI {
    text-decoration: underline;
  }

  &::after {
    position: absolute;
    content: '';
    width: 18px;
    height: 100%;
    right: 0;
    top: 0;
    box-shadow: inset -7px 0px 7px var(--fader-color);
  }

  tr.c-Table__Row.row-closed &::after {
    --fader-color: ${getColor('charcoal.100')};
  }

  tr.c-Table__Row:focus &::after {
    --fader-color: ${getColor('cobalt.100')};
  }

  tr.c-Table__Row:hover &::after,
  tr.c-Table__Row.row-closed:hover &::after {
    --fader-color: ${getColor('cobalt.100')};
  }

  tr.is-row-selected:hover &::after {
    --fader-color: ${getColor('cobalt.200')};
  }
`

export const SubjectTagsWrapperUI = styled.div`
  display: flex;
  flex-wrap: nowrap;
  flex-direction: column;
  gap: 4px;
  align-items: flex-start;
  width: 100%;
`

export const TagListUI = styled.div`
  display: flex;
  gap: 4px;
  flex-wrap: nowrap;
`

export const TagListItemUI = styled.li`
  display: inline-flex;
  max-width: 100%;
  padding: 0;
  align-items: center;
  list-style: none;
`

export const SubjectUI = styled.div`
  font-size: 13px;
  font-weight: 700;
  color: ${getColor('text.dark')};

  &.is-inactive {
    font-weight: 400;
  }
`
export const PreviewUI = styled.div`
  font-size: 13px;
  color: #68737d;
  line-height: 1.4;
`
