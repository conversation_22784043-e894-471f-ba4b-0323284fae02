import {
  Assigned<PERSON>ell,
  AttachmentCountCell,
  ConversationCell,
  CustomerCell,
  NumberCell,
  ThreadCountCell,
  WaitingCell,
  getHeaderCell,
  getSkeletonCell,
} from './components/Cell/Cell'

export const tableConfig = (isSkeleton = false) => {
  const columns = [
    {
      title: 'Customer',
      columnKey: ['customers', 'status'],
      renderCell: isSkeleton
        ? () => getSkeletonCell({ withAvatar: true })({ width: '185px' })
        : (props: Record<string, any>) => <CustomerCell {...props} />,
      renderHeaderCell: getHeaderCell(),
      width: '185px',
    },
    {
      title: 'Conversation',
      columnKey: ['subject', 'preview', 'tags', 'status'],
      renderCell: isSkeleton
        ? () => getSkeletonCell()({ width: '48%' })
        : (props: Record<string, any>) => <ConversationCell {...props} />,
      renderHeaderCell: getHeaderCell(),
      width: '48%',
    },
    {
      title: '',
      columnKey: ['attachments'],
      renderCell: isSkeleton
        ? () => getSkeletonCell()({ width: '50px' })
        : (props: Record<string, any>) => <AttachmentCountCell {...props} />,
      renderHeaderCell: getHeaderCell(),
      width: '50px',
    },
    {
      title: '',
      columnKey: ['threads'],
      renderCell: isSkeleton
        ? () => getSkeletonCell()({ width: '50px' })
        : (props: Record<string, any>) => <ThreadCountCell {...props} />,
      renderHeaderCell: getHeaderCell(),
      width: '50px',
    },
    {
      title: 'Assigned To',
      columnKey: ['assignee', 'status'],
      renderCell: isSkeleton
        ? () => getSkeletonCell()({ width: '125px' })
        : (props: Record<string, any>) => <AssignedCell {...props} />,
      renderHeaderCell: getHeaderCell(),
      width: '125px',
    },
    {
      title: 'Number',
      columnKey: ['number', 'status'],
      renderCell: isSkeleton
        ? () => getSkeletonCell()({ width: '78px' })
        : (props: Record<string, any>) => <NumberCell {...props} />,
      renderHeaderCell: getHeaderCell(),
      width: '78px',
    },
    {
      title: 'Waiting',
      columnKey: ['customerWaitingSince', 'status'],
      renderCell: isSkeleton
        ? () => getSkeletonCell()({ width: '110px' })
        : (props: Record<string, any>) => <WaitingCell {...props} />,
      renderHeaderCell: getHeaderCell(),
      width: '110px',
    },
  ]

  if (isSkeleton) {
    return {
      columns,
      data: getSkeletonData(),
    }
  }

  return { columns }
}

export const getSkeletonData = () => [
  {
    id: 'skeleton-1',
    firstName: '70%',
    customerEmails: '50%',
    conversationCount: '100%',
    lastSeenAt: '100%',
  },
  {
    id: 'skeleton-2',
    firstName: '80%',
    customerEmails: '50%',
    conversationCount: '50%',
    lastSeenAt: '100%',
  },
  {
    id: 'skeleton-3',
    firstName: '50%',
    customerEmails: '50%',
    conversationCount: '50%',
    lastSeenAt: '100%',
  },
  {
    id: 'skeleton-4',
    firstName: '70%',
    customerEmails: '50%',
    conversationCount: '50%',
    lastSeenAt: '100%',
  },
]
