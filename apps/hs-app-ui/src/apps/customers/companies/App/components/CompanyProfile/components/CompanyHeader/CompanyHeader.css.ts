import styled from 'styled-components'

import { getColor } from 'hsds/utils/color'
import { focusRing } from 'hsds/utils/mixins'
import { COMPANY_DEFAULT_BRAND_COLOR_NAME } from 'shared/utils/companies/color'

import Avatar from 'hsds/components/avatar'
import Button from 'hsds/components/button'
import Text from 'hsds/components/text'

interface AvatarUIWrapperUIProps {
  brandColor?: string
  defaultBrandColorName?: string
  isSquare?: boolean
}

const avatarColorClasses = COMPANY_DEFAULT_BRAND_COLOR_NAME.map(
  color => `
    &.${color} {
      --hsds-avatar-background-color: ${getColor(`pastel.light.${color}`)};
      color: ${getColor(`pastel.dark.${color}`)};
    }
  `
).join('\n')

export const CreateCompanyHeaderUI = styled.div`
  width: 100%;
  background-color: ${getColor('clay.200')};
  border-radius: 5px 5px 0 0;
  padding: 50px 100px 20px;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: flex-start;
  gap: 30px;

  .c-AvatarListWrapper {
    margin-left: auto;
  }
`

export const InfoWrapperUI = styled.div`
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: flex-start;
  gap: 8px;
`

export const AvatarUIWrapperUI = styled.div<AvatarUIWrapperUIProps>`
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  position: relative;
  flex-shrink: 0;

  .c-Avatar__imageMainWrapper {
    /* if the image is not a square, we fill the gap with a white bg */
    background: ${({ brandColor, defaultBrandColorName, isSquare }) =>
      isSquare
        ? brandColor || getColor(`pastel.light.${defaultBrandColorName}`)
        : 'white'};
  }
`

export const AvatarUI = styled(Avatar)`
  ${avatarColorClasses}

  .c-Icon {
    color: var(--company-fallback-icon-color, currentColor);
  }

  .c-Avatar__image {
    background-size: contain;
    background-repeat: no-repeat;
  }
`

export const CustomerAvatarUI = styled(Avatar)`
  cursor: pointer;
  ${focusRing};
  --hsds-focus-ring-radius: 50%;
`

export const LocationTextUI = styled(Text)`
  font-size: 14px;
  font-weight: 400;
  line-height: 18px;
  -webkit-font-smoothing: antialiased;
  color: ${getColor('charcoal.700')};
`

export const WebsiteLinkUI = styled(Button)`
  && {
    border: none;
    color: ${getColor('charcoal.700')};
    font-size: 14px;
    font-weight: 500;
    height: auto;
    line-height: 18px;
    text-align: left;
    text-transform: lowercase;
    -webkit-font-smoothing: antialiased;

    .c-Truncate {
      padding-right: 5px;
    }

    &.is-color-blue {
      color: ${getColor('cobalt.600')};
      font-weight: 500;
      max-width: 100%;
    }

    &:focus,
    &:hover {
      text-decoration: none;

      .c-Truncate {
        text-decoration: underline;
      }
    }

    &:focus-visible:before,
    &:focus:before {
      display: none; // Hide pseudo-element with box-shadow that simulates outline
    }
  }
`

export const CompanyNameTextUI = styled(Text)`
  width: 100%;
  margin-bottom: 0;
  -webkit-font-smoothing: antialiased;
  display: inline;
  font-weight: 500;
  color: ${getColor('charcoal.1200')};
  line-height: normal;

  &&& {
    font-size: 28px;
  }
`

export const CircleSeparatorUI = styled('span')`
  margin: 0 8px;
  color: ${getColor('charcoal.700')};
  font-size: 8px;
  vertical-align: middle;
  display: inline-block;
`
