import { SkeletonTableUI } from './Loading.css'

import { tableConfig } from '../../table.config'
import Animate from 'hsds/components/animate'

const { columns, data } = tableConfig(true)

export const Loading = () => (
  <Animate>
    <SkeletonTableUI
      columns={columns as any}
      data={data as any}
      tableDescription="Loading Conversations"
      isLoading={true}
      withTallRows
      animateRows={false}
      className=""
      columnChooserResetLabel="Reset"
      expanderText=""
      withSelectableRows={false}
      withFocusableRows={false}
      headerContent={null}
      maxRowsToDisplay={10}
      tableClassName=""
    />
  </Animate>
)
