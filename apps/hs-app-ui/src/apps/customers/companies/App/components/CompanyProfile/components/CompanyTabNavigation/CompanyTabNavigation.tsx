import { useCallback } from 'react'
import { useParams } from 'react-router-dom'

import { formatNumber } from 'hsds/utils/number'

import {
  COMPANIES_BASE,
  CompanyTab,
} from '../../../../../constants/routes.constants'

import {
  BadgeCountUI,
  CompanyTabNavigationUI,
} from './CompanyTabNavigation.css'

import TabBar from 'hsds/components/tab-bar'

interface TabInfo {
  label: string
  hasBadge?: boolean
}

type TabsType = {
  [key in CompanyTab]: TabInfo
}

const TABS: TabsType = {
  info: { label: 'Profile' },
  conversations: { label: 'Conversations', hasBadge: true },
  contacts: { label: 'Contacts', hasBadge: true },
}

const CompanyTabNavigation = ({
  conversationsCount,
  contactsCount,
}: {
  conversationsCount: number
  contactsCount: number
}) => {
  const { id, activeTab = 'info' } = useParams<{
    id: string
    activeTab?: CompanyTab
  }>()

  const isTabActive = useCallback(
    (tabName: CompanyTab) => activeTab === tabName,
    [activeTab]
  )

  const getBadgeCount = (tabName: CompanyTab): string => {
    if (tabName === 'conversations') {
      return typeof conversationsCount === 'number'
        ? formatNumber(conversationsCount) || '0'
        : '0'
    }

    if (tabName === 'contacts') {
      return typeof contactsCount === 'number'
        ? formatNumber(contactsCount) || '0'
        : '0'
    }
    return '0'
  }

  const renderTabContent = (tabName: CompanyTab) => {
    const tabInfo = TABS[tabName]

    if (!tabInfo.hasBadge) return tabInfo.label

    return (
      <>
        <span>{tabInfo.label}</span>
        <BadgeCountUI variant="white">{getBadgeCount(tabName)}</BadgeCountUI>
      </>
    )
  }

  return (
    <CompanyTabNavigationUI data-testid="CustomersNavigation">
      <TabBar variant="border-bottom" withReactRouter>
        {(Object.keys(TABS) as CompanyTab[]).map(tabName => (
          <TabBar.Item
            key={tabName}
            data-testid={`CustomersNavigation-${tabName}`}
            to={`${COMPANIES_BASE}/${id}/${tabName}`}
            isActive={() => isTabActive(tabName)}
            aria-current={isTabActive(tabName) ? 'page' : undefined}
          >
            {renderTabContent(tabName)}
          </TabBar.Item>
        ))}
      </TabBar>
    </CompanyTabNavigationUI>
  )
}

export { CompanyTabNavigation }
