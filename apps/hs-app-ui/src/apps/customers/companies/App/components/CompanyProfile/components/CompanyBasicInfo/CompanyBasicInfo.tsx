import { useState } from 'react'

import type { SaveOrganizationRequest } from '@helpscout/organization-service-typescript-fetch-client'

import { useSaveCompany } from '../../../../../hooks/useSaveCompany'

import {
  ButtonUI,
  CompanyBasicInfoUI,
  CompanyBasicInfoWrapperUI,
  InputUI,
  NoteUI,
} from './CompanyBasicInfo.css'

import { Company } from '../../../../../types/companies'
import { OptionEditor } from '../../../OptionEditor/OptionEditor'

interface CompanyBasicInfoProps {
  company: Company
}

const CompanyBasicInfo = ({ company }: CompanyBasicInfoProps) => {
  const [name, setName] = useState<string>(company.name || '')
  const [note, setNote] = useState(company.note || '')
  const [description, setDescription] = useState(company.description || '')
  const [phones, setPhones] = useState<string[]>(company.phones ?? [])

  const [errors, setErrors] = useState({
    name: '',
  })

  const saveCompany = useSaveCompany()

  const validateName = (value: string) => {
    if (!value.trim()) {
      return "Company Name can't be empty"
    }
    return ''
  }

  const handleCompanyNameChange = (value: string) => {
    setName(value)
    setErrors({ ...errors, name: validateName(value) })
  }

  const handleDescriptionChange = (value: string) => {
    setDescription(value)
  }

  const handleNoteChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
  ) => {
    setNote(e.target.value)
  }

  const handlePhonesChange = (newPhones: string[]) => {
    setPhones(newPhones)
  }

  const handleSave = async () => {
    const nameError = validateName(name)

    if (nameError) {
      setErrors({
        name: nameError,
      })
      return
    }

    const organization: SaveOrganizationRequest = {
      name,
      note: note || undefined,
      description: description || undefined,
      website: company.website || undefined,
      phones: phones.length ? phones : undefined,
    }

    await saveCompany.saveCompany({
      organizationId: company.id,
      organization,
    })
  }

  return (
    <CompanyBasicInfoUI>
      <CompanyBasicInfoWrapperUI>
        <InputUI
          type="text"
          value={name}
          label="Company Name"
          errorMessage={errors.name}
          state={errors.name ? 'error' : undefined}
          onChange={handleCompanyNameChange}
        />
        <InputUI type="text" value={company.domain} label="Domain" disabled />
        <OptionEditor
          title="Phone"
          initialData={phones}
          onChange={handlePhonesChange}
        />
        <InputUI
          type="text"
          value={description}
          label="Description"
          multiline={4}
          onChange={handleDescriptionChange}
        />
        <NoteUI
          label="Notes"
          isActionButtonsVisible={false}
          value={note}
          onChange={handleNoteChange}
        />
        <ButtonUI
          size="lg"
          color="blue"
          onClick={handleSave}
          data-testid="save-company-info"
          disabled={!!errors.name}
        >
          Update
        </ButtonUI>
      </CompanyBasicInfoWrapperUI>
    </CompanyBasicInfoUI>
  )
}

export { CompanyBasicInfo }
