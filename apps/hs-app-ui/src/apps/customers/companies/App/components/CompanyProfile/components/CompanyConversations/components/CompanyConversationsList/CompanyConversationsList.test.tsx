import { fireEvent, render, screen } from '@testing-library/react'

import { CompanyConversationsList } from './CompanyConversationsList'

const mockConversationsData = {
  items: [
    {
      id: 1,
      type: 'email',
      folderId: 456,
      state: 'open',
      number: 123,
      threads: 2,
      status: 'active',
      subject: 'Test Conversation',
      mailboxId: 789,
      tags: [
        {
          id: 1,
          tag: 'Important',
          color: '#ff0000',
          colorName: 'red',
          styles: {
            dark: {
              background: { fill: '#000' },
              text: { fill: '#fff' },
            },
            default: {
              background: { fill: '#fff' },
              text: { fill: '#000' },
            },
          },
        },
      ],
      customFields: [],
      following: false,
      primaryCustomer: { id: 1, entryId: 1 },
      mostRecentCustomerId: 1,
      mostRecentCustomer: { id: 1, entryId: 1 },
      customers: [
        {
          emails: [{ id: 1, value: '<EMAIL>', default: true }],
          phones: [],
          id: 1,
          first: '<PERSON>',
          last: 'Doe',
          photoUrl: '',
        },
      ],
      assignee: {
        id: 1,
        first: 'Agent',
        last: 'Smith',
        type: 'user',
        email: '<EMAIL>',
      },
      aliases: [],
      source: { type: 'email', via: 'web' },
      attachments: true,
      preview: 'Preview text',
      userUpdatedAt: '2024-03-20T10:00:00Z',
      createdBy: {
        id: 1,
        type: 'user',
        email: '<EMAIL>',
        first: 'Creator',
        last: 'User',
        photoUrl: '',
        alias: 'creator',
      },
      createdAt: '2024-03-20T09:00:00Z',
      closedAt: '2024-03-20T11:00:00Z',
      closedBy: {
        id: 2,
        email: '<EMAIL>',
        first: 'Closer',
        last: 'User',
        alias: 'closer',
        photoUrl: '',
      },
      customerWaitingSince: {
        friendly: '2 hours ago',
        time: '2024-03-20T10:00:00Z',
      },
      deleted: false,
      hasMultipleCustomers: false,
      defaultViewId: '456',
    },
  ],
  pages: 1,
  count: 1,
}

const mockPagination = {
  page: 1,
  pageLimit: 10,
  totalItems: 1,
  goToPage: jest.fn(),
}

describe('CompanyConversationsList', () => {
  beforeEach(() => {
    jest.clearAllMocks()
  })

  it('renders loading state', () => {
    render(
      <CompanyConversationsList isLoading={true} pagination={mockPagination} />
    )
    expect(screen.getByLabelText('Loading Conversations')).toBeInTheDocument()
  })

  it('renders empty state when no conversations', () => {
    render(
      <CompanyConversationsList isLoading={false} pagination={mockPagination} />
    )
    expect(screen.getByText('No conversations found')).toBeInTheDocument()
  })

  it('renders conversations table with data', () => {
    render(
      <CompanyConversationsList
        conversationsData={mockConversationsData}
        pagination={mockPagination}
        isLoading={false}
      />
    )

    expect(screen.getByRole('table')).toBeInTheDocument()
    expect(screen.getByText('Test Conversation')).toBeInTheDocument()
  })

  it('handles row click correctly', () => {
    const mockLocation = { href: '' }
    Object.defineProperty(window, 'location', {
      value: mockLocation,
      writable: true,
    })

    render(
      <CompanyConversationsList
        conversationsData={mockConversationsData}
        pagination={mockPagination}
        isLoading={false}
      />
    )

    const row = screen.getByText('Test Conversation')
    fireEvent.click(row)

    expect(window.location.href).toBe('/conversation/1/123?viewId=456')

    Object.defineProperty(window, 'location', {
      value: undefined,
      writable: true,
    })
  })
})
