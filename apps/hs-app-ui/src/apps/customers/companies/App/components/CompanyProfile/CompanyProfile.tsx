import { useParams } from 'react-router-dom'

import { useCompany } from '../../../hooks/useCompany'
import { useConversations } from '../../../hooks/useConversations'
import { useCustomers } from '../../../hooks/useCustomers'

import { CompanyTab } from '../../../constants/routes.constants'

import { CompanyProfileWrapperUI } from './CompanyProfile.css'

import {
  CompanyBasicInfo,
  CompanyContacts,
  CompanyConversations,
  CompanyHeader,
  CompanyTabNavigation,
} from './components'

function CompanyProfile({ companyId }: { companyId: string }) {
  const { activeTab = 'info' } = useParams<{ activeTab?: CompanyTab }>()
  const { data: company, error, isLoading } = useCompany(companyId)
  const {
    data: customerData,
    pagination,
    isLoading: isCustomersLoading,
  } = useCustomers({
    organizationId: company?.id,
    initialPageLimit: 10,
  })

  const {
    data: conversationsData,
    pagination: conversationsPagination,
    isLoading: isConversationsLoading,
  } = useConversations({
    companyId: company?.id,
    initialPageLimit: 10,
  })

  if (error) return <div>Error: {error.message}</div>
  if (!company && !isLoading) return <div>Company not found</div>

  const renderTabContent = () => {
    if (!company) return null

    switch (activeTab) {
      case 'conversations':
        return (
          <CompanyConversations
            conversationsData={conversationsData}
            pagination={conversationsPagination}
            isLoading={isConversationsLoading}
          />
        )
      case 'contacts':
        return (
          <CompanyContacts
            customerData={customerData}
            pagination={pagination}
            isLoading={isCustomersLoading}
          />
        )
      default:
        return <CompanyBasicInfo company={company} />
    }
  }

  if (!company) return null

  return (
    <CompanyProfileWrapperUI>
      <CompanyHeader
        companyName={company.name}
        organizationId={company.id}
        website={company.website}
        address={company.location}
        logoUrl={company.logoUrl}
        customerData={customerData?.items}
        contactsCount={company?.customerCount}
        brandColor={company?.brandColor}
        defaultBrandColorName={company?.defaultBrandColorName}
      />
      <CompanyTabNavigation
        conversationsCount={company?.conversationCount}
        contactsCount={company?.customerCount}
      />
      {renderTabContent()}
    </CompanyProfileWrapperUI>
  )
}

export { CompanyProfile }
