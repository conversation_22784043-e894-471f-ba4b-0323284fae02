import styled from 'styled-components'

import { getColor } from 'hsds/utils/color'

import BadgeCount from 'hsds/components/badge-count'

export const CompanyTabNavigationUI = styled.div`
  padding-top: 0;
  padding-bottom: 0;

  .c-TabBar {
    padding-right: var(--hsds-page-card-padding-side);
    padding-left: var(--hsds-page-card-padding-side);
    border-bottom: none;
    border-radius: 0 0 5px 5px;
  }

  .c-TabBar,
  .c-TabBarList {
    background-color: ${getColor('clay.200')};
  }
`

export const BadgeCountUI = styled(BadgeCount)`
  display: inline-flex;
  margin-left: 4px;
  font-size: 11px;
  font-weight: 400;
  line-height: 16px;
  height: 22px;
  flex-wrap: nowrap;
  align-items: center;
  -webkit-font-smoothing: antialiased;
`
