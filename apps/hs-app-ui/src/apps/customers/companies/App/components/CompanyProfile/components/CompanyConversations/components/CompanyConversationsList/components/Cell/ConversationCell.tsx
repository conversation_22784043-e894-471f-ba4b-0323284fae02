import cx from 'classnames'
import type { FC } from 'react'

import { sanitizePreview } from 'shared/utils/StringUtils'

import {
  ConversationCellUI,
  PreviewUI,
  SubjectTagsWrapperUI,
  SubjectUI,
  TagListItemUI,
  TagListUI,
} from './ConversationCell.css'

import Tag from 'hsds/components/tag'

export const PLACEHOLDER_NO_SUBJECT = '(no subject)'

type ApiTagColor =
  | 'grey'
  | 'blue'
  | 'green'
  | 'purple'
  | 'red'
  | 'yellow'
  | 'orange'
  | 'pink'
  | 'teal'
  | 'none'

type TagColor = Exclude<ApiTagColor, 'none'>

type Tag = {
  colorName?: ApiTagColor
  tag: string
  id: string
  color: string
}

type ConversationCellProps = {
  subject?: string
  preview?: string
  tags?: Tag[]
  status?: string
}

function renderTags(tags: Tag[] = []) {
  if (!tags.length) {
    return null
  }

  const content = tags.map(({ colorName, tag, id }) => {
    let useableColorValue: TagColor = 'grey'

    if (colorName && colorName !== 'none') {
      useableColorValue = colorName as TagColor
    }

    return (
      <TagListItemUI key={`${tag}_${id}`}>
        <Tag filled size="sm" color={useableColorValue} key={`${tag}_${id}`}>
          {tag}
        </Tag>
      </TagListItemUI>
    )
  })

  return (
    <TagListUI data-testid="Organization.ConversationListTable.Cell.Tags">
      {content}
    </TagListUI>
  )
}

export function ariaLabelForConversation({ subject }: { subject?: string }) {
  return `Subject: ${subject || PLACEHOLDER_NO_SUBJECT}`
}

export const ConversationCell: FC<ConversationCellProps> = ({
  subject,
  preview,
  tags = [],
  status,
}) => {
  // conversations with empty subject text display a placeholder
  const subjectText = subject || PLACEHOLDER_NO_SUBJECT

  return (
    <ConversationCellUI>
      <SubjectTagsWrapperUI>
        {renderTags(tags)}
        <SubjectUI
          className={cx({ 'is-inactive': status !== 'active' })}
          data-testid="ConversationListTable.ConversationCell.Subject"
        >
          {subjectText}
        </SubjectUI>
      </SubjectTagsWrapperUI>
      <PreviewUI data-testid="ConversationListTable.ConversationCell.Preview">
        {preview ? sanitizePreview(preview, 300) : ''}
      </PreviewUI>
    </ConversationCellUI>
  )
}
