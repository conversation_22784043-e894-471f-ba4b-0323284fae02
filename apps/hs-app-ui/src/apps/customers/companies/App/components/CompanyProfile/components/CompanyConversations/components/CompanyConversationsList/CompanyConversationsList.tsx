import { EmptyStateTextUI, PaginationUI } from './CompanyConversationsList.css'
import { CompanyConversationsTableUI } from './CompanyConversationsList.css'

import {
  CONVO_STATUS_CLASSNAMES,
  skin,
} from './CompanyConversationList.constants'
import { Loading } from './components/Loading/Loading'
import { tableConfig } from './table.config'
import { ConversationItemType } from './types'

// eslint-disable-next-line @typescript-eslint/no-unsafe-assignment, @typescript-eslint/no-explicit-any
const CompanyConversationsTable: any = CompanyConversationsTableUI

interface CompanyConversationsListProps {
  conversationsData?: {
    items: ConversationItemType[]
    pages: number
    count: number
  }
  pagination: {
    page: number
    pageLimit: number
    totalItems: number
    goToPage: (page: number) => void
  }
  isLoading: boolean
}

export const CompanyConversationsList = ({
  conversationsData,
  pagination,
  isLoading,
}: CompanyConversationsListProps) => {
  if (isLoading) {
    return <Loading />
  }

  if (!isLoading && !conversationsData?.items?.length) {
    return <EmptyStateTextUI>No conversations found</EmptyStateTextUI>
  }

  function getRowClassName(row: ConversationItemType) {
    return {
      [CONVO_STATUS_CLASSNAMES[row.status]]: true,
    }
  }

  const handleRowClick = (
    _: any,
    row: { id: number; number: string; folderId: string }
  ) => {
    const path = `/conversation/${row?.id}/${row?.number}?viewId=${row?.folderId}`

    window.location.href = path
  }

  return (
    <div>
      <CompanyConversationsTable
        columnChooserResetLabel="Reset"
        isLoading={isLoading}
        columns={tableConfig().columns}
        data={conversationsData?.items || []}
        withTallRows={true}
        rowClassName={getRowClassName}
        tableDescription="Company Conversations List"
        onRowClick={handleRowClick}
        skin={skin}
      />
      <PaginationUI
        activePage={pagination.page}
        totalItems={pagination.totalItems}
        rangePerPage={pagination.pageLimit}
        onChange={pagination.goToPage}
      />
    </div>
  )
}
