import { useHistory } from 'react-router-dom'

import { render, screen, waitFor } from '@testing-library/react'
import userEvent from '@testing-library/user-event'

import { useCustomers } from '../../../../../../../hooks/useCustomers'

import { CompanyContactsList } from './CompanyContactsList'

jest.mock('../../../../../../../hooks/useCustomers')

jest.mock('react-router-dom', () => ({
  useHistory: jest.fn(),
}))

const mockUseCustomers = useCustomers as jest.MockedFunction<
  typeof useCustomers
>
const mockUseHistory = useHistory as jest.MockedFunction<typeof useHistory>

describe('CompanyContactsList', () => {
  const mockPagination = {
    page: 1,
    pageLimit: 20,
    totalItems: 50,
    goToPage: jest.fn(),
  }

  const mockCustomerData = {
    items: [
      {
        id: 1,
        firstName: 'John',
        lastName: 'Doe',
        emails: [{ id: 1, value: '<EMAIL>', default: true }],
      },
      {
        id: 2,
        firstName: 'Jane',
        lastName: 'Smith',
        emails: [{ id: 2, value: '<EMAIL>', default: true }],
      },
    ],
    count: 50,
    pages: 3,
  }

  const mockHistoryPush = jest.fn()

  beforeEach(() => {
    jest.clearAllMocks()

    // Default mock implementations
    mockUseHistory.mockReturnValue({
      push: mockHistoryPush,
    } as any)
  })

  it('displays loading state when data is loading', () => {
    mockUseCustomers.mockReturnValue({
      isLoading: true,
      pagination: mockPagination,
      data: undefined,
    } as any)

    render(<CompanyContactsList isLoading={true} pagination={mockPagination} />)

    expect(screen.getByLabelText('Loading Contacts')).toBeInTheDocument()
  })

  it('displays empty state when no contacts are found', () => {
    mockUseCustomers.mockReturnValue({
      isLoading: false,
      pagination: mockPagination,
      data: { items: [], count: 0, pages: 0 },
    } as any)

    render(
      <CompanyContactsList
        isLoading={false}
        pagination={mockPagination}
        customerData={{ items: [], count: 0, pages: 0 }}
      />
    )

    expect(screen.getByText('No contacts found')).toBeInTheDocument()
  })

  it('renders the contacts list with data', () => {
    mockUseCustomers.mockReturnValue({
      isLoading: false,
      pagination: mockPagination,
      data: mockCustomerData,
    } as any)

    render(
      <CompanyContactsList
        isLoading={false}
        pagination={mockPagination}
        customerData={mockCustomerData}
      />
    )

    // Check for contact names
    expect(screen.getByText('John Doe')).toBeInTheDocument()
    expect(screen.getByText('Jane Smith')).toBeInTheDocument()

    // Verify table description
    const table = screen.getByRole('table')
    expect(table).toHaveAttribute('aria-label', 'Company Contacts List')
  })

  it('navigates to customer profile when row is clicked', () => {
    mockUseCustomers.mockReturnValue({
      isLoading: false,
      pagination: mockPagination,
      data: mockCustomerData,
    } as any)

    render(
      <CompanyContactsList
        isLoading={false}
        pagination={mockPagination}
        customerData={mockCustomerData}
      />
    )

    // Find and click the first row
    const firstRow = screen.getByText('John Doe').closest('tr')
    userEvent.click(firstRow!)

    // Verify navigation
    expect(mockHistoryPush).toHaveBeenCalledWith('/customers/1')
  })

  it('renders pagination with correct values', () => {
    mockUseCustomers.mockReturnValue({
      isLoading: false,
      pagination: mockPagination,
      data: mockCustomerData,
    } as any)

    render(
      <CompanyContactsList
        isLoading={false}
        pagination={mockPagination}
        customerData={mockCustomerData}
      />
    )

    // Verify pagination is rendered with correct values
    expect(screen.getByRole('navigation')).toBeInTheDocument()
    expect(screen.getByText('50')).toBeInTheDocument() // total items
  })

  it('calls goToPage when pagination is changed', async () => {
    mockUseCustomers.mockReturnValue({
      isLoading: false,
      pagination: mockPagination,
      data: mockCustomerData,
    } as any)

    render(
      <CompanyContactsList
        isLoading={false}
        pagination={mockPagination}
        customerData={mockCustomerData}
      />
    )

    // Find and click the next page button
    const nextButton = screen.getByRole('button', { name: /next|forward|>/i })
    userEvent.click(nextButton)

    // Verify goToPage was called with page 2
    await waitFor(() => {
      expect(mockPagination.goToPage).toHaveBeenCalledWith(2)
    })
  })
})
