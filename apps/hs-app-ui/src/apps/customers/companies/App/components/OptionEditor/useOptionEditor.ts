import { useState } from 'react'

interface Option {
  value: string
  label?: string
  isEditing?: boolean
}

interface UseOptionEditorProps {
  optionLabel?: string
  initialData?: string[]
}

interface UseOptionEditorReturn {
  options: Option[]
  editValues: { [key: number]: string }
  setEditValues: React.Dispatch<React.SetStateAction<{ [key: number]: string }>>
  handleAddOption: () => void
  handleApply: (index: number) => void
  handleCancel: (index: number) => void
  handleEdit: (index: number) => void
  isAnyRowEditing: boolean
  isValueDuplicate: (index: number, value: string) => boolean
}

export const useOptionEditor = ({
  optionLabel,
  initialData = [],
}: UseOptionEditorProps): UseOptionEditorReturn => {
  const initialOptions: Option[] = initialData.map((num, index) => ({
    value: num.toString(),
    label: index === 0 ? optionLabel : undefined,
    isEditing: false,
  }))

  const [options, setOptions] = useState<Option[]>(initialOptions)
  // Track temporary values while editing
  const [editValues, setEditValues] = useState<{ [key: number]: string }>({})

  const isAnyRowEditing = options.some(option => option.isEditing)

  /**
   * Checks if a value is already used in another option
   */
  const isValueDuplicate = (index: number, value: string): boolean => {
    const trimmedValue = value.trim()

    return options.some(
      (option, i) =>
        i !== index && option.value.toLowerCase() === trimmedValue.toLowerCase()
    )
  }

  /**
   * Helper function: Remove the temporary edit value for a specific index
   */
  const cleanupEditValue = (index: number) => {
    setEditValues(prev => {
      const newValues = { ...prev }
      delete newValues[index]
      return newValues
    })
  }

  /**
   * Adds a new empty option at the beginning of the list
   * Sets it to edit mode immediately
   */
  const handleAddOption = () => {
    const newOption = {
      value: '',
      label: options.length === 0 ? optionLabel : undefined,
      isEditing: true,
    }

    setOptions([newOption, ...options])
    // Initialize edit value for the new option
    setEditValues({ 0: '' })
  }

  const handleApply = (index: number) => {
    setOptions(prevOptions => {
      const newOptions = [...prevOptions]
      const newValue = editValues[index] || ''

      if (!newValue.trim()) {
        // If the value is empty, remove the option
        newOptions.splice(index, 1)
      } else {
        // Otherwise update the option with the new value
        newOptions[index] = {
          ...newOptions[index],
          value: newValue,
          isEditing: false,
        }
      }
      return newOptions
    })

    cleanupEditValue(index)
  }

  /**
   * Cancels editing and either:
   * - Removes the option if it's empty
   * - Or reverts to the previous value
   */
  const handleCancel = (index: number) => {
    setOptions(prevOptions => {
      const newOptions = [...prevOptions]
      if (!newOptions[index].value) {
        newOptions.splice(index, 1)
      } else {
        newOptions[index] = { ...newOptions[index], isEditing: false }
      }
      return newOptions
    })

    cleanupEditValue(index)
  }

  /**
   * Enters edit mode for an existing option
   * Initializes the edit value with the current option's value
   */
  const handleEdit = (index: number) => {
    if (isAnyRowEditing) {
      return // Don't allow editing if another row is being edited
    }

    setOptions(prevOptions => {
      const newOptions = [...prevOptions]
      newOptions[index] = { ...newOptions[index], isEditing: true }
      // Initialize with current option value
      setEditValues(prev => ({
        ...prev,
        [index]: newOptions[index].value,
      }))
      return newOptions
    })
  }

  return {
    options,
    editValues,
    setEditValues,
    handleAddOption,
    handleApply,
    handleCancel,
    handleEdit,
    isAnyRowEditing,
    isValueDuplicate,
  }
}
