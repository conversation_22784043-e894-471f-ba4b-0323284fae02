import styled from 'styled-components'

import { getColor } from 'hsds/utils/color'

import Avatar from 'hsds/components/avatar'
import Button from 'hsds/components/button'
import EditableTextarea from 'hsds/components/editable-textarea'
import Text from 'hsds/components/text'
import { getToken } from 'hsds/tokens'

export const CreateCompanyHeaderUI = styled.div`
  width: 100%;
  height: 215px;
  background-color: ${getColor('clay.200')};
  padding: 48px 100px;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: flex-start;
  gap: 25px;
`

export const InfoWrapperUI = styled.div`
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: flex-start;
  gap: 8px;
`

export const AvatarUIWrapperUI = styled.div`
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  position: relative;
  flex-shrink: 0;
`

export const AvatarUI = styled(Avatar)`
  .c-Avatar__crop {
    background: #b388ff;
  }
`

export const LocationTextUI = styled(Text)`
  font-size: 14px;
  font-weight: 400;
  line-height: 18px;
  -webkit-font-smoothing: antialiased;
  color: ${getColor('charcoal.700')};
`

export const WebsiteLinkUI = styled(Button)`
  && {
    border: none;
    color: ${getColor('charcoal.700')};
    font-size: 14px;
    font-weight: 500;
    height: auto;
    line-height: 18px;
    text-align: left;
    text-transform: lowercase;
    -webkit-font-smoothing: antialiased;

    .c-Truncate {
      padding-right: 5px;
    }

    &.is-color-blue {
      color: ${getColor('cobalt.600')};
      font-weight: 500;
      max-width: 100%;
    }

    &:focus,
    &:hover {
      text-decoration: none;

      .c-Truncate {
        text-decoration: underline;
      }
    }

    &:focus-visible:before,
    &:focus:before {
      display: none; // Hide pseudo-element with box-shadow that simulates outline
    }
  }
`

export const CompanyNameFieldUI = styled(EditableTextarea)`
  width: 100%;
  margin-bottom: 0;
  -webkit-font-smoothing: antialiased;
  isolation: isolate;
  font-family: 'Aktiv Grotesk', 'Helvetica Neue', Helvetica, Arial, sans-serif;

  .EditableTextarea__ResizableTextarea {
    --hsds-editableTextarea-focus-indicator-bg: ${getToken(
      'color.cobalt.accent'
    )};
  }

  .EditableTextarea__Textarea,
  .EditableTextarea__Mask {
    font-weight: 500;
    font-size: 20px;
    color: ${getColor('charcoal.1200')};
    line-height: 24px;
    min-height: 28px;
    --hsds-token-editableField-color-background: transparent;
  }

  .with-placeholder > textarea::placeholder {
    font-weight: 400;
    font-size: 20px;
    -webkit-font-smoothing: initial;
    color: ${getColor('charcoal.700')};
  }

  .with-placeholder.is-readonly
    .field:not(.is-hidden):not(.EditableTextarea__Textarea) {
    height: 24px;
    span {
      font-weight: 400;
      display: inline;
      font-size: 20px;
      height: 24px;
      -webkit-font-smoothing: initial;
      color: ${getColor('charcoal.700')};
    }
  }

  #editabletextarea {
    overflow: hidden;
  }
`

export const CircleSeparatorUI = styled('span')`
  margin: 0 8px;
  color: ${getColor('charcoal.700')};
  font-size: 8px;
  vertical-align: middle;
  display: inline-block;
`
