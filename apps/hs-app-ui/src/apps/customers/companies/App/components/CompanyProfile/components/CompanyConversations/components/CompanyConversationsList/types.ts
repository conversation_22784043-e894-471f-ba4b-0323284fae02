export type ConversationItemType = {
  id: number
  type: string
  folderId: number
  state: string
  number: number
  threads: number
  status: string
  subject: string
  mailboxId: number
  tags: Array<{
    id: number
    tag: string
    color: string
    colorName: string
    styles: {
      dark: {
        background: { fill: string }
        text: { fill: string }
      }
      default: {
        background: { fill: string }
        text: { fill: string }
      }
    }
  }>
  customFields: Array<{
    id: number
    name: string
    value: string
    text: string
  }>
  following: boolean
  primaryCustomer: {
    id: number
    entryId: number
  }
  mostRecentCustomerId: number
  mostRecentCustomer: {
    id: number
    entryId: number
  }
  customers: Array<{
    emails: Array<{
      id: number
      value: string
      default: boolean
    }>
    phones: any[]
    id: number
    first: string
    last: string
    photoUrl: string
  }>
  assignee: {
    id: number
    first: string
    last: string
    type: string
    email: string
  }
  aliases: any[]
  source: {
    type: string
    via: string
  }
  attachments: boolean
  preview: string
  userUpdatedAt: string
  createdBy: {
    id: number
    type: string
    email: string
    first: string
    last: string
    photoUrl: string
    alias: string
  }
  createdAt: string
  closedAt: string
  closedBy: {
    id: number
    email: string
    first: string
    last: string
    alias: string
    photoUrl: string
  }
  customerWaitingSince: {
    friendly: string
    time: string
  }
  deleted: boolean
  hasMultipleCustomers: boolean
  defaultViewId: string
}
