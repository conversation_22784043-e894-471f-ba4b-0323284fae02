import React, { useCallback, useEffect, useMemo, useRef, useState } from 'react'

import { IconUI, InputWrapperUI, SearchBarUI } from './SearchBar.css'

import Icon from 'hsds/components/icon'
import Input from 'hsds/components/input'
import SearchMedium from 'hsds/icons/search'
import debounce from 'lodash/debounce'

interface SearchBarProps {
  onSearch: (searchTerm: string) => void
  initialSearchTerm?: string
  placeholder?: string
  debounceDelay?: number
}

export const SearchBar: React.FC<SearchBarProps> = ({
  onSearch,
  initialSearchTerm = '',
  placeholder = 'Search',
  debounceDelay = 500,
}) => {
  const [inputValue, setInputValue] = useState(initialSearchTerm)
  const lastSearchTermRef = useRef(initialSearchTerm)

  const triggerSearch = useCallback(
    (term: string) => {
      if (term !== lastSearchTermRef.current) {
        lastSearchTermRef.current = term
        onSearch(term)
      }
    },
    [onSearch]
  )

  const debouncedTriggerSearch = useMemo(
    () => debounce(triggerSearch, debounceDelay),
    [triggerSearch, debounceDelay]
  )

  const handleInputChange = useCallback(
    (value: string) => {
      setInputValue(value)
      debouncedTriggerSearch(value)
    },
    [debouncedTriggerSearch]
  )

  const handleBlur = useCallback(() => {
    debouncedTriggerSearch.flush()
  }, [debouncedTriggerSearch])

  const handleFocus = useCallback(() => {}, [])

  useEffect(() => {
    return () => {
      debouncedTriggerSearch.cancel()
    }
  }, [debouncedTriggerSearch])

  const searchIcon = (
    <IconUI>
      <Icon icon={SearchMedium} size="20" data-cy="SearchBar.SearchIcon" />
    </IconUI>
  )

  return (
    <SearchBarUI data-cy="CompanyList.SearchBar">
      <InputWrapperUI>
        <Input
          aria-label="Search companies"
          data-testid="SearchBar.Input"
          data-cy="SearchBar.Input"
          autoComplete="off"
          placeholder={placeholder}
          name="companyList-search"
          onChange={handleInputChange}
          onBlur={handleBlur}
          onFocus={handleFocus}
          value={inputValue}
          inlineSuffix={searchIcon}
        />
      </InputWrapperUI>
    </SearchBarUI>
  )
}

export default SearchBar
