import { useSelector } from 'react-redux'

import { useAppSidePanel } from './components/AppSidePanel/hooks'
import { CustomerSidebarContext } from './hooks/useCustomerSidebarContext'
import {
  getCustomerData,
  getCustomerSidebarViewMode,
} from 'apps/mailbox/state/customer/reducer'
import { useFeatureFlag, useMoveBeacon } from 'shared/hooks'

import {
  AppPlugins,
  AppSidePanel,
  CompanyProfilePanel,
  ProfilePanel,
  PropertiesPanel,
} from './components'
import { AvatarToggle } from './components/AvatarToggle/AvatarToggle'

function CustomerSidebar({
  isSidePanelOpen,
  openSidePanel,
  closeSidePanel,
  enableMergeCustomer = true, // Note: does not override permissions
  enableChangeCustomer = true, // Note: does not override permissions
}: {
  isSidePanelOpen: boolean
  openSidePanel: () => void
  closeSidePanel: () => void
  enableMergeCustomer?: boolean
  enableChangeCustomer?: boolean
}) {
  const customer = useSelector(getCustomerData)
  const viewMode = useSelector(getCustomerSidebarViewMode)

  const { sidePanelApp, setSidePanelApp, resetSidePanelApp } = useAppSidePanel({
    openSidePanel,
    closeSidePanel,
  })

  useMoveBeacon(isSidePanelOpen ? 'left' : 'right')

  const isCompanyInSidebarEnabled = useFeatureFlag('isCompanyInSidebarEnabled')

  return (
    <CustomerSidebarContext.Provider
      value={{ enableMergeCustomer, enableChangeCustomer }}
    >
      <AvatarToggle />

      {(!isCompanyInSidebarEnabled || viewMode === 'customer') && (
        <ProfilePanel customer={customer} />
      )}

      {isCompanyInSidebarEnabled && viewMode === 'company' && (
        <CompanyProfilePanel />
      )}

      {customer.id && (
        <>
          <PropertiesPanel />
          <AppPlugins
            isSidePanelOpen={isSidePanelOpen}
            setSidePanelApp={setSidePanelApp}
            closeSidePanelApp={resetSidePanelApp}
          >
            {isSidePanelOpen && sidePanelApp && (
              <AppSidePanel
                appId={sidePanelApp.appId}
                iframeSrc={sidePanelApp.iframeSrc}
                isSidePanelOpen={isSidePanelOpen}
                closeSidePanel={resetSidePanelApp}
              />
            )}
          </AppPlugins>
        </>
      )}
    </CustomerSidebarContext.Provider>
  )
}

export { CustomerSidebar }
