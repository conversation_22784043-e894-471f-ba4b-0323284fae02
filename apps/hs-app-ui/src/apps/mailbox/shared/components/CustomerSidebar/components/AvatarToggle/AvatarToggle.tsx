import React, { useCallback } from 'react'
import { useDispatch, useSelector } from 'react-redux'

import { getColor } from 'hsds/utils/color'
import { getFallbackCompanyColorName } from 'shared/utils/companies/color'
import { generateFallbackAvatarUrl } from 'shared/utils/generateFallbackAvatarUrl'

import { useGetCompany } from 'apps/mailbox/hooks/useGetCompany'
import {
  getCustomerData,
  getCustomerSidebarViewMode,
} from 'apps/mailbox/state/customer/reducer'
import { useFeatureFlag, useHsAppContext, usePermission } from 'shared/hooks'

import {
  AvatarContainerUI,
  AvatarToggleContainerUI,
  AvatarUI,
  ToggleAvatarUI,
} from './AvatarToggle.css'

import { setViewMode } from 'apps/mailbox/state/customer/actions'
import { setIsProfileModalOpen } from 'apps/mailbox/state/profilePanel/slice'
import { AvatarProps } from 'hsds/components/avatar/Avatar'
import Pencil from 'hsds/icons/pencil'
import Store from 'hsds/icons/store'

type ViewMode = 'customer' | 'company'

const getCustomerName = (customer: any) => {
  const firstName = customer?.firstName
  const lastName = customer?.lastName

  if (firstName && lastName) {
    return `${firstName} ${lastName}`
  }

  return firstName || lastName || customer?.emails?.[0]?.value || 'Unknown'
}

const getCompanyName = (companyQuery: any) =>
  companyQuery?.data?.name || 'Unknown Company'

/**
 * AvatarToggle displays the main avatar and toggle avatar (if company view is enabled)
 * for switching between customer and company view modes
 */
const AvatarToggle: React.FC = () => {
  const dispatch = useDispatch()
  const viewMode = useSelector(getCustomerSidebarViewMode)
  const customer = useSelector(getCustomerData)
  const companyQuery = useGetCompany(customer?.organizationId)
  const isCompanyInSidebarEnabled = useFeatureFlag('isCompanyInSidebarEnabled')
  const { appData } = useHsAppContext()
  const canUpdateCustomers = usePermission('updateCustomers')
  const companyId = companyQuery?.data?.id

  const toggleViewMode = useCallback(() => {
    const newViewMode: ViewMode =
      viewMode === 'customer' ? 'company' : 'customer'
    dispatch(setViewMode(newViewMode))
  }, [dispatch, viewMode])

  const openProfileModal = useCallback(() => {
    dispatch(setIsProfileModalOpen(true))
  }, [dispatch])

  const openCompanyProfile = useCallback(() => {
    if (companyId) {
      window.open(`/companies/${companyId}`, '_blank', 'noopener,noreferrer')
    }
  }, [companyId])

  const handleKeyDown = useCallback(
    (event: React.KeyboardEvent) => {
      if (
        event.key === 'Enter' ||
        event.key === ' ' || // Chrome / Firefox
        event.key === 'Space' || // Safari TP / spec
        event.key === 'Spacebar' // Legacy
      ) {
        event.preventDefault()
        toggleViewMode()
      }
    },
    [toggleViewMode]
  )

  // Quick exit if no customer data
  const hasCustomer = !!customer && !customer.emptyCustomer
  if (!hasCustomer) return null

  const isCompanyView = viewMode === 'company'
  const avatarBaseUrl = appData.conversationView?.avatarBaseUrl
  const customerId = customer?.id
  const customerName = getCustomerName(customer)
  const companyName = getCompanyName(companyQuery)
  const fallbackAvatarImageUrl = generateFallbackAvatarUrl(
    avatarBaseUrl,
    customerId
  )
  const customerAvatarUrl = customer?.photoUrl || fallbackAvatarImageUrl

  const buildMainAvatarProps = (): AvatarProps => {
    if (isCompanyView) {
      const companyLogo = companyQuery?.data?.logoUrl
      const companyId = companyQuery?.data?.id
      const fallbackColorName = getFallbackCompanyColorName(
        companyId ? Number(companyId) : null
      )
      const fallbackColor = getColor(`pastel.light.${fallbackColorName}`)

      return {
        size: 'xl',
        shape: 'square',
        actionable: true,
        actionIcon: Pencil,
        image: companyLogo || null,
        fallbackImage: null,
        name: companyName,
        onActionClick: openCompanyProfile,
        className: companyLogo ? undefined : fallbackColorName,
        ...(companyLogo
          ? {}
          : {
              fallbackIcon: Store,
              backgroundColor: fallbackColor,
            }),
      }
    }
    return {
      size: 'xl',
      shape: 'circle',
      actionable: Boolean(customerId),
      actionIcon: Pencil,
      image: customerAvatarUrl,
      fallbackImage: fallbackAvatarImageUrl,
      onActionClick: openProfileModal,
    }
  }

  const buildToggleAvatarProps = () => {
    const shape: 'circle' | 'square' = isCompanyView ? 'circle' : 'square'
    const companyLogo = companyQuery?.data?.logoUrl
    const companyId = companyQuery?.data?.id
    const fallbackColorName = getFallbackCompanyColorName(
      companyId ? Number(companyId) : null
    )
    const fallbackColor = getColor(`pastel.light.${fallbackColorName}`)

    const baseProps = {
      size: 'sm' as const,
      shape: shape,
      $shape: shape,
      name: isCompanyView
        ? customerName
        : `Switch to ${companyName}` || 'Switch to Company',
      image: isCompanyView ? customerAvatarUrl : companyLogo || null,
      fallbackImage: isCompanyView ? fallbackAvatarImageUrl : null,
      role: 'button',
      tabIndex: 0,
      onClick: toggleViewMode,
      onKeyDown: handleKeyDown,
      'aria-label': `Toggle to ${isCompanyView ? 'customer' : 'company'} view`,
      'data-cy': 'Sidebar.ViewModeToggle',
      className: !isCompanyView && !companyLogo ? fallbackColorName : undefined,
    }

    // Add fallback icon and color for company toggle when no logo
    if (!isCompanyView && !companyLogo) {
      return {
        ...baseProps,
        fallbackIcon: Store,
        backgroundColor: fallbackColor,
      }
    }

    return baseProps
  }

  // Render simple avatar if company sidebar is disabled or no company data
  const hasCompanyData = Boolean(
    customer?.organizationId && companyQuery?.data // any company payload
  )
  if (!isCompanyInSidebarEnabled || !hasCompanyData) {
    return (
      <AvatarContainerUI $shape={isCompanyView ? 'square' : 'circle'}>
        <AvatarUI
          {...buildMainAvatarProps()}
          aria-label={
            isCompanyView
              ? `View company: ${companyName}`
              : `${
                  canUpdateCustomers ? 'Edit' : 'View'
                } customer: ${customerName}`
          }
        />
      </AvatarContainerUI>
    )
  }

  // Render avatar with toggle when company sidebar is enabled
  const mainAvatarProps = buildMainAvatarProps()
  const toggleAvatarProps = buildToggleAvatarProps()
  const labelVerb = canUpdateCustomers ? 'Edit' : 'View'

  return (
    <AvatarContainerUI $shape={isCompanyView ? 'square' : 'circle'}>
      <AvatarToggleContainerUI>
        <AvatarUI
          {...mainAvatarProps}
          aria-label={
            isCompanyView
              ? `${labelVerb} company: ${companyName}`
              : `${labelVerb} customer: ${customerName}`
          }
        />
        {hasCompanyData && <ToggleAvatarUI {...toggleAvatarProps} />}
      </AvatarToggleContainerUI>
    </AvatarContainerUI>
  )
}

export { AvatarToggle }
