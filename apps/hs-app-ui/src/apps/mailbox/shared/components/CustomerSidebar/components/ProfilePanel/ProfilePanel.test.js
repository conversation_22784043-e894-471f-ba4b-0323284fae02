import nock from 'nock'
import PropTypes from 'prop-types'

import { screen, waitFor, within } from '@testing-library/react'
import userEvent from '@testing-library/user-event'

import { setHsAppLocalStorageValue } from 'shared/utils'

import { CustomerSidebarContext } from 'apps/mailbox/shared/components/CustomerSidebar/hooks/useCustomerSidebarContext'

import { SOURCE_TYPES } from 'apps/mailbox/App/constants/sourceTypes'

import { ProfilePanel } from './ProfilePanel'
import { aConversation } from 'apps/mailbox/testUtils/builders/conversationBuilder'
import {
  generateWindowAppData,
  renderTestConversation,
  resetAppData,
} from 'apps/mailbox/testUtils/setup'
import { waitForRequestsToFinish, waitTime } from 'shared/testUtils/async-utils'

const CUSTOMER_ID = 123
const CONVERSATION_ID = 789
const MAILBOX_ID = 251116

const baseCustomer = {
  emails: [],
  phones: [],
  properties: [],
  socialProfiles: [],
  websites: [],
}

const createInitialState = customer => ({
  customer: {
    data: customer
      ? { ...baseCustomer, ...customer }
      : {
          ...baseCustomer,
          conversationCount: 1,
          id: CUSTOMER_ID,
          firstName: 'Bob',
          lastName: 'Belcher',
          jobTitle: 'Entrepreneur',
          organization: "Bob's Burgers",
          location: 'Toronto, ON',
          background: 'Burger maker extraordinaire',
          phones: [{ id: 123456, location: 'work', value: '9059993344' }],
          properties: [
            { type: 'text', slug: 'customField', name: 'Custom Field' },
          ],
          emails: [
            {
              id: 123457,
              location: 'work',
              value: '<EMAIL>',
            },
            {
              id: 123458,
              location: 'work',
              value: '<EMAIL>',
            },
          ],
          websites: [
            {
              id: 123459,
              value: 'https://www.example.com',
            },
          ],
        },
    isFetching: false,
    isSaving: false,
  },
  conversation: {
    data: {
      id: CONVERSATION_ID,
      source: {
        type: 'web',
        via: 'user',
      },
    },
  },
})

const WrappedProfilePanel = ({
  contextValues = { enableChangeCustomer: true, enableMergeCustomer: true },
  ...rest
}) => (
  <CustomerSidebarContext.Provider value={contextValues}>
    <ProfilePanel {...rest} />
  </CustomerSidebarContext.Provider>
)

WrappedProfilePanel.propTypes = {
  contextValues: PropTypes.shape({
    enableChangeCustomer: PropTypes.bool,
    enableMergeCustomer: PropTypes.bool,
  }),
  ...ProfilePanel.propTypes,
}

describe('Profile Panel', () => {
  beforeEach(() => {
    generateWindowAppData()
    nock.cleanAll()
    window.open = jest.fn()
  })

  afterEach(() => {
    resetAppData()
    jest.restoreAllMocks()
  })

  describe('Identified Visitor', () => {
    let initialState

    beforeEach(() => {
      initialState = createInitialState()
    })

    it('renders a complete profile', () => {
      const { getByText } = renderTestConversation(
        { initialState },
        <WrappedProfilePanel customer={initialState.customer.data} />
      )

      expect(getByText('Bob Belcher')).toBeInTheDocument()
      expect(getByText('Entrepreneur')).toBeInTheDocument()
      expect(getByText("Bob's Burgers")).toBeInTheDocument()
      expect(getByText('Toronto, ON')).toBeInTheDocument()
      expect(getByText('Burger maker extraordinaire')).toBeInTheDocument()
      expect(getByText('9059993344')).toBeInTheDocument()

      expect(getByText('<EMAIL>')).toBeInTheDocument()
    })

    it('does open the profile dialog when the name is clicked', () => {
      const { getByText, queryByText } = renderTestConversation(
        { initialState },
        <WrappedProfilePanel customer={initialState.customer.data} />
      )

      let modalEditButton = queryByText('Edit Full Profile')
      expect(modalEditButton).toBeNull()

      getByText('Bob Belcher').click()

      modalEditButton = getByText('Edit Full Profile')
      expect(modalEditButton).toBeInTheDocument()
    })

    it('should open a new conversation when an email is clicked', () => {
      // Set the correct mailbox ID in the app data
      generateWindowAppData({
        shared: {
          mailbox: { id: MAILBOX_ID },
        },
      })

      // Save the original location.href
      const originalHref = window.location.href

      // Create a mock function for the href setter
      const mockHref = jest.fn()

      // Replace window.location with a mock
      Object.defineProperty(window, 'location', {
        value: {
          href: 'http://example.com',
          assign: jest.fn(),
          replace: jest.fn(),
        },
        writable: true,
      })

      // Replace the href property with our mock
      Object.defineProperty(window.location, 'href', {
        set: mockHref,
        get: () => originalHref,
        configurable: true,
      })

      const initialState = createInitialState()

      const { getByText } = renderTestConversation(
        { initialState },
        <WrappedProfilePanel customer={initialState.customer.data} />
      )

      // Find the email link and click it
      const emailText = getByText('<EMAIL>')
      const emailLink = emailText.closest('a')

      // Prevent default behavior
      emailLink.addEventListener('click', e => {
        e.preventDefault()
        window.location.href = e.currentTarget.href
      })

      userEvent.click(emailLink)

      // Verify the mock was called
      expect(mockHref).toHaveBeenCalled()
      const calledWithArg = mockHref.mock.calls[0][0]
      expect(calledWithArg).toBe(
        `http://localhost/mailbox/${MAILBOX_ID}/customer/${CUSTOMER_ID}/${initialState.customer.data.emails[0].id}`
      )
    })

    it('does open the profile dialog when the organization/company is clicked', () => {
      const { getByText, queryByText } = renderTestConversation(
        { initialState },
        <WrappedProfilePanel customer={initialState.customer.data} />
      )

      let modalEditButton = queryByText('Edit Full Profile')
      expect(modalEditButton).toBeNull()

      getByText("Bob's Burgers").click()

      modalEditButton = getByText('Edit Full Profile')
      expect(modalEditButton).toBeInTheDocument()
    })

    it('does open a new tab when the website is clicked', () => {
      const { getByText } = renderTestConversation(
        { initialState },
        <WrappedProfilePanel customer={initialState.customer.data} />
      )

      const link = getByText('example.com').closest('a')
      expect(link).toHaveAttribute('href', 'https://www.example.com')
      expect(link).toHaveAttribute('target', '_blank')
      expect(link).toHaveAttribute('rel', 'noopener noreferrer')
    })

    it('opens the profile dialog when the customer name is clicked', () => {
      const { getByText, queryByText } = renderTestConversation(
        { initialState },
        <WrappedProfilePanel customer={initialState.customer.data} />
      )

      let modalEditButton = queryByText('Edit Full Profile')
      expect(modalEditButton).toBeNull()

      getByText('Bob Belcher').click()

      modalEditButton = getByText('Edit Full Profile')
      expect(modalEditButton).toBeInTheDocument()
    })

    describe('update customer and edit conversation permissions', () => {
      beforeAll(() => {
        global.hsGlobal.memberPermissions.updateCustomers = false
        global.hsGlobal.memberPermissions.editConversationDetails = false
      })
      afterAll(() => {
        global.hsGlobal.memberPermissions.updateCustomers = true
        global.hsGlobal.memberPermissions.editConversationDetails = true
      })
      it('Should hide "Change Customer" option in the profile panel action menu if user does not have edit conversation details permissions', () => {
        const { getByLabelText, queryByRole } = renderTestConversation(
          { initialState },
          <WrappedProfilePanel customer={initialState.customer.data} />
        )

        getByLabelText('Customer Options').click()
        const changeCustomerOption = queryByRole('option', {
          name: 'Change Customer',
        })
        expect(changeCustomerOption).not.toBeInTheDocument()
      })
      it.each([
        'Company',
        'Job Title',
        'Email',
        'Phone',
        'Notes',
        'Custom Field',
      ])(
        '%s field should be have a "--" placeholder and be disabled for users who do not have update customer permissions',
        fieldName => {
          const { getByText, getByRole } = renderTestConversation(
            { initialState },
            <WrappedProfilePanel customer={initialState.customer.data} />
          )

          getByText('Bob Belcher').click()

          const companyField = getByRole('textbox', { name: fieldName })

          expect(companyField).toHaveProperty('placeholder', '—')
          expect(companyField).toHaveProperty('disabled')
        }
      )
      it('should not be able to edit the customer first and last name, in the profile modal, if user does not have update customer permissions ', () => {
        const { getByText, getByTestId, queryByPlaceholderText } =
          renderTestConversation(
            { initialState },
            <WrappedProfilePanel customer={initialState.customer.data} />
          )

        getByText('Bob Belcher').click()

        expect(getByTestId('read-only-customer-name')).toBeInTheDocument()
        expect(queryByPlaceholderText('First Name')).not.toBeInTheDocument()
        expect(queryByPlaceholderText('Last Name')).not.toBeInTheDocument()
      })
      it('should not be able to see the edit full profile button if user does not have update customer permissions ', () => {
        const { getByText, queryByText } = renderTestConversation(
          { initialState },
          <WrappedProfilePanel customer={initialState.customer.data} />
        )

        getByText('Bob Belcher').click()

        expect(queryByText('Edit Full Profile')).not.toBeInTheDocument()
      })
      it('the dropdown item to access the customer profile should read "View Profile" if user does not have update customers permissions', () => {
        const { getByRole, getByLabelText } = renderTestConversation(
          { initialState },
          <WrappedProfilePanel customer={initialState.customer.data} />
        )

        getByLabelText('Customer Options').click()
        const viewProfileOption = getByRole('option', { name: 'View Profile' })
        expect(viewProfileOption).toBeInTheDocument()
      })
      it('the dropdown item to access the customer profile should read "Edit Profile" if the user has update customers permissions', () => {
        global.hsGlobal.memberPermissions.updateCustomers = true
        const { getByRole, getByLabelText } = renderTestConversation(
          { initialState },
          <WrappedProfilePanel customer={initialState.customer.data} />
        )

        getByLabelText('Customer Options').click()
        const viewProfileOption = getByRole('option', { name: 'Edit Profile' })
        expect(viewProfileOption).toBeInTheDocument()
      })
    })
  })

  describe('Unidentified Visitor', () => {
    let initialState

    beforeEach(() => {
      initialState = createInitialState({})
    })

    it('renders a "blank profile"', () => {
      const { getByText } = renderTestConversation(
        { initialState },
        <WrappedProfilePanel customer={initialState?.customer?.data} />
      )
      expect(getByText('Unidentified Visitor')).toBeInTheDocument()
    })

    it('does not open the profile dialog when the name is clicked', () => {
      const { getByText, queryByText } = renderTestConversation(
        { initialState },
        <WrappedProfilePanel customer={initialState?.customer?.data} />
      )

      let modalEditButton = queryByText('Edit Full Profile')
      expect(modalEditButton).toBeNull()

      getByText('Unidentified Visitor').click()

      modalEditButton = queryByText('Edit Full Profile')
      expect(modalEditButton).toBeNull()
    })
  })

  // this one is not working :(
  it.skip('opens the Change Customer modal when "change customer" menu item is clicked', async () => {
    const initialState = createInitialState()
    mockGetCustomerSuggestionsList()
    const { getByTestId } = renderTestConversation(
      { initialState },
      <WrappedProfilePanel customer={initialState?.customer?.data} />
    )

    const threeDots = getByTestId('PanelActionMenu.Toggler')
    userEvent.click(threeDots)

    const menuItem = screen.getByRole('option', { name: 'Change Customer' })
    userEvent.click(menuItem)
    await waitForRequestsToFinish()

    await waitFor(() => {
      const modalDialog = within(getByTestId('simple-modal')).getByRole(
        'dialog',
        {
          name: 'Change Customer',
        }
      )
      expect(modalDialog).toBeInTheDocument()
    })
  })

  it('Change Customer option is removed when enableChangeCustomer is false', async () => {
    const initialState = createInitialState()
    renderTestConversation(
      { initialState },
      <WrappedProfilePanel
        customer={initialState?.customer?.data}
        contextValues={{
          enableChangeCustomer: false,
        }}
      />
    )

    userEvent.click(screen.getByTestId('PanelActionMenu.Toggler'))

    expect(
      screen.queryByRole('option', { name: 'Change Customer' })
    ).not.toBeInTheDocument()
    await waitTime()
  })

  it('Change Customer option is removed when Messenger conversation', () => {
    const initialState = {
      ...createInitialState(),
      conversation: aConversation()
        .withSourceType(SOURCE_TYPES.MESSENGER)
        .build(),
    }
    renderTestConversation(
      { initialState },
      <WrappedProfilePanel customer={initialState?.customer?.data} />
    )

    userEvent.click(screen.getByTestId('PanelActionMenu.Toggler'))

    expect(
      screen.queryByRole('option', { name: 'Change Customer' })
    ).not.toBeInTheDocument()
  })

  it('Change Customer option is removed when Instagram conversation', () => {
    const initialState = {
      ...createInitialState(),
      conversation: aConversation()
        .withSourceType(SOURCE_TYPES.INSTAGRAM)
        .build(),
    }
    renderTestConversation(
      { initialState },
      <WrappedProfilePanel customer={initialState?.customer?.data} />
    )

    userEvent.click(screen.getByTestId('PanelActionMenu.Toggler'))

    expect(
      screen.queryByRole('option', { name: 'Change Customer' })
    ).not.toBeInTheDocument()
  })

  it('Change Customer option is NOT removed when AI Answers conversation', () => {
    const initialState = {
      ...createInitialState(),
      conversation: aConversation()
        .withSourceType(SOURCE_TYPES.AI_ANSWERS)
        .build(),
    }
    renderTestConversation(
      { initialState },
      <WrappedProfilePanel customer={initialState?.customer?.data} />
    )

    userEvent.click(screen.getByTestId('PanelActionMenu.Toggler'))

    expect(
      screen.getByRole('option', { name: 'Change Customer' })
    ).toBeInTheDocument()
  })

  describe('Properties Section', () => {
    let initialState

    beforeEach(() => {
      initialState = createInitialState()
    })

    it('does not display the edit properties button when isPropertiesPanelEnabled is false', () => {
      global.hsGlobal.features.isPropertiesPanelEnabled = false

      renderTestConversation(
        { initialState },
        <WrappedProfilePanel customer={initialState.customer.data} />
      )

      expect(
        screen.queryByRole('button', { name: 'Edit Properties' })
      ).not.toBeInTheDocument()
    })
  })

  describe('Properties Toggle Menu Option', () => {
    // Set up the mock for useFeatureFlag only for this describe block
    let originalUseFeatureFlag

    beforeEach(() => {
      // Store the original implementation
      originalUseFeatureFlag = require('shared/hooks').useFeatureFlag

      // Mock the implementation
      jest.mock('shared/hooks', () => ({
        ...jest.requireActual('shared/hooks'),
        useFeatureFlag: flagName => {
          if (flagName === 'isPropertiesPanelEnabled') {
            return true
          }
          return jest.requireActual('shared/hooks').useFeatureFlag(flagName)
        },
      }))

      // Make sure the feature flag is enabled globally too
      global.hsGlobal.features.isPropertiesPanelEnabled = true
    })

    afterEach(() => {
      // Restore the original implementation
      require('shared/hooks').useFeatureFlag = originalUseFeatureFlag
      jest.resetModules()
      global.hsGlobal.features.isPropertiesPanelEnabled = false
    })

    it('should show the toggle properties Hide Properties option when properties exist and localStorage sidebar.properties.visible === true', () => {
      const initialState = createInitialState({
        properties: [
          { type: 'text', slug: 'customField', name: 'Custom Field' },
        ],
      })
      setHsAppLocalStorageValue('sidebar.properties.visible', true)

      const { getByLabelText, getByRole } = renderTestConversation(
        { initialState },
        <WrappedProfilePanel customer={initialState.customer.data} />
      )

      getByLabelText('Customer Options').click()

      const togglePropertiesOptionHide = getByRole('option', {
        name: 'Hide Properties',
      })
      expect(togglePropertiesOptionHide).toBeInTheDocument()
    })

    it('should show the toggle properties Show Properties option when properties exist and localStorage sidebar.properties.visible === false', () => {
      const initialState = createInitialState({
        properties: [
          { type: 'text', slug: 'customField', name: 'Custom Field' },
        ],
      })
      setHsAppLocalStorageValue('sidebar.properties.visible', false)

      const { getByLabelText, getByRole } = renderTestConversation(
        { initialState },
        <WrappedProfilePanel customer={initialState.customer.data} />
      )

      getByLabelText('Customer Options').click()

      const togglePropertiesOptionShow = getByRole('option', {
        name: 'Show Properties',
      })
      expect(togglePropertiesOptionShow).toBeInTheDocument()
    })

    it('should not show the toggle properties option when no properties exist', () => {
      const initialState = createInitialState({
        properties: [],
      })

      const { getByLabelText, queryByRole } = renderTestConversation(
        { initialState },
        <WrappedProfilePanel customer={initialState.customer.data} />
      )

      getByLabelText('Customer Options').click()

      const togglePropertiesOptionHide = queryByRole('option', {
        name: 'Hide Properties',
      })
      const togglePropertiesOptionShow = queryByRole('option', {
        name: 'Show Properties',
      })
      expect(togglePropertiesOptionHide).not.toBeInTheDocument()
      expect(togglePropertiesOptionShow).not.toBeInTheDocument()
    })

    it('should not show the toggle properties option when feature flag is disabled', () => {
      global.hsGlobal.features.isPropertiesPanelEnabled = false

      const initialState = createInitialState({
        properties: [
          { type: 'text', slug: 'customField', name: 'Custom Field' },
        ],
      })

      const { getByLabelText, queryByRole } = renderTestConversation(
        { initialState },
        <WrappedProfilePanel customer={initialState.customer.data} />
      )

      getByLabelText('Customer Options').click()

      const togglePropertiesOptionHide = queryByRole('option', {
        name: 'Hide Properties',
      })
      const togglePropertiesOptionShow = queryByRole('option', {
        name: 'Show Properties',
      })
      expect(togglePropertiesOptionHide).not.toBeInTheDocument()
      expect(togglePropertiesOptionShow).not.toBeInTheDocument()
    })

    it('should toggle properties visibility when the menu option is clicked, if feature flag is enabled', async () => {
      global.hsGlobal.features.isPropertiesPanelEnabled = true
      setHsAppLocalStorageValue('sidebar.properties.visible', true)

      const initialState = createInitialState()

      const { getByTestId, queryByRole, queryByTestId } =
        renderTestConversation(
          { initialState },
          <WrappedProfilePanel customer={initialState.customer.data} />
        )

      // Properties section should be visible initially
      expect(getByTestId('properties-section')).toBeInTheDocument()

      // Open the menu by clicking the toggler
      getByTestId('PanelActionMenu.Toggler').click()

      // Click "Hide Properties"
      const hidePropertiesOption = queryByRole('option', {
        name: 'Hide Properties',
      })
      expect(hidePropertiesOption).toBeInTheDocument()
      hidePropertiesOption.click()

      // Wait for the timeout in toggleProperties to complete
      await waitFor(
        () => {
          expect(queryByTestId('properties-section')).not.toBeInTheDocument()
        },
        { timeout: 400 }
      )

      // Open the menu again
      getByTestId('PanelActionMenu.Toggler').click()

      // Verify the option text has changed to "Show Properties"
      const showPropertiesOption = queryByRole('option', {
        name: 'Show Properties',
      })
      expect(showPropertiesOption).toBeInTheDocument()
      showPropertiesOption.click()

      // Wait for the timeout in toggleProperties to complete
      await waitFor(
        () => {
          expect(getByTestId('properties-section')).toBeInTheDocument()
        },
        { timeout: 400 }
      )
    })
  })

  describe('New Conversation Menu Option', () => {
    it('should render "New Conversation" option if there is an email', () => {
      global.hsGlobal.appData = {
        shared: {
          mailbox: { id: MAILBOX_ID },
        },
      }

      const initialState = createInitialState()

      const { getByTestId, getByRole } = renderTestConversation(
        { initialState },
        <WrappedProfilePanel customer={initialState.customer.data} />
      )

      // Open the menu by clicking the toggler
      getByTestId('PanelActionMenu.Toggler').click()

      // Click "New Conversation"
      const newConversationOption = getByRole('option', {
        name: 'New Conversation',
      })
      expect(newConversationOption).toBeInTheDocument()
    })

    it('should open a new conversation when the "New Conversation" option is clicked if there is an email', () => {
      global.hsGlobal.appData = {
        shared: {
          mailbox: { id: MAILBOX_ID },
        },
      }

      // Save the original location.href
      const originalHref = window.location.href

      // Create a mock function for the href setter
      const mockHref = jest.fn()

      // Replace window.location with a mock
      Object.defineProperty(window, 'location', {
        value: {
          href: 'http://example.com',
        },
        writable: true,
      })

      // Replace the href property with our mock
      Object.defineProperty(window.location, 'href', {
        set: mockHref,
        get: () => originalHref,
        configurable: true,
      })

      const initialState = createInitialState()

      const { getByTestId, getByRole } = renderTestConversation(
        { initialState },
        <WrappedProfilePanel customer={initialState.customer.data} />
      )

      userEvent.click(getByTestId('PanelActionMenu.Toggler'))
      const newConversationOption = getByRole('option', {
        name: 'New Conversation',
      })
      expect(newConversationOption).toBeInTheDocument()
      userEvent.click(newConversationOption)

      expect(mockHref).toHaveBeenCalled()
      const calledWithArg = mockHref.mock.calls[0][0]
      expect(calledWithArg).toMatch(
        new RegExp(
          `/mailbox/\\d+/customer/${CUSTOMER_ID}/${initialState.customer.data.emails[0].id}`
        )
      )
    })
  })
})

const mockGetCustomerSuggestionsList = ({ data = [] } = { data: [] }) => {
  return nock('http://localhost')
    .get(`/api/v1/conversations/${CONVERSATION_ID}/customer/suggestions`)
    .query(true)
    .reply(200, data)
}
