import React from 'react'
import { Provider } from 'react-redux'

import { fireEvent, render, screen } from '@testing-library/react'

import { useGetCompany } from 'apps/mailbox/hooks/useGetCompany'

import { AvatarToggle } from './AvatarToggle'
import createMailboxStore from 'apps/mailbox/state'
import * as customerActions from 'apps/mailbox/state/customer/actions'
import { HsAppContext } from 'shared/components/HsApp'

// Mock the useGetCompany hook
jest.mock('apps/mailbox/hooks/useGetCompany', () => ({
  useGetCompany: jest.fn(),
}))

// Default mock data for company
const defaultCompanyMockData = {
  data: { name: 'Acme Inc' },
  isLoading: false,
  error: null,
}

// Mock the usePermission hook
jest.mock('shared/hooks', () => {
  const original = jest.requireActual('shared/hooks')
  return {
    ...original,
    usePermission: jest.fn(() => true), // Default to having permissions
  }
})

const createTestStore = (initialState: any = {}) => {
  return createMailboxStore(initialState)
}

const renderWithProviders = (
  ui: React.ReactElement,
  {
    initialState = {},
    featureFlags = { isCompanyInSidebarEnabled: true },
    hsAppContextValue,
  }: {
    initialState?: any
    featureFlags?: { isCompanyInSidebarEnabled: boolean }
    hsAppContextValue?: any
  } = {}
) => {
  const store = createTestStore(initialState)
  const defaultHsAppContextValue = {
    hsGlobal: {
      features: featureFlags,
    },
    appData: {
      conversationView: {
        avatarBaseUrl: 'https://example.com/avatars',
      },
    },
  }

  return {
    ...render(
      <HsAppContext.Provider
        value={hsAppContextValue || defaultHsAppContextValue}
      >
        <Provider store={store}>{ui}</Provider>
      </HsAppContext.Provider>
    ),
    store,
  }
}

describe('AvatarToggle', () => {
  let setViewModeSpy: jest.SpyInstance
  let windowOpenSpy: jest.SpyInstance

  beforeEach(() => {
    setViewModeSpy = jest.spyOn(customerActions, 'setViewMode')
    windowOpenSpy = jest.spyOn(window, 'open').mockImplementation(() => null)
    // Initialize mock with default return value
    ;(useGetCompany as jest.Mock).mockReturnValue(defaultCompanyMockData)
  })

  afterEach(() => {
    // Reset the mock and restore default return value
    ;(useGetCompany as jest.Mock).mockReset()
    ;(useGetCompany as jest.Mock).mockReturnValue(defaultCompanyMockData)
    jest.clearAllMocks()
  })

  it('renders a toggle button in customer mode', () => {
    renderWithProviders(<AvatarToggle />, {
      initialState: {
        customer: {
          viewMode: 'customer',
          data: {
            id: 123,
            firstName: 'John',
            organization: 'Acme Inc',
            organizationId: 456,
            emptyCustomer: false,
          },
        },
      },
    })

    const toggleButton = screen.getByRole('button', {
      name: /toggle to company view/i,
    })

    expect(toggleButton).toBeInTheDocument()
  })

  it('renders a toggle button in company mode', () => {
    renderWithProviders(<AvatarToggle />, {
      initialState: {
        customer: {
          viewMode: 'company',
          data: {
            id: 123,
            firstName: 'John',
            organization: 'Acme Inc',
            organizationId: 456,
            emptyCustomer: false,
          },
        },
      },
    })

    const toggleButton = screen.getByRole('button', {
      name: /toggle to customer view/i,
    })

    expect(toggleButton).toBeInTheDocument()
  })

  it('dispatches action when toggle button is clicked', () => {
    const { store } = renderWithProviders(<AvatarToggle />, {
      initialState: {
        customer: {
          viewMode: 'customer',
          data: {
            id: 123,
            firstName: 'John',
            organization: 'Acme Inc',
            organizationId: 456,
            emptyCustomer: false,
          },
        },
      },
    })

    const toggleButton = screen.getByRole('button', {
      name: /toggle to company view/i,
    })
    fireEvent.click(toggleButton)

    expect(setViewModeSpy).toHaveBeenCalledWith('company')
    expect(store.getState().customer.viewMode).toBe('company')
  })

  it('dispatches action when Enter key is pressed on toggle button', () => {
    const { store } = renderWithProviders(<AvatarToggle />, {
      initialState: {
        customer: {
          viewMode: 'customer',
          data: {
            id: 123,
            firstName: 'John',
            organization: 'Acme Inc',
            organizationId: 456,
            emptyCustomer: false,
          },
        },
      },
    })

    const toggleButton = screen.getByRole('button', {
      name: /toggle to company view/i,
    })
    fireEvent.keyDown(toggleButton, { key: 'Enter', code: 'Enter' })

    expect(setViewModeSpy).toHaveBeenCalledWith('company')
    expect(store.getState().customer.viewMode).toBe('company')
  })

  it('does not show toggle button when feature flag is disabled', () => {
    renderWithProviders(<AvatarToggle />, {
      initialState: {
        customer: {
          viewMode: 'company',
          data: {
            id: 123,
            firstName: 'John',
            organization: 'Acme Inc',
            organizationId: 456,
            emptyCustomer: false,
          },
        },
      },
      featureFlags: { isCompanyInSidebarEnabled: false },
    })

    const toggleButton = screen.queryByRole('button', {
      name: /toggle to/i,
    })
    expect(toggleButton).not.toBeInTheDocument()

    const avatarElement = document.querySelector('.c-Avatar')
    expect(avatarElement).toBeInTheDocument()
  })

  it('shows customer avatar as toggle when in company mode', () => {
    renderWithProviders(<AvatarToggle />, {
      initialState: {
        customer: {
          viewMode: 'company',
          data: {
            id: 123,
            firstName: 'John',
            lastName: 'Doe',
            organization: 'Acme Inc',
            organizationId: 456,
            emptyCustomer: false,
          },
        },
      },
    })

    const toggleButton = screen.getByRole('button', {
      name: /toggle to customer view/i,
    })

    expect(toggleButton).toBeInTheDocument()
    expect(toggleButton).toHaveClass('c-Avatar')
  })

  it('shows company avatar as toggle when in customer mode', () => {
    renderWithProviders(<AvatarToggle />, {
      initialState: {
        customer: {
          viewMode: 'customer',
          data: {
            id: 123,
            firstName: 'John',
            lastName: 'Doe',
            organization: 'Acme Inc',
            organizationId: 456,
            emptyCustomer: false,
          },
        },
      },
    })

    const toggleButton = screen.getByRole('button', {
      name: /toggle to company view/i,
    })

    expect(toggleButton).toBeInTheDocument()
    expect(toggleButton).toHaveClass('c-Avatar')
  })

  it('returns null when there is no customer', () => {
    const { container } = renderWithProviders(<AvatarToggle />, {
      initialState: {
        customer: {
          viewMode: 'customer',
          data: {
            emptyCustomer: true,
          },
        },
      },
    })

    expect(container).toBeEmptyDOMElement()
  })

  it('does not show toggle button when feature flag is enabled but customer has no company data', () => {
    renderWithProviders(<AvatarToggle />, {
      initialState: {
        customer: {
          viewMode: 'customer',
          data: {
            id: 123,
            firstName: 'John',
            lastName: 'Doe',
            emptyCustomer: false,
            // No organizationId
          },
        },
      },
      featureFlags: { isCompanyInSidebarEnabled: true },
    })

    const toggleButton = screen.queryByRole('button', {
      name: /toggle to/i,
    })
    expect(toggleButton).not.toBeInTheDocument()

    const avatarElement = document.querySelector('.c-Avatar')
    expect(avatarElement).toBeInTheDocument()
  })

  it('does not show toggle button when customer has organizationId but company query returns no data', () => {
    // Mock useGetCompany to return no data for this test
    ;(useGetCompany as jest.Mock).mockReturnValueOnce({
      data: null,
      isLoading: false,
      error: null,
    })

    renderWithProviders(<AvatarToggle />, {
      initialState: {
        customer: {
          viewMode: 'customer',
          data: {
            id: 123,
            firstName: 'John',
            lastName: 'Doe',
            organizationId: 456,
            emptyCustomer: false,
          },
        },
      },
      featureFlags: { isCompanyInSidebarEnabled: true },
    })

    const toggleButton = screen.queryByRole('button', {
      name: /toggle to/i,
    })
    expect(toggleButton).not.toBeInTheDocument()

    const avatarElement = document.querySelector('.c-Avatar')
    expect(avatarElement).toBeInTheDocument()
  })

  it('opens company profile in new tab when company avatar action is clicked', () => {
    // Mock useGetCompany to return company data with ID
    ;(useGetCompany as jest.Mock).mockReturnValueOnce({
      data: { id: '789', name: 'Acme Inc' },
      isLoading: false,
      error: null,
    })

    renderWithProviders(<AvatarToggle />, {
      initialState: {
        customer: {
          viewMode: 'company',
          data: {
            id: 123,
            firstName: 'John',
            lastName: 'Doe',
            organizationId: 456,
            emptyCustomer: false,
          },
        },
      },
    })

    // Find the main avatar's action button (pencil icon)
    const actionButton = screen.getByRole('button', { name: /edit company/i })
    fireEvent.click(actionButton)

    expect(windowOpenSpy).toHaveBeenCalledWith(
      '/companies/789',
      '_blank',
      'noopener,noreferrer'
    )
  })

  it('does not open tab when company avatar action is clicked but no company ID', () => {
    // Mock useGetCompany to return company data without ID
    ;(useGetCompany as jest.Mock).mockReturnValueOnce({
      data: { name: 'Acme Inc' }, // No ID
      isLoading: false,
      error: null,
    })

    renderWithProviders(<AvatarToggle />, {
      initialState: {
        customer: {
          viewMode: 'company',
          data: {
            id: 123,
            firstName: 'John',
            lastName: 'Doe',
            organizationId: 456,
            emptyCustomer: false,
          },
        },
      },
    })

    // Find the main avatar's action button (pencil icon)
    const actionButton = screen.getByRole('button', { name: /edit company/i })
    fireEvent.click(actionButton)

    expect(windowOpenSpy).not.toHaveBeenCalled()
  })

  it('uses company logo as avatar image when available', () => {
    // Mock useGetCompany to return company data with logo
    ;(useGetCompany as jest.Mock).mockReturnValueOnce({
      data: {
        id: '789',
        name: 'Acme Inc',
        logoUrl: 'https://example.com/company-logo.png',
      },
      isLoading: false,
      error: null,
    })

    renderWithProviders(<AvatarToggle />, {
      initialState: {
        customer: {
          viewMode: 'company',
          data: {
            id: 123,
            firstName: 'John',
            lastName: 'Doe',
            organizationId: 456,
            emptyCustomer: false,
          },
        },
      },
    })

    // Verify the main avatar is rendered and has square shape
    const avatarElement = document.querySelector('.c-Avatar')
    expect(avatarElement).toBeInTheDocument()
    expect(avatarElement).toHaveClass('is-shape-square')
  })

  it('uses company logo in toggle avatar when in customer mode', () => {
    // Mock useGetCompany to return company data with logo
    ;(useGetCompany as jest.Mock).mockReturnValueOnce({
      data: {
        id: '789',
        name: 'Acme Inc',
        logoUrl: 'https://example.com/company-logo.png',
      },
      isLoading: false,
      error: null,
    })

    renderWithProviders(<AvatarToggle />, {
      initialState: {
        customer: {
          viewMode: 'customer',
          data: {
            id: 123,
            firstName: 'John',
            lastName: 'Doe',
            organizationId: 456,
            emptyCustomer: false,
          },
        },
      },
    })

    const toggleButton = screen.getByRole('button', {
      name: /toggle to company view/i,
    })
    expect(toggleButton).toBeInTheDocument()

    // The toggle avatar should be a company avatar (showing the small company logo)
    expect(toggleButton).toHaveClass('c-Avatar')
    expect(toggleButton).toHaveClass('is-shape-square')
  })

  it('falls back to no image when company has no logo', () => {
    // Mock useGetCompany to return company data without logo
    ;(useGetCompany as jest.Mock).mockReturnValueOnce({
      data: {
        id: '789',
        name: 'Acme Inc',
        // No logoUrl
      },
      isLoading: false,
      error: null,
    })

    renderWithProviders(<AvatarToggle />, {
      initialState: {
        customer: {
          viewMode: 'company',
          data: {
            id: 123,
            firstName: 'John',
            lastName: 'Doe',
            organizationId: 456,
            emptyCustomer: false,
          },
        },
      },
    })

    // Find the main avatar element
    const avatarElement = document.querySelector('.c-Avatar')
    expect(avatarElement).toBeInTheDocument()

    // Should still be square shape even without logo
    expect(avatarElement).toHaveClass('is-shape-square')
  })

  it('uses fallback icon and color when company has no logo in main avatar', () => {
    // Mock useGetCompany to return company data without logo
    ;(useGetCompany as jest.Mock).mockReturnValueOnce({
      data: {
        id: '789',
        name: 'Acme Inc',
        // No logoUrl
      },
      isLoading: false,
      error: null,
    })

    renderWithProviders(<AvatarToggle />, {
      initialState: {
        customer: {
          viewMode: 'company',
          data: {
            id: 123,
            firstName: 'John',
            lastName: 'Doe',
            organizationId: 456,
            emptyCustomer: false,
          },
        },
      },
    })

    // Find the main avatar element and verify it's using fallback icon/color
    const avatarElement = document.querySelector('.c-Avatar')
    expect(avatarElement).toBeInTheDocument()
    expect(avatarElement).toHaveClass('is-shape-square')

    // Check for the Store icon (fallback icon)
    const iconElement = avatarElement?.querySelector('.c-Avatar__icon')
    expect(iconElement).toBeInTheDocument()
  })

  it('uses fallback icon and color when company has no logo in toggle avatar', () => {
    // Mock useGetCompany to return company data without logo
    ;(useGetCompany as jest.Mock).mockReturnValueOnce({
      data: {
        id: '789',
        name: 'Acme Inc',
        // No logoUrl
      },
      isLoading: false,
      error: null,
    })

    renderWithProviders(<AvatarToggle />, {
      initialState: {
        customer: {
          viewMode: 'customer',
          data: {
            id: 123,
            firstName: 'John',
            lastName: 'Doe',
            organizationId: 456,
            emptyCustomer: false,
          },
        },
      },
    })

    const toggleButton = screen.getByRole('button', {
      name: /toggle to company view/i,
    })
    expect(toggleButton).toBeInTheDocument()
    expect(toggleButton).toHaveClass('c-Avatar')
    expect(toggleButton).toHaveClass('is-shape-square')

    // Check for the Store icon (fallback icon) in toggle avatar
    const iconElement = toggleButton.querySelector('.c-Avatar__icon')
    expect(iconElement).toBeInTheDocument()
  })

  it('applies correct color value for fallback (not just color name)', () => {
    // Mock useGetCompany to return company data without logo
    ;(useGetCompany as jest.Mock).mockReturnValueOnce({
      data: {
        id: '1', // This should result in 'red' color name (1 % 6 = 1)
        name: 'Acme Inc',
        // No logoUrl
      },
      isLoading: false,
      error: null,
    })

    renderWithProviders(<AvatarToggle />, {
      initialState: {
        customer: {
          viewMode: 'company',
          data: {
            id: 123,
            firstName: 'John',
            lastName: 'Doe',
            organizationId: 456,
            emptyCustomer: false,
          },
        },
      },
    })

    const avatarElement = document.querySelector('.c-Avatar')
    expect(avatarElement).toBeInTheDocument()

    // Check that the fallback icon is present (indicating fallback logic is working)
    const iconElement = avatarElement?.querySelector('.c-Avatar__icon')
    expect(iconElement).toBeInTheDocument()

    // The avatar should be using square shape
    expect(avatarElement).toHaveClass('is-shape-square')
  })

  it('applies fallback color name as className for consistent icon colors', () => {
    // Mock useGetCompany to return company data without logo
    ;(useGetCompany as jest.Mock).mockReturnValueOnce({
      data: {
        id: '2', // This should result in 'blue' color name (2 % 6 = 2)
        name: 'Test Company',
        // No logoUrl
      },
      isLoading: false,
      error: null,
    })

    renderWithProviders(<AvatarToggle />, {
      initialState: {
        customer: {
          viewMode: 'company',
          data: {
            id: 123,
            firstName: 'John',
            lastName: 'Doe',
            organizationId: 456,
            emptyCustomer: false,
          },
        },
      },
    })

    const avatarElement = document.querySelector('.c-Avatar')
    expect(avatarElement).toBeInTheDocument()

    // The avatar should have the fallback color name as a class for icon styling
    expect(avatarElement).toHaveClass('blue')

    // Check that the fallback icon is present
    const iconElement = avatarElement?.querySelector('.c-Avatar__icon')
    expect(iconElement).toBeInTheDocument()
  })
})
