import PropTypes from 'prop-types'
import { useCallback, useMemo, useRef } from 'react'
import { useDispatch, useSelector } from 'react-redux'

import { getCustomerName } from './ProfilePanel.utils'

import { useCustomerSidebarContext } from '../../hooks/useCustomerSidebarContext'
import { useIsChannelsConversation } from 'apps/mailbox/screens/ConversationView/hooks/useIsChannelsConversation'
import { getConversationData } from 'apps/mailbox/state/conversation/reducer'
import { useHsAppContext, usePermission } from 'shared/hooks'
import { useFeatureFlag, usePersistedState } from 'shared/hooks'

import {
  EDIT_CONVERSATION_DETAILS,
  UPDATE_CUSTOMERS,
} from 'apps/mailbox/App/constants/permissions'

import {
  AdditionalInfoUI,
  ContactInfoUI,
  FixedHeaderUI,
  HeaderUI,
  ProfilePanelUI,
  UserNameButtonUI,
  UserNameUI,
  WrapperUI,
} from './ProfilePanel.css'

import { PanelActionMenu } from '../PanelActionMenu'
import {
  ChangeCustomerModal,
  EmailList,
  Notes,
  PersonalInfoList,
  PhoneList,
  WebsiteLink,
} from './components'
import { useProfilePanelScoutActions } from './useProfilePanelScoutActions'
import PropertiesSection from 'apps/mailbox/shared/components/CustomerSidebar/components/ProfilePanel/components/Properties/PropertiesSection/PropertiesSection'
import { ProfileModal } from 'apps/mailbox/shared/components/ProfileModal'
import {
  getIsChangeCustomerModalOpen,
  getIsProfileModalOpen,
  getIsProfilePanelActive,
} from 'apps/mailbox/state/profilePanel/selectors'
import {
  setIsChangeCustomerModalOpen,
  setIsPanelActive,
  setIsProfileModalOpen,
} from 'apps/mailbox/state/profilePanel/slice'
import Truncate from 'hsds/components/truncate'
import VisuallyHidden from 'hsds/components/visually-hidden'
import ComposeIcon from 'hsds/icons/compose'
import EyeClosedIcon from 'hsds/icons/eye-closed'
import EyeOpenIcon from 'hsds/icons/eye-open'
import PencilIcon from 'hsds/icons/pencil'
import UserChangeIcon from 'hsds/icons/user-change'

const ProfilePanel = ({ customer = {} }) => {
  const {
    appData: {
      shared: {
        mailbox: { id: mailboxId },
      },
    },
  } = useHsAppContext()

  const [arePropertiesVisible, setArePropertiesVisible] = usePersistedState(
    'sidebar.properties.visible',
    true
  )
  const canUpdateCustomers = usePermission(UPDATE_CUSTOMERS)
  const canEditConversationDetails = usePermission(EDIT_CONVERSATION_DETAILS)
  const isChannels = useIsChannelsConversation()
  const { enableChangeCustomer } = useCustomerSidebarContext()

  const isPropertiesPanelEnabled = useFeatureFlag('isPropertiesPanelEnabled')

  const conversation = useSelector(getConversationData)
  const hasConversation = Boolean(conversation.id)

  const includeChangeCustomer =
    enableChangeCustomer &&
    canEditConversationDetails &&
    hasConversation &&
    !isChannels

  const {
    emptyCustomer,
    emails = [],
    firstName,
    jobTitle,
    id: customerId,
    lastName,
    location = '',
    background: notes,
    organization = '',
    phones = [],
    photoUrl,
    properties = [],
    websites = [],
    //socialProfiles,
  } = customer

  const dispatch = useDispatch()

  const panelActionMenuRef = useRef(null)
  const returnFocusTo = useRef(null)

  const isPanelActive = useSelector(getIsProfilePanelActive)
  const isProfileModalOpen = useSelector(getIsProfileModalOpen)
  const isChangeCustomerModalOpen = useSelector(getIsChangeCustomerModalOpen)
  const hasProperties = properties?.length > 0
  const hasEmail = emails?.length > 0
  const canCreateNewConversation = Boolean(hasEmail && mailboxId && customerId)

  const panelMenuItems = useMemo(() => {
    return [
      {
        value: 'editProfile',
        label: `${canUpdateCustomers ? 'Edit' : 'View'} Profile`,
        className: 'profile-droplist-edit',
        icon: PencilIcon,
      },
      ...(canCreateNewConversation
        ? [
            {
              value: 'newConversation',
              label: 'New Conversation',
              className: 'profile-droplist-new-convo',
              icon: ComposeIcon,
            },
          ]
        : []),
      ...(includeChangeCustomer
        ? [
            {
              value: 'changeCustomer',
              label: 'Change Customer',
              icon: UserChangeIcon,
              className: 'profile-droplist-change',
            },
          ]
        : []),
      ...(isPropertiesPanelEnabled && hasProperties
        ? [
            {
              value: 'toggleProperties',
              label: `${arePropertiesVisible ? 'Hide' : 'Show'} Properties`,
              icon: arePropertiesVisible ? EyeClosedIcon : EyeOpenIcon,
              className: 'profile-droplist-properties',
            },
          ]
        : []),
    ]
  }, [
    canUpdateCustomers,
    includeChangeCustomer,
    isPropertiesPanelEnabled,
    arePropertiesVisible,
    hasProperties,
    canCreateNewConversation,
  ])

  const resetReturnFocusTo = () => {
    returnFocusTo.current?.focus()
    returnFocusTo.current = null
  }

  const openProfileModal = useCallback(
    e => {
      returnFocusTo.current = e?.target
      dispatch(setIsProfileModalOpen(true))
    },
    [dispatch]
  )
  const closeProfileModal = useCallback(() => {
    resetReturnFocusTo()
    dispatch(setIsProfileModalOpen(false))
  }, [dispatch])

  const openChangeCustomerModal = useCallback(
    e => {
      returnFocusTo.current = e?.target
      dispatch(setIsChangeCustomerModalOpen(true))
    },
    [dispatch]
  )

  const toggleProperties = useCallback(() => {
    // added a timeout to ensure the user doesn't see the label changing before the kebab menu closes
    setTimeout(() => {
      setArePropertiesVisible(prevState => !prevState)
    }, 300)
  }, [])

  const closeChangeCustomerModal = useCallback(() => {
    resetReturnFocusTo()
    dispatch(setIsChangeCustomerModalOpen(false))
  }, [dispatch])

  const handlePanelActive = useCallback(
    () => dispatch(setIsPanelActive(true)),
    [dispatch]
  )
  const handlePanelInActive = useCallback(
    () => dispatch(setIsPanelActive(false)),
    [dispatch]
  )

  const createNewConversation = useCallback(() => {
    window.location.href = `/mailbox/${mailboxId}/customer/${customerId}/${emails?.[0]?.id}`
  }, [customerId, mailboxId, emails])

  const handleOnSelectPanelMenuItem = useCallback(
    ({ value }) => {
      switch (value) {
        case 'editProfile':
          openProfileModal({ target: panelActionMenuRef.current })
          return
        case 'newConversation':
          createNewConversation()
          return
        case 'changeCustomer':
          openChangeCustomerModal({ target: panelActionMenuRef.current })
          return
        case 'toggleProperties':
          toggleProperties()
          return
        default:
          return
      }
    },
    [
      openProfileModal,
      openChangeCustomerModal,
      toggleProperties,
      createNewConversation,
    ]
  )

  useProfilePanelScoutActions({
    openProfileModal,
    includeChangeCustomer,
    openChangeCustomerModal,
  })

  const hasNotes = Boolean(notes)
  const isIdentified = Boolean(customerId)
  const name = getCustomerName({ firstName, lastName, emails })

  const hasCustomer = Boolean(emptyCustomer) === false

  const hasAdditionalInfo = Boolean(phones?.length || location || organization)

  return (
    <WrapperUI>
      <ProfilePanelUI
        className={hasNotes ? '' : 'without-notes'}
        onMouseEnter={handlePanelActive}
        onMouseLeave={handlePanelInActive}
        onFocusCapture={handlePanelActive}
        onBlurCapture={handlePanelInActive}
        isPropertiesPanelEnabled={isPropertiesPanelEnabled}
        onScroll={e => {
          e.currentTarget.setAttribute('data-scroll', e.currentTarget.scrollTop)
        }}
      >
        <FixedHeaderUI>
          <HeaderUI>
            {/* TODO: Update component before adding it back.           */}
            {/* See: https://helpscout.atlassian.net/browse/HSAPPUI-573 */}
            {/* <SocialProfileList socialProfiles={socialProfiles} />   */}

            <PanelActionMenu
              isPanelActive={isPanelActive}
              items={panelMenuItems}
              onSelect={handleOnSelectPanelMenuItem}
              a11yLabel="Customer Options"
              ref={panelActionMenuRef}
            />
          </HeaderUI>

          <UserNameUI>
            <UserNameButtonUI
              as={isIdentified ? undefined : 'div'}
              color="grey"
              linked
              inlined
              onClick={isIdentified ? openProfileModal : undefined}
              tabIndex="-1"
              data-cy="Sidebar.CustomerName"
            >
              <VisuallyHidden>Customer:</VisuallyHidden>
              <Truncate>{name}</Truncate>
            </UserNameButtonUI>
          </UserNameUI>

          {isIdentified && (
            <ContactInfoUI className={!hasAdditionalInfo ? 'has-margin' : ''}>
              <EmailList
                customerId={customerId}
                emails={emails}
                mailboxId={mailboxId}
                primaryId={
                  hasConversation
                    ? conversation.mostRecentCustomer?.entryId
                    : null
                }
                onClick={openProfileModal}
              />

              <WebsiteLink websites={websites} onClick={openProfileModal} />
            </ContactInfoUI>
          )}
        </FixedHeaderUI>

        {isIdentified && (
          <>
            {hasAdditionalInfo && (
              <AdditionalInfoUI>
                <PhoneList phones={phones} />

                <PersonalInfoList
                  jobTitle={jobTitle}
                  location={location}
                  organization={organization}
                  onOrganizationClick={openProfileModal}
                />
              </AdditionalInfoUI>
            )}

            <Notes
              notes={notes}
              isPropertiesPanelEnabled={
                isPropertiesPanelEnabled && hasProperties
              }
            />
            {isPropertiesPanelEnabled && arePropertiesVisible && (
              <PropertiesSection />
            )}
          </>
        )}

        <ProfileModal isOpen={isProfileModalOpen} onClose={closeProfileModal} />

        {isChangeCustomerModalOpen && includeChangeCustomer && (
          <ChangeCustomerModal
            isOpen={isChangeCustomerModalOpen}
            onClose={closeChangeCustomerModal}
            closeModal={closeChangeCustomerModal}
          />
        )}
      </ProfilePanelUI>
    </WrapperUI>
  )
}

ProfilePanel.propTypes = {
  customer: PropTypes.shape({
    emails: PropTypes.array,
    firstName: PropTypes.string,
    id: PropTypes.number,
    jobTitle: PropTypes.string,
    lastName: PropTypes.string,
    location: PropTypes.string,
    organization: PropTypes.string,
    notes: PropTypes.string,
    phones: PropTypes.array,
    photoUrl: PropTypes.string,
    emptyCustomer: PropTypes.bool,
  }),
}

export { ProfilePanel }
