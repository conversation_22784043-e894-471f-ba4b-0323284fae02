import styled from 'styled-components'

import { getColor } from 'hsds/utils/color'
import { focusRing } from 'hsds/utils/mixins'
import { COMPANY_DEFAULT_BRAND_COLOR_NAME } from 'shared/utils/companies/color'

import Avatar from 'hsds/components/avatar'

const avatarColorClasses = COMPANY_DEFAULT_BRAND_COLOR_NAME.map(
  color => `
    &.${color} {
      .c-Icon {
        color: ${getColor(`pastel.dark.${color}`)};
      }
    }
  `
).join('\n')

export const AvatarContainerUI = styled.div<{
  $shape: 'circle' | 'square'
}>`
  border: 4px solid white;
  border-radius: ${props => (props.$shape === 'square' ? '10px' : '50%')};
  left: 18px;
  line-height: 0;
  position: absolute;
  top: 18px;
  z-index: 2;
`

export const AvatarToggleContainerUI = styled.div`
  position: relative;
  display: inline-block;
`

export const ToggleAvatarUI = styled(Avatar)<{
  $shape: 'circle' | 'square'
}>`
  ${focusRing}
  --hsds-focus-ring-radius: ${props =>
    props.$shape === 'circle' ? '50%' : '10%'};
  --hsds-focus-ring-offset: 0px;
  position: absolute;
  bottom: 28px;
  right: -24px;
  width: 30px;
  height: 30px;
  border: 0px;
  background: white;
  padding: 2px;
  cursor: pointer;
  transition: box-shadow 0.2s ease;
  z-index: 10;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.15);

  &.is-shape-circle {
    border-radius: 50%;
  }

  &.is-shape-square {
    border-radius: 4px;
  }

  &:hover {
    box-shadow: 0 3px 6px rgba(0, 0, 0, 0.12);
  }

  &:active {
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.12);
  }

  ${avatarColorClasses}
`

export const AvatarUI = styled(Avatar)`
  ${avatarColorClasses}
`
