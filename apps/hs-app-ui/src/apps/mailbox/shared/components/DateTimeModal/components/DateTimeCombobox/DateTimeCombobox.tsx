import { RefObject, useEffect, useRef, useState } from 'react'

import { useNaturalLanguageResults } from './hooks/useNaturalLanguageResults'

import {
  DropdownTextUI,
  DropdownTogglerUI,
  InputUI,
  ResultsListUI,
} from './DateTimeCombobox.css'

import {
  ContextualDropdownOption,
  IntervalSuggestion,
  OnSelectRemoveCallback,
  OnSelectResult,
  RemoveOption,
} from '../../DateTimeModal.types'
import { Result } from '../../components/Result/Result'
import DropList from 'hsds/components/drop-list'
import Icon from 'hsds/components/icon'
import VisuallyHidden from 'hsds/components/visually-hidden'
import ChevronDown from 'hsds/icons/chevron-down-tiny'

export interface DateTimeComboboxProps {
  contextualDropdownOptions?: ContextualDropdownOption[]
  defaultSuggestions: IntervalSuggestion[]
  onItemSelect: OnSelectResult
  removeOption?: RemoveOption
  onRemove?: OnSelectRemoveCallback
  containerRef: RefObject<HTMLElement>
}

const DateTimeCombobox = ({
  contextualDropdownOptions,
  defaultSuggestions,
  removeOption,
  onItemSelect,
  onRemove,
  containerRef,
}: DateTimeComboboxProps) => {
  const inputRef = useRef<HTMLInputElement | null>(null)
  const [inputVal, setInputVal] = useState('')
  const [selectedContextualOption, setSelectedContextualOption] =
    useState<ContextualDropdownOption | null>(
      contextualDropdownOptions ? contextualDropdownOptions[0] : null
    )
  const [dropdownOpen, setDropdownOpen] = useState(false)

  const selectItem = (index: number) => {
    const result = results && results[index]
    if (!result) return

    if (result.type === 'remove' && onRemove) {
      onRemove()
    } else if (result.utcTimestamp) {
      onItemSelect(result.utcTimestamp.toISOString(), selectedContextualOption)
    }
  }

  const { results, ignoreKeyDown, setHighlightedIndex } =
    useNaturalLanguageResults({
      inputVal,
      defaultSuggestions,
      removeOption,
      selectItem,
    })

  useEffect(() => {
    setTimeout(() => {
      if (inputRef.current) {
        inputRef.current.focus()
      }
    }, 150)
  }, [])

  const onSelectContextualOption = (item: ContextualDropdownOption) => {
    setSelectedContextualOption(item)
  }

  const handleTogglerFocus = () => {
    ignoreKeyDown(true)
  }

  const handleTogglerBlur = () => {
    // We'll blur even if we're opening the menu - but we still want to continue
    // ignoring keydown events in that case
    if (!dropdownOpen) {
      ignoreKeyDown(false)
    }
  }

  const handleTogglerOpenedState = (val: boolean) => {
    setDropdownOpen(val)
    ignoreKeyDown(val)
  }

  const getContextualActions = () => {
    if (!contextualDropdownOptions || !selectedContextualOption) return null

    const hasResults = results.length > 0

    const togglerText = (
      <DropdownTextUI>
        <span>
          <VisuallyHidden>Currently selected: </VisuallyHidden>
          {selectedContextualOption.label}
        </span>
        <Icon
          icon={ChevronDown}
          inline
          size={24}
          aria-label="More options..."
        />
      </DropdownTextUI>
    )
    return (
      <DropList
        items={contextualDropdownOptions}
        selection={selectedContextualOption}
        onSelect={onSelectContextualOption}
        toggler={
          <DropdownTogglerUI
            disabled={!hasResults}
            text={togglerText}
            onFocus={handleTogglerFocus}
            onBlur={handleTogglerBlur}
            color="grey"
          />
        }
        onOpenedStateChange={handleTogglerOpenedState}
        tippyOptions={{
          appendTo: () => containerRef.current,
          placement: 'bottom-end',
        }}
      />
    )
  }

  return (
    <div>
      <InputUI
        placeholder="Try: 8 am, 3 days, Aug 7"
        action={getContextualActions()}
        role="combobox"
        aria-label="Natural language date/time input"
        aria-controls="datetime-listbox"
        aria-expanded={true}
        aria-hidden={false}
        value={inputVal}
        onChange={(value: string) => setInputVal(value)}
        inputRef={(ref: HTMLInputElement | HTMLTextAreaElement | null) => {
          inputRef.current = ref as HTMLInputElement
        }}
      />
      <ResultsListUI
        role="listbox"
        id="datetime-listbox"
        tabIndex={-1}
        aria-label="Date/time suggestions"
      >
        {results.map((result, index) => (
          <Result
            key={`result-${index}`}
            setHighlightedIndex={setHighlightedIndex}
            onClick={selectItem}
            {...result}
          />
        ))}
      </ResultsListUI>
    </div>
  )
}

export { DateTimeCombobox }
