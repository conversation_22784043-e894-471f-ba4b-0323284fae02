import { useCallback, useEffect, useRef, useState } from 'react'

import { getFilteredActions } from './utils/getFilteredActions'

import { useScoutActionsContext } from './hooks/useScoutContext'
import { usePrevious, useScopedHotkeys } from 'shared/hooks'

import {
  SCOPE_CONVERSATION,
  SCOPE_CONVERSATION_LIST,
} from 'apps/mailbox/App/constants/hotkeyScopes'
import { KEYBOARD_SHORTCUTS } from 'apps/mailbox/screens/ConversationView/constants/keyboardShortcuts'

import {
  InputUI,
  ScoutBackButtonUI,
  ScoutResultsContainerUI,
  ScoutResultsUI,
  SimpleModalUI,
} from './ScoutBarModal.css'

import { ActionId, ScoutAction } from './ScoutProvider'
import { Hint } from './components/Hint/Hint'
import { Result } from './components/Result/Result'
import Portal from 'hsds/components/portal'
import ArrowLeftSmall from 'hsds/icons/arrow-left'

interface ScoutModalProps {
  modalTitle?: string
}

interface ScopedHotkeysOptions {
  enabled: boolean
  scopes: string[]
  ignoredElementWhitelist?: ('INPUT' | 'TEXTAREA')[]
}

const getIdFromAction = (actionId: string) => {
  const pieces = actionId.split('.')
  return pieces[pieces.length - 1]
}

const ScoutBarModal = ({ modalTitle = 'Scout Bar' }: ScoutModalProps) => {
  const [isOpen, setIsOpen] = useState(false)
  const focusedRef = useRef<HTMLElement | null>(null)
  const [inputVal, setInputVal] = useState('')
  const [activeActionId, setActiveActionId] = useState<ActionId | null>(null)
  const [highlightedIndex, setHighlightedIndex] = useState(0)
  const prevHighlightedIndex = usePrevious(highlightedIndex) as number | null
  const inputRef = useRef<HTMLInputElement>()
  const modalWrapper = useRef<HTMLDivElement>(null)
  const containerRef = useRef<HTMLDivElement>(null)
  const listRef = useRef<HTMLDivElement>(null)
  const { actions } = useScoutActionsContext()

  const filteredActions: ScoutAction[] = getFilteredActions({
    inputVal,
    actions,
    activeActionId,
  })

  const doAppSearch = () => {
    const searchAutocomplete =
      window.__getSearchAutocomplete &&
      (window.__getSearchAutocomplete() as {
        open: () => void
        setQuery: (query: string, append?: boolean) => void
      })
    if (!searchAutocomplete) return
    searchAutocomplete.open && searchAutocomplete.open()
    searchAutocomplete.setQuery(inputVal, true)
  }

  if (inputVal) {
    filteredActions.push({
      actionId: 'app.search',
      label: `Search for ${inputVal}`,
      callback: doAppSearch,
    })
  }

  useScopedHotkeys(
    KEYBOARD_SHORTCUTS.COMMAND_PALETTE,
    (e: KeyboardEvent) => {
      focusedRef.current = document.activeElement as HTMLElement
      e.preventDefault()
      e.stopPropagation()
      setIsOpen(true)
    },
    {
      enabled: true,
      scopes: [SCOPE_CONVERSATION, SCOPE_CONVERSATION_LIST],
      ignoredElementWhitelist: ['INPUT', 'TEXTAREA'],
    } as ScopedHotkeysOptions
  )

  const handleCommandPaletteOpenEvent = useCallback(() => setIsOpen(true), [])

  useEffect(() => {
    document.addEventListener(
      'commandPalette.open',
      handleCommandPaletteOpenEvent
    )
    return () => {
      document.removeEventListener(
        'commandPalette.open',
        handleCommandPaletteOpenEvent
      )
    }
  }, [handleCommandPaletteOpenEvent])

  const setActionId = (actionId: ActionId | null) => {
    setActiveActionId(actionId)
    setInputVal('')
    setHighlightedIndex(0)
  }

  const clickAction = useCallback(
    (action: ScoutAction, keepOpen: boolean = false) => {
      const id = getIdFromAction(action.actionId)

      if (action.subActions) {
        setActionId(action.actionId)
        return
      }

      if (action.callback) {
        if (action.closeOnSelection !== false && !keepOpen) {
          handleOnClose()
        }

        // Delay calling callback just long enough to let modal disappear
        // so that actions list doesn't reset while we can still see it
        const callback = action.callback
        setTimeout(() => {
          callback(id)

          if (keepOpen) {
            setActionId(null)
          }
        }, 50)
      }
    },
    // eslint-disable-next-line react-hooks/exhaustive-deps
    []
  )

  const selectItem = useCallback(
    (index: number, keepOpen: boolean = false) => {
      const action: ScoutAction = filteredActions[index]
      if (!action) return

      clickAction(action, keepOpen)
    },
    [filteredActions, clickAction]
  )

  const scrollToItem = useCallback(
    (index: number, alignToTop: boolean) => {
      if (!listRef.current || !containerRef.current) return

      const item = listRef.current.children.item(index)
      if (!item) return

      const containerBounds = containerRef.current.getBoundingClientRect()
      const itemBounds = item.getBoundingClientRect()

      const isFullyVisible =
        itemBounds.top >= containerBounds.top &&
        itemBounds.bottom <= containerBounds.bottom

      if (!isFullyVisible) {
        item.scrollIntoView(alignToTop)
      }
    },
    [containerRef]
  )

  const resetSearch = useCallback(() => {
    setActiveActionId(null)
    setInputVal('')
    setHighlightedIndex(prevHighlightedIndex || 0)
  }, [prevHighlightedIndex])

  const handleKeyDown = useCallback(
    (e: KeyboardEvent) => {
      const keysToRespond = [
        'ArrowUp',
        'ArrowDown',
        'ArrowLeft',
        'ArrowRight',
        'Enter',
      ]

      if (!isOpen) return
      if (!keysToRespond.includes(e.key)) return

      e.preventDefault()
      e.stopPropagation()

      if (e.key === 'ArrowUp') {
        setHighlightedIndex(prev => {
          let newIdx
          if (prev === 0) {
            newIdx = filteredActions.length - 1
          } else {
            newIdx = prev - 1
          }

          scrollToItem(newIdx, true)
          return newIdx
        })
        return
      }

      if (e.key === 'ArrowDown') {
        setHighlightedIndex(prev => {
          let newIdx
          if (prev === filteredActions.length - 1) {
            newIdx = 0
          } else {
            newIdx = prev + 1
          }
          scrollToItem(newIdx, false)
          return newIdx
        })
        return
      }

      if (e.key === 'ArrowLeft' && activeActionId && !inputVal) {
        resetSearch()
        return
      }

      if (
        e.key === 'ArrowRight' &&
        filteredActions[highlightedIndex].subActions
      ) {
        setActionId(filteredActions[highlightedIndex].actionId)
        return
      }

      if (e.key === 'Enter') {
        selectItem(highlightedIndex, e.shiftKey)
        return
      }
    },
    [
      filteredActions,
      activeActionId,
      inputVal,
      highlightedIndex,
      selectItem,
      scrollToItem,
      resetSearch,
      isOpen,
    ]
  )

  useEffect(() => {
    document.addEventListener('keydown', handleKeyDown, { capture: true })
    return () => {
      document.removeEventListener('keydown', handleKeyDown, { capture: true })
    }
  }, [handleKeyDown])

  useEffect(() => {
    setTimeout(() => {
      if (inputRef.current && isOpen) {
        inputRef.current.focus()
      }
    }, 150)
  }, [isOpen, activeActionId])

  useEffect(() => {
    if (highlightedIndex > filteredActions.length - 1) {
      setHighlightedIndex(0)
    }
  }, [filteredActions, highlightedIndex])

  const handleOnClose = (
    e?: React.MouseEvent | React.KeyboardEvent | MouseEvent
  ) => {
    resetSearch()

    if (activeActionId && !(e instanceof PointerEvent)) {
      return
    }

    setIsOpen(false)
  }

  const getFilteredActionsList = () => {
    const actionsToReturn = filteredActions.map((action, idx) => (
      <Result
        key={action.actionId}
        result={action}
        setHighlightedIndex={setHighlightedIndex}
        index={idx}
        onSelect={clickAction}
        isHighlighted={idx === highlightedIndex}
      />
    ))

    return actionsToReturn
  }

  const getSearchPrefix = () => {
    if (!activeActionId) return null

    return (
      <ScoutBackButtonUI
        prefixIcon={ArrowLeftSmall}
        outlined
        color="grey"
        onClick={resetSearch}
        size="sm"
      >
        {actions[activeActionId].label}
      </ScoutBackButtonUI>
    )
  }

  return (
    <Portal renderTo="#mailbox">
      <div ref={modalWrapper}>
        <SimpleModalUI
          show={isOpen}
          onClose={handleOnClose}
          aria-label={modalTitle}
          closeOnClickOutside="modal"
          focusModalOnShow={false}
          withCloseButton={false}
        >
          <InputUI
            prefix={getSearchPrefix()}
            placeholder="What do you want to do?"
            role="combobox"
            aria-label="Command Palette"
            aria-controls="datetime-listbox"
            aria-expanded={true}
            aria-hidden={false}
            value={inputVal}
            onChange={(value: string) => setInputVal(value)}
            inputRef={ref => {
              inputRef.current = ref as HTMLInputElement
            }}
          />
          <ScoutResultsContainerUI ref={containerRef}>
            <ScoutResultsUI ref={listRef}>
              {getFilteredActionsList()}
            </ScoutResultsUI>
          </ScoutResultsContainerUI>
          <Hint />
        </SimpleModalUI>
      </div>
    </Portal>
  )
}

export { ScoutBarModal }
