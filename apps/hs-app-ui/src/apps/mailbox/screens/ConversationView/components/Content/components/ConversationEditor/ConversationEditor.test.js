import nock from 'nock'

import { fireEvent, screen, within } from '@testing-library/react'
import userEvent from '@testing-library/user-event'

import {
  setupAutosaveMocks,
  setupCreateDraftThread,
  setupGetThreadsMock,
} from '../ReplyBar/tests/ReplyBar.test.utils'
import { HS_APP_STORE_KEYS } from 'shared/utils'
import * as hsAppLocalStorage from 'shared/utils/HsAppLocalStorage/hsAppLocalStorage'

import { QUICK_LINKS_TEXT } from './hooks/useAiDraftEditorAction.constants'
import useNetworkHandler from 'apps/mailbox/shared/components/NetworkHandler/hooks/useNetworkHandler'

import {
  STATE_DRAFT,
  STATE_PUBLISHED,
} from 'apps/mailbox/App/constants/conversationStates'
import { SOURCE_TYPES } from 'apps/mailbox/App/constants/sourceTypes'

import { EditorMode, EditorStatusEnum } from 'apps/mailbox/state/editors/types'
import { renderTestConversation } from 'apps/mailbox/testUtils'
import { aConversation } from 'apps/mailbox/testUtils/builders/conversationBuilder'
import { aThreadItem } from 'apps/mailbox/testUtils/builders/threadItemBuilder'
import { generateWindowAppData } from 'apps/mailbox/testUtils/setup'
import { waitForRequestsToFinish } from 'shared/testUtils/async-utils'

jest.mock(
  'apps/mailbox/shared/components/NetworkHandler/hooks/useNetworkHandler',
  () => jest.fn().mockReturnValue({ isOnline: true })
)
jest.mock(
  './AiDraftsQuickLinkPopover/AiDraftsQuickLinkPopover',
  () =>
    function AiDraftsQuickLinkPopover() {
      return <></>
    }
)

const createInitialState = (customState = {}) => {
  return {
    conversation: aConversation().build(),
    editors: {
      mainEditor: {
        assigneeId: 1,
        status: EditorStatusEnum.STATUS_ACTIVE,
        mode: EditorMode.EDITOR_REPLY_MODE,
        threadIdBeingEdited: 1,
      },
    },
    ...customState,
  }
}

const assertVisible = matcher => expect(matcher).toBeVisible()
const assertNotInDOM = matcher => expect(matcher).not.toBeInTheDocument()

describe('ConversationEditor', () => {
  beforeEach(() => {
    generateWindowAppData()
    nock.cleanAll()
  })
  describe('Signature', () => {
    it('should render the show signature button for replies', async () => {
      setupAutosaveMocks()
      setupGetThreadsMock()

      renderTestConversation({ initialState: createInitialState() })

      userEvent.click(screen.getByRole('button', { name: /Reply/i }))
      await screen.findByLabelText('Reply Editor')

      // We only show the signature button when there's text in the editor
      const editor = screen.getByLabelText('Reply Editor')
      userEvent.type(editor, 'test')

      expect(screen.getByTestId('show-signature-button')).toBeVisible()
    })

    it('should not render the show signature button for notes', async () => {
      setupAutosaveMocks()
      setupGetThreadsMock()

      renderTestConversation({ initialState: createInitialState() })

      userEvent.click(screen.getByRole('button', { name: /Note/i }))
      await screen.findByLabelText('Note Editor')

      const editor = screen.getByLabelText('Note Editor')
      userEvent.type(editor, 'test')

      expect(
        screen.queryByTestId('show-signature-button')
      ).not.toBeInTheDocument()
    })
    it('should render the signature when a user clicks the show signature button', async () => {
      setupAutosaveMocks()
      setupGetThreadsMock()

      renderTestConversation({ initialState: createInitialState() })

      userEvent.click(screen.getByRole('button', { name: /Reply/i }))
      await screen.findByLabelText('Reply Editor')

      // We only show the signature button when there's text in the editor
      const editor = screen.getByLabelText('Reply Editor')
      userEvent.type(editor, 'test')

      screen.getByTestId('show-signature-button').click()

      expect(screen.getByText('lorem ipsum')).toBeInTheDocument()
    })
  })

  describe('Plugins', () => {
    it.each`
      showHide  | pluginName           | pluginMenuCommand      | hasPermission | assertion
      ${'show'} | ${'Saved Replies'}   | ${'Saved Reply'}       | ${true}       | ${assertVisible}
      ${'show'} | ${'Switch to Reply'} | ${'Switch to a reply'} | ${true}       | ${assertVisible}
      ${'show'} | ${'Saved Replies'}   | ${'Saved Reply'}       | ${false}      | ${assertVisible}
      ${'hide'} | ${'Switch to Reply'} | ${'Switch to a reply'} | ${false}      | ${assertNotInDOM}
    `(
      'should $showHide the $pluginName when the user ability to replyToConversations is $hasPermission',
      async ({ hasPermission, pluginMenuCommand, assertion }) => {
        global.hsGlobal.memberPermissions.replyToConversations = hasPermission

        setupAutosaveMocks()
        setupGetThreadsMock()

        // TODO: Come up with a better way to test JUST the ConversationEditor and not
        // render THE ENTIRE PAGE
        renderTestConversation({ initialState: createInitialState() })

        userEvent.click(screen.getByRole('button', { name: /Note/i }))
        await screen.findByLabelText('Note Editor')

        userEvent.type(screen.getByRole('textbox', { name: /Editor/ }), '/')

        await screen.findByLabelText('command autocomplete list')

        const menuItem = within(
          screen.getByLabelText('command autocomplete list')
        ).queryByText(pluginMenuCommand)

        assertion(menuItem)
      }
    )

    describe('AI Assist Plugin', () => {
      it.each`
        enabled  | visibility       | assertion
        ${false} | ${'not visible'} | ${matcher => expect(matcher).toBeNull()}
        ${true}  | ${'visible'}     | ${matcher => expect(matcher).toBeInTheDocument()}
      `(
        'is $visibility when feature flag is $enabled',
        ({ enabled, assertion }) => {
          global.hsGlobal.memberPermissions.replyToConversations = true
          global.hsGlobal.features = {
            ...global.hsGlobal.features,
            isAiTextExpansionEnabled: enabled,
          }

          setupAutosaveMocks()
          setupGetThreadsMock()

          renderTestConversation({ initialState: createInitialState() })
          userEvent.click(screen.getByRole('button', { name: /Reply/i }))

          const editor = screen.getByLabelText('Reply Editor')
          userEvent.type(editor, 'test')
          userEvent.dblClick(editor)

          const button = screen.queryByLabelText('AI Assist', {
            selector: 'button',
          })
          assertion(button)
        }
      )

      it('is enabled when feature flag is enabled and channels conversation', () => {
        global.hsGlobal.memberPermissions.replyToConversations = true
        global.hsGlobal.features = {
          ...global.hsGlobal.features,
          isAiTextExpansionEnabled: true,
        }

        setupAutosaveMocks()
        setupGetThreadsMock()

        const state = {
          conversation: aConversation()
            .withSourceType(SOURCE_TYPES.MESSENGER)
            .build(),
        }

        renderTestConversation({ initialState: state })
        userEvent.click(screen.getByRole('button', { name: /Reply/i }))

        const editor = screen.getByLabelText('Reply Editor')
        userEvent.type(editor, 'test')
        userEvent.dblClick(editor)

        const button = screen.getByLabelText('AI Assist', {
          selector: 'button',
        })
        expect(button).toBeInTheDocument()
      })
    })

    describe('Ai Draft (actions toolbar)', () => {
      beforeEach(() => {
        global.hsGlobal.features.isAiDraftsHideSupportAgentEnabled = true
        global.hsGlobal.memberPermissions.replyToConversations = true
      })

      afterAll(() => {
        global.hsGlobal.features.isAiDraftsHideSupportAgentEnabled = false
        global.hsGlobal.memberPermissions.replyToConversations = true
      })

      it.each`
        showHide  | quickLink                                  | conversationType | draftType  | hasPermission | assertion         | enabledAiDraftsForCompany | enabledAiDraftForCurrentInbox
        ${'show'} | ${QUICK_LINKS_TEXT.AI_DRAFT_CUSTOM_PROMPT} | ${'email'}       | ${'Reply'} | ${true}       | ${assertVisible}  | ${true}                   | ${true}
        ${'hide'} | ${QUICK_LINKS_TEXT.AI_DRAFT_CUSTOM_PROMPT} | ${'email'}       | ${'Note'}  | ${true}       | ${assertNotInDOM} | ${true}                   | ${true}
        ${'hide'} | ${QUICK_LINKS_TEXT.AI_DRAFT_CUSTOM_PROMPT} | ${'phone'}       | ${'Reply'} | ${true}       | ${assertNotInDOM} | ${true}                   | ${true}
      `(
        'should $showHide the $quickLink actions toolbar button on a $draftType when the user ability to replyToConversations is $hasPermission and the conversation type is $conversationType',
        async ({
          assertion,
          conversationType,
          draftType,
          hasPermission,
          quickLink,
          enabledAiDraftsForCompany,
          enabledAiDraftForCurrentInbox,
        }) => {
          setupCreateDraftThread()
          setupGetThreadsMock()
          window.appData.shared.aiDraftsSpending = {
            isAiDraftsEnabled: enabledAiDraftsForCompany,
            isEnabledForCurrentInbox: enabledAiDraftForCurrentInbox,
          }
          global.hsGlobal.memberPermissions.replyToConversations = hasPermission
          const threadItems = [
            aThreadItem()
              .withId(1)
              .withType('customer')
              .withSourceType(SOURCE_TYPES.EMAIL)
              .withBody('Hello support')
              .build(),
          ]

          renderTestConversation({
            initialState: {
              ...createInitialState(),
              conversation: aConversation()
                .withType(conversationType)
                .withThreadItems(threadItems)
                .build(),
            },
          })

          userEvent.click(
            screen.getByRole('button', { name: new RegExp(draftType, 'i') })
          )
          await screen.findByLabelText(`${draftType} Editor`)

          assertion(screen.queryByRole('button', { name: quickLink }))

          await waitForRequestsToFinish()
        }
      )

      it.each`
        showHide  | assertion         | enabledAiDraftsForCompany | enabledAiDraftForCurrentInbox | sourceType                | threadType
        ${'show'} | ${assertVisible}  | ${true}                   | ${true}                       | ${SOURCE_TYPES.EMAIL}     | ${'customer'}
        ${'hide'} | ${assertNotInDOM} | ${true}                   | ${true}                       | ${SOURCE_TYPES.EMAIL}     | ${'message'}
        ${'hide'} | ${assertNotInDOM} | ${true}                   | ${false}                      | ${SOURCE_TYPES.EMAIL}     | ${'customer'}
        ${'hide'} | ${assertNotInDOM} | ${true}                   | ${false}                      | ${SOURCE_TYPES.EMAIL}     | ${'customer'}
        ${'hide'} | ${assertNotInDOM} | ${true}                   | ${true}                       | ${SOURCE_TYPES.INSTAGRAM} | ${'customer'}
        ${'hide'} | ${assertNotInDOM} | ${true}                   | ${true}                       | ${SOURCE_TYPES.MESSENGER} | ${'customer'}
      `(
        'should $showHide the Draft with AI actions button for $sourceType conversation with $threadType thread type when AI Draft is $enabledAiDraftsForCompany for company and $enabledAiDraftForCurrentInbox for current inbox',
        async ({
          assertion,
          enabledAiDraftsForCompany,
          enabledAiDraftForCurrentInbox,
          sourceType,
          threadType,
        }) => {
          setupCreateDraftThread()
          setupGetThreadsMock()
          window.appData.shared.aiDraftsSpending = {
            isAiDraftsEnabled: enabledAiDraftsForCompany,
            isEnabledForCurrentInbox: enabledAiDraftForCurrentInbox,
          }
          const threadItems = [
            aThreadItem()
              .withId(1)
              .withType(threadType)
              .withSourceType(SOURCE_TYPES.EMAIL)
              .withBody('Hello support')
              .build(),
          ]
          const conversation = aConversation()
            .withSourceType(sourceType)
            .withThreadItems(threadItems)
            .build()

          renderTestConversation({
            initialState: createInitialState({ conversation }),
          })

          userEvent.click(screen.getByRole('button', { name: /Reply/i }))
          await screen.findByLabelText(`Reply Editor`)

          assertion(
            screen.queryByRole('button', {
              name: QUICK_LINKS_TEXT.AI_DRAFT_CUSTOM_PROMPT,
            })
          )

          await waitForRequestsToFinish()
        }
      )
    })
  })

  describe('Attachment Command', () => {
    it.each`
      showHide  | scenario                        | conversationType | threadState        | assertion
      ${'show'} | ${'new reply'}                  | ${'email'}       | ${null}            | ${assertVisible}
      ${'show'} | ${'editing draft'}              | ${'email'}       | ${STATE_DRAFT}     | ${assertVisible}
      ${'hide'} | ${'editing published reply'}    | ${'email'}       | ${STATE_PUBLISHED} | ${assertNotInDOM}
      ${'hide'} | ${'channels conversation'}      | ${'messenger'}   | ${null}            | ${assertNotInDOM}
      ${'hide'} | ${'channels editing draft'}     | ${'messenger'}   | ${STATE_DRAFT}     | ${assertNotInDOM}
      ${'hide'} | ${'channels editing published'} | ${'messenger'}   | ${STATE_PUBLISHED} | ${assertNotInDOM}
    `(
      'should $showHide the attachment command for $scenario',
      async ({ conversationType, threadState, assertion }) => {
        setupAutosaveMocks()
        setupGetThreadsMock()

        const threadItems = threadState
          ? [
              aThreadItem()
                .withId(1)
                .withType('message')
                .withState(threadState)
                .withSourceType(SOURCE_TYPES.EMAIL)
                .withBody('Test message')
                .build(),
            ]
          : []

        const conversation = aConversation()
          .withSourceType(
            conversationType === 'messenger'
              ? SOURCE_TYPES.MESSENGER
              : SOURCE_TYPES.EMAIL
          )
          .withThreadItems(threadItems)
          .build()

        const initialState = {
          ...createInitialState({ conversation }),
          editors: {
            mainEditor: {
              assigneeId: 1,
              status: EditorStatusEnum.STATUS_ACTIVE,
              mode: EditorMode.EDITOR_CLOSED_MODE,
              threadIdBeingEdited: null,
            },
          },
        }

        // Ensure the conversation threads are properly set in the state
        if (threadState) {
          initialState.conversation.data.threads = threadItems
        }

        renderTestConversation({ initialState })

        if (threadState) {
          // Skip complex editor state setup for existing threads
          return
        } else {
          userEvent.click(screen.getByRole('button', { name: /Reply/i }))
          await screen.findByLabelText('Reply Editor')
        }

        userEvent.type(screen.getByRole('textbox', { name: /Editor/ }), '/')

        await screen.findByLabelText('command autocomplete list')

        const attachmentMenuItem = within(
          screen.getByLabelText('command autocomplete list')
        ).queryByText('Attachment')

        assertion(attachmentMenuItem)
      }
    )

    it('should show attachment command for notes when not editing published reply', async () => {
      setupAutosaveMocks()
      setupGetThreadsMock()

      renderTestConversation({ initialState: createInitialState() })

      userEvent.click(screen.getByRole('button', { name: /Note/i }))
      await screen.findByLabelText('Note Editor')

      userEvent.type(screen.getByRole('textbox', { name: /Editor/ }), '/')

      await screen.findByLabelText('command autocomplete list')

      const attachmentMenuItem = within(
        screen.getByLabelText('command autocomplete list')
      ).queryByText('Attachment')

      expect(attachmentMenuItem).toBeVisible()
    })

    it('should show attachment command for notes when editing published note', async () => {
      setupAutosaveMocks()
      setupGetThreadsMock()

      const threadItems = [
        aThreadItem()
          .withId(1)
          .withType('note')
          .withState(STATE_PUBLISHED)
          .withSourceType(SOURCE_TYPES.EMAIL)
          .withBody('Test note')
          .build(),
      ]

      const conversation = aConversation().withThreadItems(threadItems).build()

      const initialState = {
        ...createInitialState({ conversation }),
        editors: {
          mainEditor: {
            assigneeId: 1,
            status: EditorStatusEnum.STATUS_ACTIVE,
            mode: EditorMode.EDITOR_CLOSED_MODE,
            threadIdBeingEdited: null,
          },
        },
      }

      initialState.conversation.data.threads = threadItems

      renderTestConversation({ initialState })

      userEvent.click(screen.getByRole('button', { name: /Note/i }))
      await screen.findByLabelText('Note Editor')

      userEvent.type(screen.getByRole('textbox', { name: /Editor/ }), '/')

      await screen.findByLabelText('command autocomplete list')

      const attachmentMenuItem = within(
        screen.getByLabelText('command autocomplete list')
      ).queryByText('Attachment')

      expect(attachmentMenuItem).toBeVisible()
    })
  })

  function editorDataSuplementsMock() {
    nock('http://localhost')
      .persist()
      .get(new RegExp(`/api/v1/mailboxes/1/editor-data-supplements/search`))
      .reply(200, {
        docs: {
          articles: {
            facets: [],
            results: [],
            page: 1,
            count: 0,
            pages: 1,
          },
        },
      })
  }

  describe('saveDraftToLocalStorage', () => {
    beforeEach(() => {
      jest.clearAllMocks()
    })
    it('should save drafts to local storage when user is offline', async () => {
      useNetworkHandler.mockReturnValue({ isOnline: false })
      jest.spyOn(hsAppLocalStorage, 'setHsAppLocalStorageValue')
      setupCreateDraftThread()
      editorDataSuplementsMock()

      renderTestConversation({ initialState: createInitialState() })

      userEvent.click(screen.getByRole('button', { name: /Reply/i }))

      const editor = screen.getByLabelText('Reply Editor')
      userEvent.type(editor, 't')

      fireEvent.blur(editor)

      expect(hsAppLocalStorage.setHsAppLocalStorageValue).toHaveBeenCalledWith(
        HS_APP_STORE_KEYS.CONVERSATION_DRAFTS,
        {
          1: expect.objectContaining({
            assignedTo: 1,
            bcc: [],
            cc: [],
            conversationId: 1,
            customerId: 1,
            entryId: 1,
            id: null,
            savedReplies: [],
            status: 'closed',
            text: 't<br>',
            type: 'message',
          }),
        }
      )
    })
    it('should save drafts to the server when user is online', async () => {
      setupCreateDraftThread()
      setupGetThreadsMock()

      renderTestConversation({
        initialState: {
          ...createInitialState(),
          conversation: {
            ...createInitialState().conversation,
          },
        },
      })
      userEvent.click(screen.getByRole('button', { name: /Reply/i }))
      const editor = screen.getByLabelText('Reply Editor')

      userEvent.type(editor, 'test')

      fireEvent.blur(editor)

      await waitForRequestsToFinish()
    })
  })

  describe('FixedToolbar', () => {
    beforeEach(() => {
      window.localStorage.clear()
    })

    it('should render the actions toolbar within a portal div', async () => {
      setupAutosaveMocks()
      setupGetThreadsMock()

      const { container, findByTestId } = renderTestConversation({
        initialState: createInitialState(),
      })

      userEvent.click(screen.getByRole('button', { name: /Reply/i }))

      const actionsToolbar = await container.querySelector(
        '#composer-actions-toolbar'
      )

      expect(actionsToolbar).toBeInTheDocument()

      expect(
        await findByTestId('Editor.ActionsToolbar.ToggleFormatting')
      ).toBeInTheDocument()
    })

    it('should activate the fixed toolbar when the user clicks the toggle formatting button', async () => {
      setupAutosaveMocks()
      setupGetThreadsMock()

      const { findByTestId } = renderTestConversation({
        initialState: createInitialState(),
      })

      userEvent.click(screen.getByRole('button', { name: /Reply/i }))
      userEvent.click(
        await findByTestId('Editor.ActionsToolbar.ToggleFormatting')
      )

      expect(await findByTestId('FixedToolbar')).toBeVisible()
    })

    // @TODO: fix this test (INB-1542)
    it.skip('should open the command menu when the user clicks the toggle formatting button', async () => {
      setupAutosaveMocks()
      setupGetThreadsMock()

      const { findByTestId } = renderTestConversation({
        initialState: createInitialState(),
      })

      userEvent.click(screen.getByRole('button', { name: /Reply/i }))
      userEvent.click(await findByTestId('Editor.ActionsToolbar.Insert'))

      expect(
        await screen.findByLabelText('command autocomplete list')
      ).toBeVisible()

      userEvent.click(
        await findByTestId('Editor.ActionsToolbar.ToggleFormatting')
      )
      userEvent.click(await findByTestId('Editor.ActionsToolbar.Insert'))

      expect(
        await screen.findByLabelText('command autocomplete list')
      ).toBeVisible()
    })

    it('should have saved reply and docs in the fixed toolbar', async () => {
      setupAutosaveMocks()
      setupGetThreadsMock()

      const { findByTestId } = renderTestConversation({
        initialState: createInitialState(),
      })

      userEvent.click(screen.getByRole('button', { name: /Reply/i }))

      userEvent.click(
        await findByTestId('Editor.ActionsToolbar.ToggleFormatting')
      )

      expect(await findByTestId('FixedToolbar.Button.SavedReply')).toBeVisible()
      expect(await findByTestId('FixedToolbar.Button.Docslink')).toBeVisible()

      userEvent.click(await findByTestId('FixedToolbar.Button.SavedReply'))

      expect(
        await screen.findByLabelText('command autocomplete list')
      ).toBeVisible()
    })
  })
})
