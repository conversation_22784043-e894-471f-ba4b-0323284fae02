import cx from 'classnames'
import { HsEditorPluginProvider } from 'editor'
import PropTypes from 'prop-types'
import {
  forwardRef,
  memo,
  useCallback,
  useEffect,
  useMemo,
  useRef,
  useState,
} from 'react'
import { useDispatch, useSelector } from 'react-redux'

import { isAiAnswersSource } from 'apps/mailbox/aiAnswers/aiAnswers.utils'
import { isChannelsSource } from 'apps/mailbox/channels/channels.utils'
import { mergeRefs } from 'hsds/utils/react'

import useCreateAttachment from '../ReplyBar/hooks/useCreateAttachment'
import { useConversationEditorFocus } from './hooks/useConversationEditorFocus'
import { useEditorKeyboardShortcuts } from './hooks/useEditorKeyboardShortcuts'
import { useHandleHsAppEscape } from './hooks/useHandleHsAppEscape'
import { useAiDraftEditorAction } from 'apps/mailbox/screens/ConversationView/components/Content/components/ConversationEditor/hooks/useAiDraftEditorAction'
import { useCommonEditorConfig } from 'apps/mailbox/screens/ConversationView/components/Content/components/ConversationEditor/hooks/useCommonEditorConfig'
import { useDraftWithAiCommand } from 'apps/mailbox/screens/ConversationView/components/Content/components/ConversationEditor/hooks/useDraftWithAiCommand'
import useIsNewConversation from 'apps/mailbox/screens/NewConversationView/hooks/useIsNewConversation'
import useNetworkHandler from 'apps/mailbox/shared/components/NetworkHandler/hooks/useNetworkHandler'
import {
  getSignature,
  getThreadBeingEdited,
} from 'apps/mailbox/state/conversation/reducer'
import {
  useFeatureFlag,
  useHsAppContext,
  usePermission,
  usePrevious,
} from 'shared/hooks'

import { STATE_PUBLISHED } from 'apps/mailbox/App/constants/conversationStates'
import { SOURCE_TYPES } from 'apps/mailbox/App/constants/sourceTypes'
import { TEAM_TYPE } from 'apps/mailbox/App/constants/userTypes'

import { ConvoEditorOverlayUI, ConvoEditorUI } from './ConversationEditor.css'

import Signature from './Signature'
import { addAnAttachment, createSavedReply } from './editorCommands.util'
import { usePrimaryEditorContext } from './usePrimaryEditor'
import { SaveThisReplyModal } from 'apps/mailbox/shared/components/EditorPlugins/SavedReplies/components'
import useSavedRepliesCommands from 'apps/mailbox/shared/components/EditorPlugins/SavedReplies/useSavedRepliesCommands'
import { setUsedSavedReplies } from 'apps/mailbox/state/editors/mainEditor/mainEditor.slice'
import AiDraftCursor from 'editor/plugins/AiDraftCursor'
import HtmlBlock from 'editor/plugins/HtmlBlock'
import Mention, {
  createMention,
  createTeamMention,
} from 'editor/plugins/Mention'
import Table from 'editor/plugins/Table'
import { useTheme } from 'hsds/components/hsds'

const PLACEHOLDER = 'Type / for more options'

const getEditorLabel = ({ isNote, isForward }) => {
  if (isNote) return 'Note'
  if (isForward) return 'Forward'
  return 'Reply'
}

const EMPTY_USERS = []

const MESSENGER_MAX_THREAD_LENGTH = 2000
const INSTAGRAM_MAX_THREAD_LENGTH = 1000

function ConvoEditorWrapper(props) {
  const { editorRef, children, ...rest } = props

  return (
    <ConvoEditorUI {...rest} ref={editorRef}>
      {children}
    </ConvoEditorUI>
  )
}

ConvoEditorWrapper.propTypes = {
  editorRef: PropTypes.any,
  children: PropTypes.node,
}

const ConvoEditorMemo = memo(ConvoEditorWrapper)

const ConversationEditor = forwardRef(function ConversationEditor(props, ref) {
  const {
    children,
    isNote,
    source,
    isForward,
    onSave,
    disabled,
    extraCommands = [],
    initialValue,
    className,
    controlsButtonsRef,
    openRecipientsEdit,
    showSignature = false,
    onCancelKeydown,
    blockInput = false,
    onClickBlock,
    focusTargetRef,
    actionsToolbarSelector,
    ...rest
  } = props
  const editorRef = useRef()
  const overlayRef = useRef()

  const { name: themeName } = useTheme()
  const dispatch = useDispatch()
  const {
    appData: {
      shared: { mailboxUsers = EMPTY_USERS, member: currentUser = {} } = {},
      conversationView: { avatarBaseUrl, mailboxUsersWhoCanBeMentioned } = {},
    },
  } = useHsAppContext()

  const {
    editorDataSupplements: { createDocsInsertCommand },
  } = usePrimaryEditorContext()

  const signature = useSelector(getSignature)

  const isNewConvo = useIsNewConversation()

  const isChannels = isChannelsSource(source)
  const isChannelsView = isChannels || isAiAnswersSource(source)

  const { isOnline } = useNetworkHandler()

  const isHtmlBlockActive = useFeatureFlag('isHtmlBlockComposer') && !isChannels

  const isAiDraftsHideSupportAgentEnabled = useFeatureFlag(
    'isAiDraftsHideSupportAgentEnabled'
  )

  const aiDraftEditorAction = useAiDraftEditorAction()

  const fallbackAvatarUrl = `${avatarBaseUrl}01.png`

  const recordSavedReplyUsage = useCallback(
    savedReplyId => {
      dispatch(setUsedSavedReplies(savedReplyId))
    },
    [dispatch]
  )
  const savedReplyCommands = useSavedRepliesCommands(
    recordSavedReplyUsage,
    isChannels
  )

  // Mentions
  const mentionItems = useMemo(() => {
    const filteredMailboxUsers = Array.isArray(mailboxUsersWhoCanBeMentioned)
      ? mailboxUsers.filter(
          user =>
            mailboxUsersWhoCanBeMentioned.includes(user.id) ||
            user.id === currentUser.id
        )
      : mailboxUsers

    return filteredMailboxUsers.map(user => {
      const { id, fullName, photoUrl, mentionName, type } = user
      const isTeam = parseInt(type) === TEAM_TYPE
      const userCommand = isTeam
        ? createTeamMention(
            id,
            fullName,
            mentionName,
            'Team',
            photoUrl,
            photoUrl ? fallbackAvatarUrl : null
          )
        : createMention(id, fullName, mentionName, photoUrl, fallbackAvatarUrl)

      return userCommand
    })
  }, [
    currentUser,
    mailboxUsers,
    fallbackAvatarUrl,
    mailboxUsersWhoCanBeMentioned,
  ])

  const mentions = useMemo(() => {
    return {
      items: mentionItems,
    }
  }, [mentionItems])

  const extraPlugins = useMemo(() => {
    const plugins = [Mention, Table]
    if (isHtmlBlockActive) {
      plugins.push(HtmlBlock)
    }
    if (isAiDraftsHideSupportAgentEnabled) {
      plugins.push(AiDraftCursor)
    }
    return plugins
  }, [isHtmlBlockActive, isAiDraftsHideSupportAgentEnabled])

  // Saved Replies
  const canReplyToConversations = usePermission('replyToConversations')

  const [displaySaveThisReplyModal, setDisplaySaveThisReplyModal] =
    useState(false)
  const [draftForSavedReply, setDraftForSavedReply] = useState(null)
  const closeSavedReplyModal = () => {
    setDisplaySaveThisReplyModal(false)
    setDraftForSavedReply(null)
    editorRef.current.focus()
  }

  // Attachments
  const attachmentInputRef = useRef()
  const { handleAttachmentCreation, handleInlineAttachmentCreation } =
    useCreateAttachment()

  const { attachments, state: threadState } = useSelector(getThreadBeingEdited)

  // Clear file input so that the user can attach the same file again after deleting it
  // (otherwise the input onChange event is not triggered)
  useEffect(() => {
    if (
      attachmentInputRef.current &&
      // Only clear if filename is not in the list of attachments (otherwise this prevents the user
      // from uploading multiple attachments at once)
      !attachments?.some(({ filename }) =>
        attachmentInputRef.current.value.includes(filename)
      )
    ) {
      attachmentInputRef.current.value = ''
    }
  }, [attachments])

  const draftWithAiCommand = useDraftWithAiCommand()

  // Editor commands and config
  const commands = useMemo(() => {
    // * This list of shared commands are commands used by both the main editor instance in the
    // * Reply Bar as well as the Inline Conversation Editor found in a thread

    // Determine if we should hide the attachment command
    // Hide for channels or when editing published/sent replies (threadBeingEdited.state === 'published')
    // Show in all other cases (new replies, editing drafts, etc.)
    const shouldHideAttachmentCommand =
      isChannels || threadState === STATE_PUBLISHED

    const sharedCommands = [
      ...(shouldHideAttachmentCommand
        ? []
        : [addAnAttachment(attachmentInputRef)]),
      createDocsInsertCommand(isNote),
    ]

    if (canReplyToConversations || isNote) {
      sharedCommands.push(...savedReplyCommands)
      sharedCommands.push(
        createSavedReply(editor => {
          setDraftForSavedReply(editor.serialize(editor.children))
          setDisplaySaveThisReplyModal(true)
        })
      )
    }

    if (draftWithAiCommand && !isNote && canReplyToConversations) {
      sharedCommands.push(draftWithAiCommand)
    }

    return [...sharedCommands, ...extraCommands]
  }, [
    isChannels,
    createDocsInsertCommand,
    isNote,
    canReplyToConversations,
    extraCommands,
    savedReplyCommands,
    draftWithAiCommand,
    threadState,
  ])

  const commonEditorConfig = useCommonEditorConfig(
    handleInlineAttachmentCreation,
    {
      isNote,
      isNewConvo,
    }
  )

  const editorConfig = useMemo(() => {
    return {
      ...commonEditorConfig,
      Mention: {
        displayTrigger: true,
        isActive: isNote,
      },
      Table: {
        allowCommand: false,
        inlineStyles: true,
      },
      Command: {
        showInsert: false,
      },
      actionsToolbar: {
        selector: actionsToolbarSelector,
      },
      ContextualToolbar: {
        isFixedToolbarSticky: true,
      },
      Divider: false,
    }
  }, [commonEditorConfig, isNote, actionsToolbarSelector])

  const commandContextValue = useMemo(() => {
    return {
      items: commands,
    }
  }, [commands])

  // This is not a heavy calculation but it's memoized because
  // in Jest-land, the isNote and isDraft values were sometimes initially undefined
  // and that caused an issue with the test-id not getting set as it needed to for the test to work
  const testId = useMemo(() => {
    return `${getEditorLabel({ isNote, isForward }).toLowerCase()}-editor`
  }, [isNote, isForward])

  const onStopTyping = useCallback(editor => {
    editor.save({ shouldResetOriginalValue: false })
  }, [])

  // We want to save the editor state right when the user starts typing,
  // but only the *first time* they start typing
  const hasSavedOnFirstTyping = useRef(false)
  const onStartTyping = useCallback(editor => {
    if (hasSavedOnFirstTyping.current) return

    editor.save({ shouldResetOriginalValue: false })
    hasSavedOnFirstTyping.current = true
  }, [])

  const maxLength =
    source === SOURCE_TYPES.MESSENGER
      ? MESSENGER_MAX_THREAD_LENGTH
      : source === SOURCE_TYPES.INSTAGRAM
      ? INSTAGRAM_MAX_THREAD_LENGTH
      : undefined

  const handleEditorKeyDown = useEditorKeyboardShortcuts({
    attachmentInputRef,
    controlsButtonsRef,
    onSave,
    editorRef,
    openRecipientsEdit,
    onCancelKeydown,
  })

  const { onAutocompleteOpen, onAutocompleteClose } = useHandleHsAppEscape()

  const shouldShowSignature = showSignature && !!signature

  useConversationEditorFocus({
    editorRef,
    focusTargetRef,
  })

  // Handles blocking the editor UI, used when recipients is opened
  const previousBlockInput = usePrevious(blockInput)
  useEffect(() => {
    if (!previousBlockInput || previousBlockInput === blockInput) return

    if (!blockInput) {
      editorRef.current.focus()
    } else {
      // Because the overlay is a direct child of the scrollable container, we can't absolutely
      // position it to cover the entire scrollable contents (it'd take the size of the container, not the contents)
      // So we'll need to manually set the height here, based on the scrollHeight
      overlayRef.current.style.height = `${editorRef.current.editorRef.scrollHeight}px`
    }
  }, [blockInput, previousBlockInput])

  const handleEditableBlur = useCallback(e => {
    // We only want to refocus the editor if the blur happened because we clicked on the immediate editor wrapper
    if (e.relatedTarget && e.relatedTarget == editorRef.current.editorRef) {
      e.preventDefault()
      editorRef.current.focus()
    }
  }, [])

  useEffect(() => {
    if (!editorRef.current.editorRef) return

    const currentEditorRef = editorRef.current.editorRef
    const currentEditableRef =
      currentEditorRef.querySelector('[role="textbox"]')

    // In tests, this won't exist, so we can skip
    if (!currentEditableRef) return

    // Due to the way we structure elements to enable signature/attachments to scroll with the editor content,
    // the scrollable element *isn't* the editor. But when we use the mouse to scroll this element, the editor
    // loses focus. This trick will keep the editor focus when the container is scrolled.
    currentEditorRef.setAttribute('tabindex', '-1')
    currentEditableRef.addEventListener('blur', handleEditableBlur)

    return () => {
      currentEditableRef.removeEventListener('blur', handleEditableBlur)
    }
  }, [handleEditableBlur])

  return (
    <>
      {displaySaveThisReplyModal && (
        <SaveThisReplyModal
          closeSavedReplyModal={closeSavedReplyModal}
          initialBody={draftForSavedReply}
        />
      )}
      <HsEditorPluginProvider
        command={commandContextValue}
        mention={mentions}
        actionsToolbar={aiDraftEditorAction ? [aiDraftEditorAction] : null}
      >
        <ConvoEditorMemo
          className={cx(
            className,
            isForward && 'is-forward',
            isNewConvo && 'is-new-convo',
            isChannelsView && 'is-channels',
            shouldShowSignature && 'show-signature',
            blockInput && 'is-blocked',
            'has-custom-prompting',
            'focus-target'
          )}
          config={editorConfig}
          autoFocusDelay={50}
          disabled={disabled}
          isNote={isNote}
          isForward={isForward}
          onSave={onSave}
          initialValue={initialValue}
          data-testid={testId}
          aria-label={`${getEditorLabel({ isNote, isForward })} Editor`}
          extraPlugins={extraPlugins}
          multiline={0}
          maxHeight={false}
          {...rest}
          editorRef={mergeRefs(editorRef, ref)}
          onStopTyping={onStopTyping}
          onStartTyping={onStartTyping}
          withTypingEvent
          typingTimeoutDelay={isOnline ? 750 : 500}
          maxLength={maxLength}
          withCharValidator={!!isChannelsView}
          handleAttachmentCreation={handleAttachmentCreation}
          isChannels={isChannels}
          onKeyDown={handleEditorKeyDown}
          onAutocompleteOpen={onAutocompleteOpen}
          onAutocompleteClose={onAutocompleteClose}
          placeholder={PLACEHOLDER}
          shouldShowSignature={shouldShowSignature}
          signature={signature || ''}
          themeName={themeName}
          withBackdrop={false}
          debug
        >
          {signature && shouldShowSignature && (
            <Signature
              content={signature}
              editorRef={editorRef}
              onClick={() => editorRef.current.focus()}
            />
          )}
          {blockInput && (
            <ConvoEditorOverlayUI
              ref={overlayRef}
              onClick={onClickBlock}
              data-testid="EditorOverlay"
            />
          )}
          {children}
        </ConvoEditorMemo>

        <input
          type="file"
          ref={attachmentInputRef}
          onChange={() =>
            handleAttachmentCreation(attachmentInputRef.current.files)
          }
          multiple
          hidden
        />
      </HsEditorPluginProvider>
    </>
  )
})

ConversationEditor.propTypes = {
  children: PropTypes.node,
  isNote: PropTypes.bool,
  source: PropTypes.string,
  isForward: PropTypes.bool,
  onChange: PropTypes.func,
  onSave: PropTypes.func.isRequired,
  disabled: PropTypes.bool,
  extraCommands: PropTypes.arrayOf(PropTypes.object),
  initialValue: PropTypes.string,
  className: PropTypes.string,
  controlsButtonsRef: PropTypes.object,
  openRecipientsEdit: PropTypes.func,
  showSignature: PropTypes.bool,
  onCancelKeydown: PropTypes.func,
  blockInput: PropTypes.bool,
  onClickBlock: PropTypes.func,
  focusTargetRef: PropTypes.object,
  actionsToolbarSelector: PropTypes.string,
}

export default memo(ConversationEditor)
