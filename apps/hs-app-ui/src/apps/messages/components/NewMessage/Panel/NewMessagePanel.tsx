import classNames from 'classnames'
import React, { use<PERSON><PERSON>back, useEffect, useMemo, useState } from 'react'

import { useIsMounted } from 'shared/hooks/useIsMounted'

import {
  ButtonUI,
  CheckMarkCardGridUI,
  CheckMarkCardUI,
  ContentUI,
  HeadingUI,
  IconWrapUI,
  NewMessageSidePanelContainerUI,
  NewMessageSidePanelUI,
  SubHeadingUI,
  TypesFormGroupUI,
} from './NewMessagePanel.css'

import NewMessagePreview from './NewMessagePreview'
import {
  preLoadImage,
  useImagePath,
} from 'apps/messages/common/images/imageUtils'
import FormGroup from 'apps/messages/components/FormGroup'
import { BannerIcon } from 'apps/messages/components/Icons/MessageType/BannerIcon'
import { MicrosurveyIcon } from 'apps/messages/components/Icons/MessageType/MicrosurveyIcon'
import { ModalIcon } from 'apps/messages/components/Icons/MessageType/ModalIcon'
import { NpsIcon } from 'apps/messages/components/Icons/MessageType/NpsIcon'
import { StandardIcon } from 'apps/messages/components/Icons/MessageType/StandardIcon'
import { MessageDisplayType } from 'apps/messages/model/Message'
import { previewImages } from 'apps/messages/variants/displayType/images'
import Input from 'hsds/components/input'
import Link from 'hsds/components/link'
import Portal from 'hsds/components/portal'
import ArrowLeft from 'hsds/icons/arrow-left-large'

const types = [
  { label: 'Standard', value: 'standard', Icon: StandardIcon },
  { label: 'NPS®', value: 'nps', Icon: NpsIcon },
  { label: 'Microsurvey', value: 'microsurvey', Icon: MicrosurveyIcon },
  { label: 'Modal', value: 'modal', Icon: ModalIcon },
  { label: 'Banner', value: 'banner', Icon: BannerIcon },
]

const headingId = 'create-message-header'

function usePreviewImagesPreload() {
  const imageBasePath = useImagePath('preview')
  useEffect(() => {
    Object.values(previewImages).forEach(image => {
      preLoadImage(`${imageBasePath}/${image}`)
    })
  }, [imageBasePath])
}

type Props = {
  isOpen: boolean
  isLoading: boolean
  messageName: string
  setMessageName(name: string): void
  messageType: MessageDisplayType
  setMessageType(type: MessageDisplayType): void
  onOpen(): void
  onClose(): void
  onPanelClose(): void
  onSubmit(): Promise<void>
  setInputRef(input: HTMLInputElement | HTMLTextAreaElement | null): void
  isFirstMessage?: boolean
}

export function NewMessagePanel({
  isOpen,
  isLoading,
  messageName,
  setMessageName,
  messageType,
  setMessageType,
  onClose,
  onOpen,
  onSubmit,
  setInputRef,
  isFirstMessage,
  onPanelClose,
}: Props) {
  const buttonDisabled = messageName === '' || isLoading
  const [showChrome, setShowChrome] = useState(false)
  const isMounted = useIsMounted()

  // preload preview images so that the experience is better when panel is opened
  usePreviewImagesPreload()

  const handleClose = useCallback(() => {
    onClose()
    setShowChrome(false)
  }, [onClose])

  const columnsCount = types.length === 2 ? 2 : 3

  const onPanelOpen = useCallback(() => {
    onOpen()
    isMounted() && setShowChrome(true)
  }, [isMounted, onOpen])

  const selectedMessageType = useMemo(() => [messageType], [messageType])

  return (
    <Portal>
      <NewMessageSidePanelContainerUI
        className={classNames(isFirstMessage && 'is-first-message')}
      >
        <NewMessageSidePanelUI
          show={isOpen}
          withFooter={false}
          trapFocus
          side="left"
          mode="slide-over"
          onClose={handleClose}
          ariaLabelledBy={headingId}
          onPanelOpen={onPanelOpen}
          onPanelClose={onPanelClose}
          overlayContent={
            <NewMessagePreview
              showChrome={showChrome}
              messageType={messageType}
              onClose={handleClose}
              isFirstMessage={isFirstMessage}
            />
          }
          closeButtonIcon={ArrowLeft}
          closeOnEscape={!isFirstMessage}
          zIndex={isFirstMessage ? 100 : 999} // different zIndex to avoid issues with some element from top bar being overlapped by panel
          animationDuration={200}
        >
          <ContentUI>
            <HeadingUI selector="h1" id={headingId}>
              {!isFirstMessage ? (
                <>New Message</>
              ) : (
                <div>
                  <div>Create your first message</div>
                  <SubHeadingUI>
                    <Link
                      target="_blank"
                      href="https://docs.helpscout.com/article/1344-messages"
                    >
                      Messages
                    </Link>{' '}
                    help you promote new initiatives, share key updates, and
                    proactively support your customers.
                  </SubHeadingUI>
                </div>
              )}
            </HeadingUI>
            <FormGroup label="Name">
              <Input
                value={messageName}
                onChange={setMessageName}
                inputRef={setInputRef}
                name="message-name"
              />
            </FormGroup>
            <TypesFormGroupUI label="Display type">
              <CheckMarkCardGridUI
                data-testid="types-grid"
                value={selectedMessageType}
                onChange={setMessageType}
                selectionLimits="radio"
                choiceMaxWidth="100%"
                choiceHeight="160px"
                $columnsCount={columnsCount}
              >
                {types.map(({ value, label, Icon }) => (
                  <CheckMarkCardUI key={value} label={label} value={value}>
                    <IconWrapUI>
                      <Icon />
                    </IconWrapUI>
                  </CheckMarkCardUI>
                ))}
              </CheckMarkCardGridUI>
            </TypesFormGroupUI>
            <ButtonUI
              disabled={buttonDisabled}
              onClick={onSubmit}
              loading={isLoading}
              size="xxl"
            >
              Create
            </ButtonUI>
          </ContentUI>
        </NewMessageSidePanelUI>
      </NewMessageSidePanelContainerUI>
    </Portal>
  )
}
