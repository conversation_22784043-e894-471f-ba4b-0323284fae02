import React, { memo } from 'react'

import FormGroup from '../FormGroup/FormGroup'
import Input, { InputProps } from 'hsds/components/input'

type Props = InputProps & {
  label: string
  id?: string
  helpText?: string
  errorMessage?: string
  cyWrapper?: string
  autoComplete?: string
  charValidatorLimit?: number
  withCharValidator?: boolean
  onChange: (value: string) => void
}

export const FormInput = (props: Props) => {
  const {
    cyWrapper,
    id,
    errorMessage,
    helpText,
    label,
    autoComplete = 'off',
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    ref,
    ...rest
  } = props

  return (
    <FormGroup data-cy={cyWrapper} id={id} helpText={helpText} label={label}>
      <Input
        {...rest}
        autoComplete={autoComplete}
        state={errorMessage ? 'error' : 'default'}
        errorMessage={errorMessage}
      />
    </FormGroup>
  )
}

export default memo(FormInput)
