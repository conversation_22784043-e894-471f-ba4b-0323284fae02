import { useRef } from 'react'

import { useAppSelector } from 'apps/messages/common/hooks/useAppSelector'
import { useSearchParam } from 'apps/messages/common/hooks/useSearchParam'

import { InputUI, SuffixUI } from './SearchBar.css'

import { MessageActionsType } from 'apps/messages/common/message-actions/messageAction.constants'
import { isSurveyActionIncludingNps } from 'apps/messages/model/Actions'
import ButtonIcon from 'hsds/components/button-icon'
import Icon from 'hsds/components/icon'
import CrossSmall from 'hsds/icons/cross-tiny'
import SearchSmall from 'hsds/icons/search-tiny'

function SearchBar() {
  const inputRef = useRef<HTMLInputElement | null>()
  const message = useAppSelector(state => state.message)
  const queryParam = useSearchParam<string>('query')
  const value = queryParam.get() || ''
  const messageAction = message.action
  const hasResponseText =
    messageAction.type === MessageActionsType.SURVEY_TEXT ||
    (isSurveyActionIncludingNps(messageAction) && messageAction.commentEnabled)

  function setInputRef(node: HTMLInputElement | HTMLTextAreaElement | null) {
    inputRef.current = node as HTMLInputElement
  }

  function onChange(value: string) {
    queryParam.set(value, true)
  }

  if (!hasResponseText) {
    return null
  }

  return (
    <InputUI
      autocomplete="off"
      suffix={
        <SuffixUI>
          {value ? (
            <ButtonIcon
              seamless
              size="sm"
              icon={CrossSmall}
              onClick={() => {
                onChange('')
                inputRef.current?.focus()
              }}
              aria-label="Clear search text"
            />
          ) : (
            <Icon icon={SearchSmall} faint />
          )}
        </SuffixUI>
      }
      name="search-responses"
      aria-label="Search Responses"
      placeholder="Search..."
      value={value}
      onChange={onChange}
      inputRef={setInputRef}
      style={{ width: '330px' }}
    />
  )
}

export default SearchBar
