import { format, toDate } from 'date-fns-tz'
import { noop } from 'lodash'
import React, { FocusEvent, useEffect, useRef, useState } from 'react'

import { useLegacyRef } from 'apps/messages/common/hooks/useLegacyRef'
import { useIsMounted } from 'shared/hooks/useIsMounted'

import {
  DateInputUI,
  DatePickerUI,
  DatePickerValueUI,
} from './InputDatePicker.css'

import { useReactivateFocus } from './useReactivateFocus'
import { scrollContentIntoView } from 'apps/messages/common/scroll/scrollUtils'
import Datepicker from 'hsds/components/datepicker'

const datePickerComponentRegexp = /Datepicker.+__/
const inputBackdropRegexp = /InputBackdrop/

type Props = {
  onChange?(value?: string): void
  onBlur?(): void
  value?: string
  inputName?: string
  errorMessage?: string
  dateFormat?: string
  width?: string
  position?: 'left' | 'right'
}

export const InputDatePicker = ({
  onChange = noop,
  onBlur,
  value,
  inputName = 'input-date-picker',
  errorMessage,
  dateFormat = 'yyyy-MM-dd',
  width = '150px',
  position = 'right',
  ...rest
}: Props) => {
  const [opened, setOpened] = useState(false)
  const containerRef = useRef<HTMLElement>()
  const inputRef = useRef<HTMLInputElement | null>()
  const [datepickerRef, setDatepickerRef] = useLegacyRef<HTMLElement>()
  const { setReactivateFocus } = useReactivateFocus(inputRef)
  const isMounted = useIsMounted()

  useEffect(() => {
    // This effect temporarily removes tab index from Error Icon
    // so that it is possible to tab to the calendar
    // the tab index is restored when calendar is closed
    const tooltipTrigger =
      containerRef.current?.querySelector('.TooltipTrigger')
    const tabIndexValue = opened ? '-1' : '0'
    tooltipTrigger?.setAttribute('tabindex', tabIndexValue)
  }, [opened])

  const isWithinInput = (target: HTMLInputElement) =>
    target === containerRef.current ||
    target.parentElement === containerRef.current ||
    target.name === inputName ||
    inputBackdropRegexp.test(target.classList.toString())

  const isWithinComponent = (target: HTMLInputElement) => {
    return (
      target.closest('.c-InputDatePicker') === containerRef.current &&
      (datePickerComponentRegexp.test(target.classList.toString()) ||
        (target.parentElement &&
          datePickerComponentRegexp.test(
            target.parentElement.classList.toString()
          )) ||
        isWithinInput(target))
    )
  }

  const handleChange = ({ startDate }: { startDate: string }) => {
    const date = new Date(startDate)
    const newValue = format(date, 'yyyy-MM-dd')

    onChange(newValue)
    setOpened(false)
    setReactivateFocus(true)
  }

  const setInputRef = (ref: HTMLInputElement | HTMLTextAreaElement | null) =>
    (inputRef.current = ref as HTMLInputElement)

  const onFocus = (e: FocusEvent<HTMLInputElement>) => {
    if (isWithinInput(e.target)) {
      setReactivateFocus(false)
    }
  }
  const handleBlur = () => {
    // When blur is called, new element doesn't have focus yet. This is to make sure new element is focused
    // we need this information to see if focus moved outside of the whole component or to the Datepicker
    // in the first case, we close the picker
    setTimeout(() => {
      if (
        !isWithinComponent(document.activeElement as HTMLInputElement) &&
        isMounted()
      ) {
        setOpened(false)
        onBlur?.()
      }
    }, 0)
  }

  function scrollToViewWhenOpened() {
    // Need to wait for the element to appear, because it's not rendered yet
    setTimeout(() => {
      isMounted() &&
        scrollContentIntoView({
          contentNode: datepickerRef.current,
          duration: 500,
          delay: 0,
        })
    }, 0)
  }

  const onClick = (e: MouseEvent) => {
    if (isWithinInput(e.target as HTMLInputElement)) {
      setOpened(opened => !opened)
      inputRef.current?.focus?.()
      scrollToViewWhenOpened()
    }
  }

  const onEscapeKey = (e: KeyboardEvent) => {
    const isEscapeKey = e.key === 'Escape'
    if (isEscapeKey) {
      setOpened(false)
      setReactivateFocus(true)
    }
  }

  const toggleOpenForEnterOrSpace = (
    e: React.KeyboardEvent<HTMLInputElement | HTMLTextAreaElement>
  ) => {
    const isEnter = e.key === 'Enter'
    const isSpace = e.key === ' '
    if (isEnter || isSpace) {
      setOpened(opened => !opened)
      scrollToViewWhenOpened()
    }
  }

  return (
    //@ts-ignore
    <DatePickerValueUI
      onFocus={onFocus}
      onBlur={handleBlur}
      onClick={onClick}
      // Need to prevent default on mouse down so that focus doesn't fire twice on click
      onMouseDown={(e: MouseEvent) => e.preventDefault()}
      ref={containerRef}
      onKeyUp={onEscapeKey}
      width={width}
      className="c-InputDatePicker"
    >
      <DateInputUI
        autoComplete="off"
        placeholder="Date"
        value={value ? format(toDate(value), dateFormat) : ''}
        width={width}
        readOnly={true}
        name={inputName}
        inputRef={setInputRef}
        onKeyDown={toggleOpenForEnterOrSpace}
        errorMessage={errorMessage}
        state={errorMessage ? 'error' : 'default'}
        {...rest}
      />
      {opened ? (
        <DatePickerUI
          position={position}
          //@ts-ignore
          onKeyDown={(e: KeyboardEvent) => e.stopPropagation()}
        >
          <Datepicker
            firstDayOfWeek={1}
            startDate={value ? toDate(value).toISOString() : undefined}
            onDateChange={handleChange}
            innerRef={setDatepickerRef}
          />
        </DatePickerUI>
      ) : null}
    </DatePickerValueUI>
  )
}
