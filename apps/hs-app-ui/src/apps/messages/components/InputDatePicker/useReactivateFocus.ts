import { MutableRefObject, useEffect, useState } from 'react'

export function useReactivateFocus(
  inputRef: MutableRefObject<HTMLInputElement | null | undefined>
) {
  const [reactivateFocus, setReactivateFocus] = useState(false)

  useEffect(() => {
    // we need to reactivate focus on input when closing date picker due to value selection or escape pressed
    if (reactivateFocus) {
      inputRef.current?.focus?.()
    }
  }, [inputRef, reactivateFocus])

  return { reactivateFocus, setReactivateFocus }
}
