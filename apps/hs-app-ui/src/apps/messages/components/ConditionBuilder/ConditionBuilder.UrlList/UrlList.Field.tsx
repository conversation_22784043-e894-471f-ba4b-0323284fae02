import { noop } from 'lodash'
import { useEffect, useRef } from 'react'

import { removeCondition } from '../ConditionBuilder.utils'
import {
  changeAction,
  removeAction,
} from 'apps/messages/components/ConditionBuilder/common/actions.utils'

import { AvailableConditions } from 'apps/messages/model/Conditions'
import { ConditionField } from 'hsds/components/condition'
import Input from 'hsds/components/input'

export function updateValueOfCondition(
  arr: string[],
  index: number,
  newValue: string
) {
  const newArr = [...arr]
  newArr[index] = newValue
  return newArr
}

type Props = {
  condition: string
  conditionType?: AvailableConditions
  errorMessage?: string
  index: number
  isWithRemove: boolean
  onFieldChange?(conditionType: string, value: string[]): void
  onRemove?(conditionType: string): void
  onBlur?(): void
  value: string[]
  'data-cy'?: string
  addedNew?: boolean
  ariaLabel: string
}

function useFocusAfterMount(addedNew?: boolean) {
  const inputRef = useRef<HTMLInputElement | HTMLTextAreaElement | null>()
  useEffect(() => {
    addedNew && inputRef.current?.focus?.()
  }, [addedNew])

  return (ref: HTMLInputElement | HTMLTextAreaElement | null) =>
    (inputRef.current = ref)
}

const UrlListField = (props: Props) => {
  const {
    condition,
    conditionType,
    errorMessage,
    index = 0,
    isWithRemove,
    onFieldChange = noop,
    onRemove = noop,
    value,
    addedNew,
    ariaLabel,
    onBlur,
    ...rest
  } = props

  const handleOnRemove = () => {
    value.length === 1
      ? onRemove(conditionType)
      : onFieldChange(
          conditionType,
          removeCondition(value, index),
          removeAction(index)
        )
  }

  const handleOnChange = (_value: string) => {
    onFieldChange(
      conditionType,
      updateValueOfCondition(value, index, _value),
      changeAction(index)
    )
  }
  const setInputRef = useFocusAfterMount(addedNew)

  return (
    <ConditionField
      {...rest}
      data-cy={props['data-cy']}
      isWithRemove={isWithRemove}
      onRemove={handleOnRemove}
    >
      <ConditionField.Block>
        <Input
          autoComplete="off"
          errorMessage={errorMessage}
          onChange={handleOnChange}
          placeholder="https://example.com/"
          state={errorMessage ? 'error' : 'default'}
          value={condition}
          inputRef={setInputRef}
          aria-label={ariaLabel}
          onBlur={onBlur}
        />
      </ConditionField.Block>
    </ConditionField>
  )
}

export default UrlListField
