import { isReviewImportPage } from '../utils/isReviewImportPage'

import { useHsAppContext, useNoty } from 'shared/hooks'

import { CardBlockUI, TitleUI } from './ImporterDevTools.css'

import ErrorNotification from '../components/ErrorNotification'
import Button from 'hsds/components/button'
import Card from 'hsds/components/card'
import Input from 'hsds/components/input'

export function ImporterDevTools() {
  const { appData, setAppData } = useHsAppContext()
  const { showErrorNoty } = useNoty()

  const { amountDue } = appData
  const isReviewPage = isReviewImportPage()

  function handleAmountDueChange(value: string | number) {
    setAppData({ ...appData, amountDue: value })
  }

  return (
    <Card seamless>
      {isReviewPage ? (
        <>
          <CardBlockUI>
            <TitleUI>Imports page</TitleUI>
            <Button href="/importer" outlined size="sm" color="grey">
              Go
            </Button>
          </CardBlockUI>
          <CardBlockUI>
            <TitleUI>Amount Due</TitleUI>
            <Input
              inlinePrefix="$"
              onChange={handleAmountDueChange}
              type="number"
              value={amountDue}
              width={150}
            />
          </CardBlockUI>
        </>
      ) : (
        <>
          <CardBlockUI>
            <TitleUI>Review Import page</TitleUI>
            <Button href="/importer/review" outlined size="sm" color="grey">
              Go
            </Button>
          </CardBlockUI>
          <CardBlockUI>
            <TitleUI>Error notification for Review Import page</TitleUI>
            <Button
              onClick={() => showErrorNoty(<ErrorNotification />)}
              outlined
              size="sm"
              color="grey"
            >
              Show
            </Button>
          </CardBlockUI>
        </>
      )}
    </Card>
  )
}
