import {
  ToggleFormGroupWrapperUI,
  ToggleWrapperUI,
} from 'apps/ai-drafts/components/ToggleFormGroup/ToggleFormGroup.css'

import { FormLabel } from 'apps/ai-drafts/components/FormLabel/FormLabel'
import Switch from 'hsds/components/switch'
import Tooltip from 'hsds/components/tooltip'
import ConditionalWrapper from 'shared/components/ConditionalWrapper'

type ToggleFormGroupProps = {
  children?: JSX.Element
  className?: string
  description: string
  isEnabled: boolean
  isOptionDisabled?: boolean
  label: string
  handleOnToggle: (value: boolean) => void | Promise<void>
  tooltipTitle?: string
  type: string
}

export const ToggleFormGroup = ({
  children,
  className,
  description,
  handleOnToggle,
  isEnabled,
  isOptionDisabled = false,
  label,
  tooltipTitle = '',
  type,
}: ToggleFormGroupProps) => (
  <ToggleFormGroupWrapperUI className={className}>
    <ToggleWrapperUI>
      <FormLabel
        description={description}
        forId={`enable-${type}-toggle`}
        label={label}
      />
      <ConditionalWrapper
        condition={isOptionDisabled}
        wrapper={children => <Tooltip title={tooltipTitle}>{children}</Tooltip>}
      >
        <Switch
          checked={isEnabled}
          disabled={isOptionDisabled}
          id={`enable-${type}-toggle`}
          onChange={handleOnToggle}
        />
      </ConditionalWrapper>
    </ToggleWrapperUI>
    {isEnabled && children}
  </ToggleFormGroupWrapperUI>
)
