import { useGetSettings } from 'apps/ai-drafts/hooks/useGetSettings'
import { useInboxSettingsSavedNoty } from 'apps/ai-drafts/hooks/useInboxSettingsSavedNoty'
import { useIsAiDraftsEnabled } from 'apps/ai-drafts/hooks/useIsAiDraftsEnabled'

import { PageLoaderUI } from './SettingsScreen.css'

import { Actions } from 'apps/ai-drafts/components/Actions/Actions'
import { DataSources } from 'apps/ai-drafts/components/DataSources/DataSources'
import { Manage } from 'apps/ai-drafts/components/Manage/Manage'
import Page from 'hsds/components/page'
import Loader from 'shared/components/Loader'

export const SettingsScreen = () => {
  const { isLoading } = useGetSettings()
  const isAiDraftsEnabled = useIsAiDraftsEnabled()

  useInboxSettingsSavedNoty()

  if (isLoading) {
    return (
      <PageLoaderUI>
        <Loader />
      </PageLoaderUI>
    )
  }

  return (
    <Page>
      <Manage />
      {isAiDraftsEnabled && (
        <>
          <DataSources />
          <Actions />
        </>
      )}
    </Page>
  )
}
