import styled from 'styled-components'

import { getColor } from 'hsds/utils/color'

import Button from 'hsds/components/button'
import Page from 'hsds/components/page'
import { getToken } from 'hsds/tokens'

export const MigrationPageUI = styled(Page)`
  &&&& {
    --hsds-token-page-maxWidth-default: 1000px;
  }

  .c-PageCard {
    min-height: 600px;
  }

  .c-PageContent {
    display: flex;
    flex-direction: column;
    justify-content: center;
  }
`

export const MigrationContentUI = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 100%;
`

export const MigrationTextUI = styled.div`
  display: flex;
  flex-direction: column;
  width: 375px;

  color: ${getToken('color.text.default')};
  font-size: 14px;
  font-weight: 400;
  line-height: 22px;
`

export const MigrateReasonsListUI = styled.ol`
  margin-top: 20px;
  margin-bottom: 30px;
`

export const MigrationInProgressContainerUI = styled.div`
  margin-top: 10px;
  margin-bottom: 30px;
`

export const MigrateReasonItemUI = styled.li`
  list-style: none;
  display: flex;
  align-items: center;
  gap: 4px;

  & + & {
    margin-top: 10px;
  }

  .c-Icon {
    color: ${getColor('green.500')};
  }
`

export const ButtonUI = styled(Button)`
  & + & {
    margin-top: 6px;
  }
`

export const ImageUI = styled.img`
  height: 500px;
  margin-right: calc(var(--hsds-page-card-padding-side) * -0.5);
`
