import { useState } from 'react'

import { useGlobals } from 'shared/components/HsApp/HsApp.utils'
import {
  getHsAppLocalStorageValue,
  setHsAppLocalStorageValue,
} from 'shared/utils/HsAppLocalStorage/hsAppLocalStorage'
import { HS_APP_STORE_KEYS } from 'shared/utils/HsAppLocalStorage/localStorageKeys'

import { useBeacon } from 'shared/hooks/useBeacon'

import {
  ButtonUI,
  ImageUI,
  MigrateReasonItemUI,
  MigrateReasonsListUI,
  MigrationContentUI,
  MigrationInProgressContainerUI,
  MigrationPageUI,
  MigrationTextUI,
} from './MigrationScreen.css'

import { LearnModalContent } from './LearnModalContent/LearnModalContent'
import Heading from 'hsds/components/heading'
import Icon from 'hsds/components/icon'
import Page from 'hsds/components/page'
import Portal from 'hsds/components/portal'
import SimpleModal from 'hsds/components/simple-modal'
import Checkmark from 'hsds/icons/check'

const migrateReasons = [
  'Generate AI Drafts automatically or on demand',
  'Use custom prompts to edit AI Drafts',
  'Access everything in one place',
]

export const MigrationScreen = () => {
  const { imagePath } = useGlobals()
  const migrationImage = `${imagePath}ai-drafts/migration-image.png`

  const { openMessage, once } = useBeacon()

  const [show, setShowModal] = useState(false)
  const [migrationStarted, setMigrationStarted] = useState<boolean>(() =>
    Boolean(
      getHsAppLocalStorageValue(HS_APP_STORE_KEYS.AI_DRAFT_MIGRATION_STARTED)
    )
  )

  const startMigration = () => {
    once('email-sent', () => {
      setMigrationStarted(true)
      setHsAppLocalStorageValue(
        HS_APP_STORE_KEYS.AI_DRAFT_MIGRATION_STARTED,
        true
      )
    })
    openMessage({
      subject: 'Migrate my SupportAgent.ai account',
      text: `I’d like to migrate my SupportAgent.ai account over to AI Drafts in Help Scout. Can you take care of that for me?`,
    })
  }
  return (
    <MigrationPageUI>
      <Page.Card>
        <Page.Content>
          <MigrationContentUI>
            <MigrationTextUI>
              <Heading size="h3">
                {migrationStarted
                  ? 'We’re on it!'
                  : 'Migrate from SupportAgent.ai for more AI magic ✨'}
              </Heading>
              {migrationStarted ? (
                <MigrationInProgressContainerUI>
                  <MigrationTextUI>
                    Your migration is in progress — feel free to navigate away
                    from this page. You’ll receive an email when the process is
                    complete. Stay tuned!
                  </MigrationTextUI>
                </MigrationInProgressContainerUI>
              ) : (
                <MigrateReasonsListUI>
                  {migrateReasons.map(reason => (
                    <MigrateReasonItemUI key={reason}>
                      <Icon icon={Checkmark} />
                      {reason}
                    </MigrateReasonItemUI>
                  ))}
                </MigrateReasonsListUI>
              )}
              <ButtonUI
                size="xxl"
                onClick={startMigration}
                disabled={migrationStarted}
              >
                {migrationStarted
                  ? 'Migration in Progress...'
                  : 'Migrate from SupportAgent.ai'}
              </ButtonUI>
              <ButtonUI
                size="xxl"
                linked
                onClick={() => {
                  setShowModal(true)
                }}
              >
                How migrating works
              </ButtonUI>
            </MigrationTextUI>
            <ImageUI src={migrationImage} alt="AI Drafts example" />
          </MigrationContentUI>
        </Page.Content>
      </Page.Card>
      <Portal>
        <SimpleModal
          show={show}
          onClose={() => setShowModal(false)}
          width="500px"
          height="560px"
          closeOnClickOutside="modal"
        >
          <LearnModalContent />
        </SimpleModal>
      </Portal>
    </MigrationPageUI>
  )
}
