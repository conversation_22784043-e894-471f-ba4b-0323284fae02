import { useEffect, useState } from 'react'

import { useHsAppContext } from 'shared/hooks'

import { useRegistrationType } from './useRegistrationType'
import { InputProps } from 'hsds/components/input'

interface ErrorValidationProps {
  otherErrors: string[]
  getInputState: (name: string) => InputProps['state']
  getInputErrorMessage: (name: string) => string
}

export function useErrorValidation(): ErrorValidationProps {
  const [fieldErrors, setFieldErrors] = useState<{ [name: string]: string }>({})
  const [otherErrors, setOtherErrors] = useState<string[]>([])
  const { isRegistrationTypeEmail } = useRegistrationType()

  const { appData } = useHsAppContext()

  const { errorMessages, errorFields } = appData

  function matchFieldWithErrorMessage(
    fields: string[],
    messages: string[]
  ): { [name: string]: string } {
    return fields.reduce<{ [name: string]: string }>((errors, field, index) => {
      errors[field] = messages[index]
      return errors
    }, {})
  }

  useEffect(() => {
    // Only match the fields with the error messages if the registration type
    // is email
    if (errorFields && errorFields.length > 0 && isRegistrationTypeEmail) {
      setFieldErrors(matchFieldWithErrorMessage(errorFields, errorMessages))
    } else {
      setOtherErrors(errorMessages)
    }
  }, [errorMessages, errorFields, isRegistrationTypeEmail])

  const getInputState = (name: string): InputProps['state'] => {
    return fieldErrors[name] ? 'error' : 'default'
  }

  const getInputErrorMessage = (name: string): string => {
    return fieldErrors[name] || ''
  }

  return { getInputErrorMessage, otherErrors, getInputState }
}
