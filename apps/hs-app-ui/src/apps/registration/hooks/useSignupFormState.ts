import { useEffect, useRef, useState } from 'react'

import { useErrorValidation } from './useErrorValidation'
import { usePrefilledUserData } from './usePrefilledUserData'

interface InputProps {
  id: string
  label: string
  // 'aria-required'?: string
  placeholder: string
  name: string
}

export const useSignupFormState = () => {
  const prefilledUserData = usePrefilledUserData()
  const [inputValues, setInputValues] = useState<{ [id: string]: string }>(
    prefilledUserData
  )
  const [isSubmitting, setIsSubmitting] = useState<boolean>(false)

  const inputRef = useRef<HTMLInputElement>()
  const setInputRef = (ref: HTMLInputElement | HTMLTextAreaElement | null) =>
    (inputRef.current = ref as HTMLInputElement)

  useEffect(() => {
    if (inputRef.current) {
      inputRef.current.focus()
    }
  }, [])

  const { otherErrors, getInputState, getInputErrorMessage } =
    useErrorValidation()

  const getInputAttributes = (props: InputProps) => {
    const { id, ...rest } = props
    return {
      id,
      value: inputValues[id],
      onChange: (value: string) =>
        setInputValues(_inputValues => ({
          ..._inputValues,
          [id]: value,
        })),
      state: getInputState(id),
      errorMessage: getInputErrorMessage(id),
      'data-cy': `signup-${id}`,
      'data-tracking': `Registration.EmailSignup.${id}`,
      ...rest,
    }
  }

  return {
    getInputAttributes,
    isSubmitting,
    otherErrors,
    setInputRef,
    setIsSubmitting,
  }
}
