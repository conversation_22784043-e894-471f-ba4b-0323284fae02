import { useCallback, useEffect, useRef, useState } from 'react'
import { useHistory } from 'react-router'

import { useAppSelector, useThunkDispatch } from 'apps/users/state/hooks'
import { useNoty } from 'shared/hooks'

import { UserTextFields } from 'apps/users/common/model'
import {
  removeValidationError,
  resetState,
  setMailboxes,
  updateUserTextData,
} from 'apps/users/state/user/actions'
import { saveUser } from 'apps/users/state/user/actions/users.actions'

function useModalState(isModalOpen: boolean) {
  const [isOpen, setIsOpen] = useState(isModalOpen)
  const history = useHistory()

  const dispatch = useThunkDispatch()
  useEffect(() => {
    void dispatch(setMailboxes())
  }, [dispatch])

  useEffect(() => {
    if (isOpen) {
      firstNameInputRef.current?.focus()
    }
  }, [isOpen])

  const firstNameInputRef = useRef<
    HTMLInputElement | HTMLTextAreaElement | null
  >()

  // For now it redirects to the Users page on hs-app,
  // when the hsapp-ui Users List page is ready,  we should set isOpen to false, return the
  // focus to the modal trigger and reset the state.
  const onModalClose = (
    event: React.MouseEvent | MouseEvent | React.KeyboardEvent
  ) => {
    event.preventDefault()
    history.push('/')
  }

  function setFirstNameInputRef(
    node: HTMLInputElement | HTMLTextAreaElement | null
  ) {
    firstNameInputRef.current = node
  }

  return {
    isOpen,
    openModal: () => setIsOpen(true),
    onClose: onModalClose,
    setFirstNameInputRef,
  }
}

function useTextFieldEvent() {
  const dispatch = useThunkDispatch()
  const validationErrors = useAppSelector(state => state.users.validationErrors)

  const handleTextFieldChange = useCallback(
    ({ name = '', value = '' }) => {
      const field = name as keyof UserTextFields
      dispatch(updateUserTextData({ field, value }))
      validationErrors[field] &&
        value.length &&
        dispatch(removeValidationError(name))
    },
    [dispatch, validationErrors]
  )

  return {
    handleTextFieldChange,
  }
}

function useModalForm() {
  const dispatch = useThunkDispatch()
  const history = useHistory()
  const validationErrors = useAppSelector(state => state.users.validationErrors)
  const inputValues = useAppSelector(state => state.users.data)
  const isButtonLoading = useAppSelector(state => state.users.isSavingUser)
  const { showSuccessNoty, showErrorNoty } = useNoty()

  async function onSubmit() {
    try {
      await dispatch(saveUser()).unwrap()
      showSuccessNoty('User created')
      history.push('/')
      dispatch(resetState())
    } catch (error: any) {
      if (
        error?.data?.errors &&
        Array.isArray(error.data.errors) &&
        error.data.errors.length > 0
      ) {
        if (
          error.status === 422 &&
          error.data.errors[0]?.toLowerCase().includes('already in use')
        ) {
          showErrorNoty(
            'That email is already associated with a Help Scout account.'
          )
        } else {
          showErrorNoty(error.data.errors[0])
        }
        return
      }

      !error.isValidationError &&
        showErrorNoty('Something went wrong. Please try again.')
    }
  }

  const getErrorState = (name: string) =>
    validationErrors[name] ? 'error' : 'default'

  return {
    onSubmit,
    validationErrors,
    inputValues,
    isButtonLoading,
    getErrorState,
  }
}

export { useModalState, useTextFieldEvent, useModalForm }
