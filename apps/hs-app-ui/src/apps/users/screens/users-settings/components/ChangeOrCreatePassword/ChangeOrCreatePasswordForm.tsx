import { useEffect, useState } from 'react'

import { getStrengthLevel } from 'shared/utils/passwordStrength/passwordStrength'

import {
  IconUI,
  PasswordFormUI,
  PasswordInputUI,
  PasswordStrengthUI,
  TextUI,
} from './ChangeOrCreatePasswordForm.css'

import { ChangePasswordFields } from './type'
import { validateChangePasswordField } from './util'
import FormGroup from 'hsds/components/form-group'
import Input from 'hsds/components/input'
import Label from 'hsds/components/label'

export const ChangeOrCreatePasswordForm = ({
  hasPassword,
  changePasswordFields,
  updateField,
  setCanSave,
  errors,
  updateErrors,
}: {
  hasPassword: boolean
  changePasswordFields: ChangePasswordFields
  updateField: (field: ChangePasswordFields) => void
  setCanSave: (canSave: boolean) => void
  errors: Record<keyof ChangePasswordFields, string>
  updateErrors: (errors: Record<keyof ChangePasswordFields, string>) => void
}) => {
  const [isDirty, setIsDirty] = useState<
    Record<keyof ChangePasswordFields, boolean>
  >({
    currentPassword: false,
    newPassword: false,
    confirmPassword: false,
  })
  const { icon, text, state } = getStrengthLevel(
    changePasswordFields.newPassword
  )

  useEffect(() => {
    const isAllFieldsDirty = hasPassword
      ? isDirty.currentPassword &&
        isDirty.newPassword &&
        isDirty.confirmPassword
      : isDirty.newPassword && isDirty.confirmPassword

    isAllFieldsDirty && handleCanSave()
  }, [isDirty, errors])

  const handleUpdateField =
    (fieldName: keyof ChangePasswordFields) => (value: string) => {
      if (isDirty[fieldName]) {
        const error = validateChangePasswordField(
          fieldName,
          value,
          changePasswordFields.newPassword
        )
        const newErrors = { ...errors, [fieldName]: error }
        if (fieldName === 'newPassword' && isDirty['confirmPassword']) {
          newErrors.confirmPassword = validateChangePasswordField(
            'confirmPassword',
            changePasswordFields.confirmPassword,
            value
          )
        }
        updateErrors(newErrors)
      }
      updateField({ ...changePasswordFields, [fieldName]: value })
    }

  const handleOnBlur =
    (fieldName: keyof ChangePasswordFields) =>
    (
      event: React.FocusEvent<HTMLInputElement | HTMLTextAreaElement | null>
    ) => {
      const value = event.target.value
      if (!isDirty[fieldName]) {
        const error = validateChangePasswordField(
          fieldName,
          value,
          changePasswordFields.newPassword
        )
        updateErrors({ ...errors, [fieldName]: error })
        setIsDirty({ ...isDirty, [fieldName]: true })
      }
    }

  const handleCanSave = () => {
    const canSave = hasPassword
      ? !errors.currentPassword &&
        !errors.newPassword &&
        !errors.confirmPassword
      : !errors.newPassword && !errors.confirmPassword
    setCanSave(canSave)
  }

  const showPasswordStrength =
    isDirty.newPassword && !errors.newPassword && state

  return (
    <PasswordFormUI>
      {hasPassword && (
        <FormGroup data-testid="currentPasswordGroup">
          <Label for="currentPassword">Current Password</Label>
          <Input
            aria-required="true"
            name="currentPassword"
            type="password"
            id="currentPassword"
            value={changePasswordFields.currentPassword}
            data-cy="Profile.currentPassword"
            data-testid="currentPassword"
            onChange={handleUpdateField('currentPassword')}
            onBlur={handleOnBlur('currentPassword')}
            errorMessage={errors.currentPassword}
            state={errors.currentPassword ? 'error' : 'default'}
            autocomplete="current-password"
          />
        </FormGroup>
      )}
      <FormGroup data-testid="newPasswordGroup">
        <Label for="newPassword">New Password</Label>
        <PasswordInputUI
          aria-required="true"
          type="password"
          id="newPassword"
          value={changePasswordFields.newPassword}
          data-cy="Profile.newPassword"
          data-testid="newPassword"
          onChange={handleUpdateField('newPassword')}
          onBlur={handleOnBlur('newPassword')}
          errorMessage={errors.newPassword}
          state={errors.newPassword ? 'error' : 'default'}
          autocomplete="new-password"
          suffix={
            showPasswordStrength && (
              <PasswordStrengthUI data-testid="password-strength">
                <IconUI icon={icon} state={state} size={24} />
                <TextUI state={state}>{text}</TextUI>
              </PasswordStrengthUI>
            )
          }
        />
      </FormGroup>
      <FormGroup data-testid="confirmPasswordGroup">
        <Label for="confirmPassword">Confirm Password</Label>
        <Input
          aria-required="true"
          type="password"
          id="confirmPassword"
          value={changePasswordFields.confirmPassword}
          data-cy="Profile.confirmPassword"
          data-testid="confirmPassword"
          onChange={handleUpdateField('confirmPassword')}
          onBlur={handleOnBlur('confirmPassword')}
          errorMessage={errors.confirmPassword}
          state={errors.confirmPassword ? 'error' : 'default'}
          autocomplete="new-password"
        />
      </FormGroup>
    </PasswordFormUI>
  )
}
