import { Provider } from 'react-redux'

import '@testing-library/jest-dom'
import { fireEvent, render, screen, waitFor } from '@testing-library/react'
import userEvent from '@testing-library/user-event'

import { useModal } from '../../../../hooks/AuthenticationModalsContext'
import { useDisable2FA } from '../../../../hooks/useDisable2FA'
import { useSecurityData } from 'apps/users/screens/users-settings/screens/Authentication/hooks/useSecurityData'
import { useCreateTwoFactorSms } from 'apps/users/screens/users-settings/screens/Authentication/tabs/Security/hooks/useCreateTwoFactorSms'
import { useGetUserId } from 'apps/users/screens/users-settings/screens/Authentication/tabs/Security/hooks/useGetUserId'
import { useHsAppContext, useNoty } from 'shared/hooks'

import {
  UserSettings,
  UserSettingsStatus,
} from '../../../../../../common/types'
import { PhoneNumber } from './PhoneNumber'
import { createUsersStore } from 'apps/users/state'
import Input from 'hsds/components/input'

jest.mock('shared/components/Phone', () => {
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const MockPhone = (props: React.HTMLProps<HTMLInputElement>) => (
    <Input {...(props as any)} name="phone-number" />
  )
  MockPhone.displayName = 'MockPhone'
  return MockPhone
})
jest.mock('react-router', () => ({
  useParams: jest.fn().mockImplementation(() => ({ profileId: '1234' })),
}))

jest.mock('../../../../hooks/AuthenticationModalsContext', () => ({
  useModal: jest.fn(),
}))

jest.mock('../../../../hooks/useCreateTwoFactorSms', () => ({
  useCreateTwoFactorSms: jest.fn(),
}))

jest.mock('../../../../hooks/useGetUserId', () => ({
  useGetUserId: jest.fn(),
}))

jest.mock(
  'apps/users/screens/users-settings/screens/Authentication/hooks/useSecurityData',
  () => ({
    useSecurityData: jest.fn(),
  })
)

jest.mock('../../../../hooks/useDisable2FA', () => ({
  useDisable2FA: jest.fn(),
}))

jest.mock('shared/hooks', () => ({
  useHsAppContext: jest.fn(),
  useNoty: jest.fn(),
}))

describe('PhoneNumber Component', () => {
  const closeModalMock = jest.fn()
  const loadSecurityDataMock = jest.fn()
  const showSuccessNotyMock = jest.fn()
  const createTwoFactorSmsSetupMock = jest.fn()
  const disable2FAMock = jest.fn(() => Promise.resolve())
  const userIdMock = 1234

  beforeEach(() => {
    jest.clearAllMocks()

    // Mocking hooks and their return values
    ;(useModal as jest.Mock).mockReturnValue({ closeModal: closeModalMock })
    ;(useSecurityData as jest.Mock).mockReturnValue({
      loadSecurityData: loadSecurityDataMock,
    })
    ;(useNoty as jest.Mock).mockReturnValue({
      showSuccessNoty: showSuccessNotyMock,
    })
    ;(useCreateTwoFactorSms as jest.Mock).mockReturnValue({
      createTwoFactorSmsSetup: createTwoFactorSmsSetupMock,
      isLoading: false,
      error: null,
    })
    ;(useGetUserId as jest.Mock).mockReturnValue(userIdMock)
    ;(useHsAppContext as jest.Mock).mockReturnValue({
      appData: { phoneCountryCodes: [{ value: '+1', label: 'USA (+1)' }] },
    })
    ;(useDisable2FA as jest.Mock).mockReturnValue({
      disable2FA: disable2FAMock,
    })
  })

  const initialSecurity = {
    userSettings: {
      phoneNumber: '',
      status: UserSettingsStatus.Disabled,
      isFactorVerified: false,
      isPasswordVerified: false,
      type: '' as UserSettings['type'],
      backupPhone: '',
    },
    backupCode: [],
    isUiEnabled: false,
    hasLoadedSecurityData: false,
    isInitialSetup: false,
  }

  const setupComponent = ({
    isBackup = false,
    hasSkip = false,
    isInitialSetup = false,
  }: {
    isBackup?: boolean
    hasSkip?: boolean
    isInitialSetup?: boolean
  }) => {
    const store = createUsersStore({
      authentication: {
        security: {
          ...initialSecurity,
          isInitialSetup,
        },
        authorizedApps: {
          authenticatedApps: [],
          hasLoadedAuthenticatedApps: false,
        },
        apiKeys: {
          hasLoadedApiKeys: false,
        },
        activeSessions: {
          hasLoadedSessions: false,
        },
      },
    })
    return render(
      <Provider store={store}>
        <PhoneNumber isBackup={isBackup} hasSkip={hasSkip} />
      </Provider>
    )
  }

  it('renders with title and description for primary phone number', () => {
    setupComponent({})
    expect(screen.getByText('Enter your mobile number')).toBeInTheDocument()
    expect(
      screen.getByText(
        'When you log in, we’ll send a verification code to this number.'
      )
    ).toBeInTheDocument()
  })

  it('renders with title and description for backup phone number', () => {
    setupComponent({ isBackup: true })
    expect(
      screen.getByText('Would you like to add a backup number?')
    ).toBeInTheDocument()
    expect(
      screen.getByText(
        'When you log in, we’ll send a one-time verification code to this number.'
      )
    ).toBeInTheDocument()
  })

  it('disables the primary button when phone number is empty', () => {
    setupComponent({})
    const primaryButton = screen.getByRole('button', { name: /send code/i })
    expect(primaryButton).toBeDisabled()
  })

  it('enables the primary button when phone number is entered', () => {
    setupComponent({})
    const primaryButton = screen.getByRole('button', { name: /send code/i })
    const phoneInput = screen.getByRole('textbox', { name: 'Phone number' })

    fireEvent.change(phoneInput, { target: { value: '+1234567890' } })

    expect(primaryButton).toBeEnabled()
  })

  it('calls createTwoFactorSmsSetup with correct parameters on primary button click', () => {
    setupComponent({})
    const phoneInput = screen.getByLabelText('Phone number')
    userEvent.type(phoneInput, '+1234567890')

    const primaryButton = screen.getByRole('button', { name: /send code/i })
    fireEvent.click(primaryButton)

    expect(createTwoFactorSmsSetupMock).toHaveBeenCalled()
  })

  it('calls onSkip function and shows success notification as updated when Skip button is clicked for backup number', async () => {
    setupComponent({ isBackup: true, hasSkip: true })
    const skipButton = screen.getByRole('button', { name: /skip/i })
    fireEvent.click(skipButton)

    await waitFor(() => {
      expect(loadSecurityDataMock).toHaveBeenCalledWith(userIdMock.toString())
      expect(showSuccessNotyMock).toHaveBeenCalledWith(
        'Two-factor authentication updated'
      )
    })
  })
  it('calls onSkip function and shows success notification as updated when Skip button is clicked for backup number', async () => {
    setupComponent({ isBackup: true, hasSkip: true, isInitialSetup: true })
    const skipButton = screen.getByRole('button', { name: /skip/i })
    fireEvent.click(skipButton)

    await waitFor(() => {
      expect(loadSecurityDataMock).toHaveBeenCalledWith(userIdMock.toString())
      expect(showSuccessNotyMock).toHaveBeenCalledWith(
        'Two-factor authentication enabled'
      )
    })
  })

  it('displays loading state when isLoading is true', () => {
    ;(useCreateTwoFactorSms as jest.Mock).mockReturnValue({
      createTwoFactorSmsSetup: createTwoFactorSmsSetupMock,
      isLoading: true,
      error: null,
    })

    setupComponent({})
    const primaryButton = screen.getByRole('button', { name: /send code/i })
    expect(primaryButton).toBeDisabled()
  })

  it('should disable 2FA when cancelling the setup on initial setup back phone step ', async () => {
    setupComponent({ isBackup: true, isInitialSetup: true })
    const cancelButton = screen.getByRole('button', { name: /cancel setup/i })
    fireEvent.click(cancelButton)

    await waitFor(() => {
      expect(disable2FAMock).toHaveBeenCalled()
      expect(closeModalMock).toHaveBeenCalled()
    })
  })

  it('should not disable 2FA when closing the modal', async () => {
    setupComponent({ isBackup: false, isInitialSetup: true })
    const cancelButton = screen.getByRole('button', { name: /cancel setup/i })
    fireEvent.click(cancelButton)

    await waitFor(() => {
      expect(disable2FAMock).not.toHaveBeenCalled()
      expect(closeModalMock).toHaveBeenCalled()
    })
  })
})
