import { useCallback, useEffect, useRef, useState } from 'react'

const useFormInput = (
  initialValue: string,
  {
    validate,
    validateOnChange = true,
    validateOnBlur = true,
  }: {
    validate?: (value: string) => null | {
      isValid?: boolean
      message?: string | undefined
    }
    validateOnChange?: boolean
    validateOnBlur?: boolean
  } = {}
): {
  checkValidity: () => void
  errorMessage: string | null | undefined
  getValidationState: () => 'default' | 'error'
  handleChange: (value: string) => void
  handleBlur: () => void
  inputRef: React.RefObject<HTMLInputElement | HTMLTextAreaElement | null>
  isTouched: boolean
  isValid: boolean
  setInputRef: (ref: HTMLInputElement | HTMLTextAreaElement | null) => void
  setValue: (newValue: string) => void
  value: string
} => {
  const [value, setValue] = useState<string>(initialValue)
  const [isTouched, setTouched] = useState<boolean>(false)
  const [isValid, setIsValid] = useState<boolean>(!validateOnChange) // if validateOnChange is false, we assume the initial value is valid to avoid immediately showing an invalid state
  const [errorMessage, setErrorMessage] = useState<string | null>(null)

  const valueRef = useRef<string>(initialValue)
  const inputRef = useRef<HTMLInputElement | HTMLTextAreaElement | null>(null)

  const checkValidity = useCallback(() => {
    if (!inputRef.current) return

    const inputElement = inputRef.current as HTMLInputElement
    const isValidBrowser = inputElement.checkValidity() ?? true
    const browserMessage = inputElement.validationMessage ?? null
    const validationResult = {
      isValid: isValidBrowser,
      message: browserMessage,
    }

    if (validate) {
      const validation = validate(valueRef.current)
      validationResult.isValid = validation?.isValid ?? isValidBrowser
      validationResult.message = validation?.message ?? browserMessage
    }

    setIsValid(validationResult.isValid)
    setErrorMessage(validationResult.message)
  }, [validate])

  useEffect(() => {
    if (validateOnChange) {
      checkValidity()
    }
  }, [value, validateOnChange, checkValidity])

  const setInputRef = useCallback(
    (ref: HTMLInputElement | HTMLTextAreaElement | null) => {
      inputRef.current = ref
    },
    []
  )

  const updateValue = useCallback((newValue: string) => {
    setValue(newValue)
    valueRef.current = newValue
  }, [])

  const handleChange = useCallback(
    (value: string) => {
      if (!isTouched) setTouched(true)
      updateValue(value)
      if (validateOnChange) {
        checkValidity()
      }
    },
    [isTouched, updateValue, checkValidity, validateOnChange]
  )

  const handleBlur = useCallback(() => {
    if (!isTouched) setTouched(true)
    if (validateOnBlur) {
      checkValidity()
    }
  }, [isTouched, checkValidity, validateOnBlur])

  const getValidationState = useCallback(() => {
    if (!isTouched) return 'default'
    return isValid ? 'default' : 'error'
  }, [isTouched, isValid])

  return {
    checkValidity,
    errorMessage: isTouched ? errorMessage : null,
    getValidationState,
    handleChange,
    handleBlur,
    inputRef,
    isTouched,
    isValid,
    setInputRef,
    setValue: updateValue,
    value: valueRef.current,
  }
}

export default useFormInput
