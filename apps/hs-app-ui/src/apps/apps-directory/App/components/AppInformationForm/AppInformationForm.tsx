import {
  parseSidePanelUrls,
  stringifySidePanelUrls,
  validateSidePanelUrlValid,
} from '../../../utils/sidePanelUrls'

import useFetchApplications from './hooks/useFetchApplications'
import useFormInput from './hooks/useFormInput'
import useSubmitAppInformation from './hooks/useSubmitAppInformation'
import { useHsAppContext } from 'shared/hooks'

import { SidePanelUrlsInputUI } from './AppInformationForm.css'

import RequiredFormLabel from '../RequiredFormLabel/RequiredFormLabel'
import Alert from 'hsds/components/alert'
import Form from 'hsds/components/form'
import FormGroup from 'hsds/components/form-group'
import FormLabel from 'hsds/components/form-label'
import Input from 'hsds/components/input'
import { AppsPlatformApplication } from 'shared/types/apps/AppsPlatformApp'

const AppInformationForm = ({
  application,
  sidePanelUrls: currentSidePanelUrls,
}: {
  application?: AppsPlatformApplication
  sidePanelUrls?: string[]
}) => {
  const {
    hsGlobal: { companyId, memberId },
  }: {
    hsGlobal: {
      companyId: number
      memberId: number
    }
  } = useHsAppContext()

  const name = useFormInput(application?.name || '', {
    validate: () => ({
      // default browser validation will handle the validation of this field
      message: 'App Name is required.',
    }),
  })
  const contentUrl = useFormInput(
    application?.extensions?.conversationRightSidebar?.contentUrl || 'https://',
    {
      validate: (url: string) => {
        if (!url) return { isValid: false, message: 'Content URL is required.' }
        try {
          new URL(url)
          return !url.startsWith('https://')
            ? {
                isValid: false,
                message: 'Please provide a valid URL starting with https://.',
              }
            : null // fall back to default browser validation
        } catch {
          return { isValid: false, message: 'Invalid URL format.' }
        }
      },
    }
  )
  const contentUrlSignatureKey = useFormInput(
    application?.extensions?.conversationRightSidebar?.contentUrlSignatureKey ||
      ''
  )

  const sidePanelUrls = useFormInput(
    stringifySidePanelUrls(currentSidePanelUrls),
    {
      validateOnChange: false,
      validate: (urls: string) => {
        const lines = parseSidePanelUrls(urls)

        const invalidMessages = lines
          .map(validateSidePanelUrlValid)
          .filter(val => val !== null)

        if (invalidMessages.length > 0) {
          return {
            isValid: false,
            message: invalidMessages.join('\n'),
          }
        }

        return null
      },
    }
  )
  const isFormValid =
    !!name.value &&
    name.isValid &&
    !!contentUrl.value &&
    contentUrl.isValid &&
    contentUrlSignatureKey.isValid &&
    sidePanelUrls.isValid

  const { isFetchingApplications, isError } = useFetchApplications()

  const submitEnabled = isFormValid && !isFetchingApplications

  const {
    isSubmitting,
    errorMessage,
    messageAreaRef,
    setErrorMessage,
    submitForm,
  } = useSubmitAppInformation(companyId, memberId, application?.id)

  const handleSave = (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault()

    // clear error message
    setErrorMessage('')

    name.checkValidity()
    contentUrl.checkValidity()
    contentUrlSignatureKey.checkValidity()

    if (!isFormValid) {
      setErrorMessage('Please ensure all fields are filled out correctly.')
      return
    }

    const formData = {
      name: name.value,
      contentUrl: contentUrl.value,
      contentUrlSignatureKey: contentUrlSignatureKey.value,
      sidePanelUrls: parseSidePanelUrls(sidePanelUrls.value),
    }

    submitForm(formData)
  }

  return (
    <Form
      onSave={handleSave}
      {...(isSubmitting && {
        saveButtonProps: { loading: true, disabled: true },
      })}
      {...(!submitEnabled && {
        saveButtonProps: { disabled: true },
      })}
    >
      <div ref={messageAreaRef}>
        {errorMessage && <Alert status="error">{errorMessage}</Alert>}
      </div>
      {isError && (
        <Alert status="error">
          Something went wrong. Please refresh your browser.
        </Alert>
      )}
      <FormGroup>
        <RequiredFormLabel
          required={true}
          label="App name"
          state={name.getValidationState()}
        >
          <Input
            inputRef={(ref: HTMLInputElement | HTMLTextAreaElement | null) =>
              name.setInputRef(ref as HTMLInputElement)
            }
            type="text"
            id="name"
            name="name"
            required={true}
            value={name.value || ''}
            onBlur={name.handleBlur}
            onChange={name.handleChange}
            state={name.getValidationState()}
            errorMessage={name.errorMessage}
          />
        </RequiredFormLabel>
      </FormGroup>
      <FormGroup>
        <RequiredFormLabel
          required={true}
          label="Content URL"
          state={contentUrl.getValidationState()}
        >
          <Input
            inputRef={(ref: HTMLInputElement | HTMLTextAreaElement | null) =>
              contentUrl.setInputRef(ref as HTMLInputElement)
            }
            type="url"
            id="contentUrl"
            name="contentUrl"
            required={true}
            value={contentUrl.value || ''}
            onBlur={contentUrl.handleBlur}
            onChange={contentUrl.handleChange}
            state={contentUrl.getValidationState()}
            errorMessage={contentUrl.errorMessage}
            hintText="The URL of your web app. It must be a valid URL that starts with https://."
          />
        </RequiredFormLabel>
      </FormGroup>
      <FormGroup>
        <RequiredFormLabel
          required={false}
          label="Content signature key"
          state={contentUrlSignatureKey.getValidationState()}
        >
          <Input
            inputRef={(ref: HTMLInputElement | HTMLTextAreaElement | null) =>
              contentUrlSignatureKey.setInputRef(ref as HTMLInputElement)
            }
            type="text"
            id="contentUrlSignatureKey"
            name="contentUrlSignatureKey"
            maxLength={40}
            charValidatorLimit={40}
            withCharValidator={true}
            value={contentUrlSignatureKey.value || ''}
            onBlur={contentUrlSignatureKey.handleBlur}
            onChange={contentUrlSignatureKey.handleChange}
            state={contentUrlSignatureKey.getValidationState()}
            errorMessage={contentUrlSignatureKey.errorMessage}
            hintText="The secret key is a randomly generated (by you) 40-character or less string used to create signatures for each data request. Help Scout uses this secret key to generate a signature for each message."
          />
        </RequiredFormLabel>
      </FormGroup>

      <FormGroup>
        <FormLabel label="Side Panel URLs">
          <SidePanelUrlsInputUI
            required={false}
            inputRef={(ref: HTMLInputElement | HTMLTextAreaElement | null) =>
              sidePanelUrls.setInputRef(ref as HTMLInputElement)
            }
            type="text"
            multiline={true}
            id="sidePanelUrls"
            name="sidePanelUrls"
            value={sidePanelUrls.value || ''}
            onBlur={sidePanelUrls.handleBlur}
            onChange={sidePanelUrls.handleChange}
            state={sidePanelUrls.getValidationState()}
            errorMessage={sidePanelUrls.errorMessage}
            hintText={
              <>
                The URL(s) used for Side Panels (if your app uses this feature).
                Separate URLs with newlines. They must be valid URLs that start
                with https:// and can contain wildcard patterns.{' '}
                <a
                  target="_blank"
                  href="https://developer.helpscout.com/apps/guides/working-with-side-panel/"
                  rel="noreferrer"
                >
                  More Information
                </a>
              </>
            }
          />
        </FormLabel>
      </FormGroup>
    </Form>
  )
}

export default AppInformationForm
