import { useState } from 'react'

import { useGlobals } from 'shared/components/HsApp/HsApp.utils'

import {
  AppCardUI,
  CheckmarkUI,
  IconUI,
  ImageUI,
  OverlayTextUI,
  PremiumBadgeUI,
  TextUI,
  TitleUI,
} from './AppCard.css'

import Link from 'hsds/components/link'
import Truncate from 'hsds/components/truncate'
import CheckmarkSmall from 'hsds/icons/check'
import ConditionalWrapper from 'shared/components/ConditionalWrapper'

const AppCard = ({
  imageSrc,
  imageAlt,
  overlayText,
  withPremiumBadge = false,
  withInstalledCheckmark = false,
  linkHref = '',
  title = '',
}: {
  imageSrc: string
  imageAlt: string
  overlayText: string
  withPremiumBadge?: boolean
  withInstalledCheckmark?: boolean
  linkHref?: string
  title?: string
}) => {
  const { imagePath } = useGlobals()

  const [isImageError, setIsImageError] = useState(false)

  if (isImageError) {
    imageSrc = `${imagePath}apps/cover.png`
  }

  const handleImageError = () => {
    setIsImageError(true)
  }

  return (
    <AppCardUI>
      <ConditionalWrapper
        condition={Boolean(linkHref)}
        wrapper={children => (
          <Link href={linkHref} noUnderline>
            {children}
          </Link>
        )}
      >
        <>
          {withPremiumBadge && (
            <PremiumBadgeUI status="info">Plus</PremiumBadgeUI>
          )}
          {overlayText && (
            <OverlayTextUI>
              <Truncate type="end" limit={54}>
                {overlayText}
              </Truncate>
            </OverlayTextUI>
          )}
          {withInstalledCheckmark && (
            <CheckmarkUI status="success" title="This app is installed">
              <IconUI icon={CheckmarkSmall} size={14} />
            </CheckmarkUI>
          )}
          <ImageUI src={imageSrc} alt={imageAlt} onError={handleImageError} />
          {title && (
            <TitleUI>
              <TextUI truncate={true} size={13}>
                {title}
              </TextUI>
            </TitleUI>
          )}
        </>
      </ConditionalWrapper>
    </AppCardUI>
  )
}

export default AppCard
