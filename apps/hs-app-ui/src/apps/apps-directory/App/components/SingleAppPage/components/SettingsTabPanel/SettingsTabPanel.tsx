import useGetMailboxes from '../../hooks/useGetMailboxes'
import useInstallApp from '../../hooks/useInstallApp'
import { useHsAppContext, useUrlSearchParams } from 'shared/hooks'

import SettingsInstallationSection from './components/SettingsInstallationSection/SettingsInstallationSection'
import Accordion from 'hsds/components/accordion'
import Button from 'hsds/components/button'
import ConditionalWrapper from 'shared/components/ConditionalWrapper'
import Loader from 'shared/components/Loader'
import {
  AppsPlatformApplication,
  AppsPlatformInstallation,
} from 'shared/types/apps/AppsPlatformApp'

const SettingsTabPanel = ({
  application,
  installations,
}: {
  application: AppsPlatformApplication
  installations: AppsPlatformInstallation[]
}) => {
  const searchParams = useUrlSearchParams()
  const { mailboxes, isLoading } = useGetMailboxes()

  const {
    hsGlobal: { companyId, memberId },
  }: {
    hsGlobal: {
      companyId: number
      memberId: number
    }
  } = useHsAppContext()

  const { isInstalling, installApp } = useInstallApp(
    application,
    companyId,
    memberId
  )

  if (isLoading) {
    return <Loader />
  }

  if (installations.length === 0) {
    return null
  }

  const installationIds: string[] = []
  installations.forEach(installation => {
    installationIds.push(...installation.installationIds)
  })

  const openSectionIds: string[] = []
  const activeInstallationId = searchParams.get('ins')
  if (activeInstallationId) {
    openSectionIds.push(`settings-${activeInstallationId}`)
  }

  let index = 0

  const handleNewClick = () => {
    if (application.externalInstallationUrl) {
      window.location.assign(application.externalInstallationUrl)
    } else {
      installApp()
    }
  }

  return (
    <>
      {installationIds.length > 1 && (
        <Button size="sm" onClick={() => !isInstalling && handleNewClick()}>
          New Account
        </Button>
      )}
      <ConditionalWrapper
        condition={installationIds.length > 1}
        wrapper={children => (
          <Accordion
            allowMultiple={true}
            openSectionIds={openSectionIds}
            isPage={true}
            isSeamless={true}
            size="lg"
          >
            {children}
          </Accordion>
        )}
      >
        <>
          {installationIds.map(installationId => {
            index++

            return (
              <SettingsInstallationSection
                key={index}
                application={application}
                installationId={installationId}
                index={index}
                hasMultipleInstallations={installationIds.length > 1}
                mailboxes={mailboxes}
              />
            )
          })}
        </>
      </ConditionalWrapper>
    </>
  )
}

export default SettingsTabPanel
