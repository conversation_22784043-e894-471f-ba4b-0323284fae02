import useInstallApp from '../../../../../../hooks/useInstallApp'
import { useHsAppContext } from 'shared/hooks'

import { ButtonUI } from '../../AppInfo.css'

import Tooltip from 'hsds/components/tooltip'
import ConditionalWrapper from 'shared/components/ConditionalWrapper'
import { AppsPlatformApplication } from 'shared/types/apps/AppsPlatformApp'

const InstallButton = ({
  application,
}: {
  application: AppsPlatformApplication
}) => {
  const {
    appData: { isAdmin },
    hsGlobal: { companyId, memberId },
  }: {
    appData: { isAdmin?: boolean }
    hsGlobal: {
      companyId: number
      memberId: number
    }
  } = useHsAppContext()

  const { isInstalling, installApp } = useInstallApp(
    application,
    companyId,
    memberId
  )

  const { externalInstallationUrl, name } = application

  return (
    <ConditionalWrapper
      condition={!isAdmin}
      wrapper={children => (
        <Tooltip
          placement="right"
          title="You don't have permission to install or manage apps. Please contact your account admin for more information."
        >
          {children}
        </Tooltip>
      )}
    >
      {externalInstallationUrl != undefined ? (
        <ButtonUI
          disabled={isInstalling || !isAdmin}
          href={externalInstallationUrl}
        >
          Install from {name}
        </ButtonUI>
      ) : (
        <ButtonUI
          disabled={isInstalling || !isAdmin}
          onClick={() => installApp()}
        >
          Install App
        </ButtonUI>
      )}
    </ConditionalWrapper>
  )
}

export default InstallButton
