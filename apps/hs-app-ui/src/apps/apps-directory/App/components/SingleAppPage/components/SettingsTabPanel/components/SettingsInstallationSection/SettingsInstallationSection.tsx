import useGetSettings from 'apps/apps-directory/App/components/SingleAppPage/hooks/useGetSettings'

import {
  SettingsAccordionTitleHeadingUI,
  SettingsAccordionTitleUI,
} from './SettingsInstallationSection.css'

import SettingsForm from '../SettingsForm/SettingsForm'
import Accordion from 'hsds/components/accordion'
import ConditionalWrapper from 'shared/components/ConditionalWrapper'
import Loader from 'shared/components/Loader'
import { AppsPlatformApplication } from 'shared/types/apps/AppsPlatformApp'
import { Mailbox } from 'shared/types/mailbox/Mailbox'

const SettingsInstallationSection = ({
  application,
  installationId,
  index,
  hasMultipleInstallations,
  mailboxes,
}: {
  application: AppsPlatformApplication
  installationId: string
  index: number
  hasMultipleInstallations: boolean
  mailboxes: Mailbox[]
}) => {
  const { settings, isLoading } = useGetSettings(installationId)

  if (isLoading || !settings || !mailboxes) {
    return <Loader />
  }

  return (
    <ConditionalWrapper
      condition={hasMultipleInstallations}
      wrapper={children => (
        <Accordion.Section
          id={`settings-${installationId}`}
          key={installationId}
        >
          <SettingsAccordionTitleUI
            data-section-id={`settings-${installationId}`}
          >
            <SettingsAccordionTitleHeadingUI size="h3" selector="h3">
              {settings?.customSettings?.installationLabel ||
                `Installation ${index}`}
            </SettingsAccordionTitleHeadingUI>
          </SettingsAccordionTitleUI>
          <Accordion.Body>{children}</Accordion.Body>
        </Accordion.Section>
      )}
    >
      <SettingsForm
        installationId={installationId}
        hasMultipleInstallations={hasMultipleInstallations}
        application={application}
        settings={settings}
        mailboxes={mailboxes}
      />
    </ConditionalWrapper>
  )
}

export default SettingsInstallationSection
