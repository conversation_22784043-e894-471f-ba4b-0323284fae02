import { noop } from 'lodash'

import useUninstallApp from '../../../../hooks/useUninstallApp'
import { useHsAppContext, useNoty } from 'shared/hooks'

import { ButtonUI } from '../../AppInfo.css'

import Tooltip from 'hsds/components/tooltip'
import ConditionalWrapper from 'shared/components/ConditionalWrapper'
import {
  AppsPlatformApplication,
  AppsPlatformInstallation,
} from 'shared/types/apps/AppsPlatformApp'

const UninstallButton = ({
  application,
  installations,
}: {
  application: AppsPlatformApplication
  installations: AppsPlatformInstallation[]
}) => {
  const {
    appData: { isAdmin },
  } = useHsAppContext()

  const { showConfirmNoty } = useNoty()

  const { isUninstalling, uninstallApp } = useUninstallApp(installations)

  const installationIds: string[] = []
  installations.forEach(installation => {
    installationIds.push(...installation.installationIds)
  })

  const hasMultipleInstallations = installationIds.length > 1

  const confirmationMessage = hasMultipleInstallations
    ? `All accounts will be removed, are you sure you want to continue?`
    : ''

  function handleClick() {
    showConfirmNoty(`Uninstall ${application.name}?`, {
      body: confirmationMessage,
      primaryButtonText: 'Uninstall',
      danger: true,
      onClose: noop,
      closeOnConfirm: true,
      onConfirm: () => uninstallApp(),
    })
  }

  return (
    <ConditionalWrapper
      condition={!isAdmin}
      wrapper={children => (
        <Tooltip
          placement="right"
          title="You don't have permission to install or manage apps. Please contact your account admin for more information."
        >
          {children}
        </Tooltip>
      )}
    >
      <ButtonUI
        outlined
        color="grey"
        onClick={handleClick}
        disabled={isUninstalling || !isAdmin}
      >
        Uninstall App
      </ButtonUI>
    </ConditionalWrapper>
  )
}

export default UninstallButton
