import { render, screen } from '@testing-library/react'

import appListingFactory from 'apps/apps-directory/utils/factories/appListing'

import useGetAppListings from './hooks/useGetAppListings'

import AppsList from './AppsList'
import HsApp from 'shared/components/HsApp/HsApp'

const appThatMatchesFilter = appListingFactory()
const anotherApp = appListingFactory()
const anotherAppThatMatchesFilter = appListingFactory()

jest.mock('./hooks/useGetAppListings', () => ({
  __esModule: true,
  default: jest.fn(),
}))

jest.mock('./hooks/useFilterAppListings', () => ({
  __esModule: true,
  default: jest.fn(() => ({
    filteredAppListings: [appThatMatchesFilter, anotherAppThatMatchesFilter],
    filterAppListings: jest.fn(),
  })),
}))

const renderComponent = () =>
  render(
    <HsApp>
      <AppsList />
    </HsApp>
  )

describe('AppsList', () => {
  beforeEach(() => {
    window.appData.imagePath = 'https://legacy-example.com/'
    window.hsGlobal.imagePath = 'https://example.com/'
  })

  afterEach(() => {
    // eslint-disable-next-line @typescript-eslint/no-extra-semi
    ;(useGetAppListings as jest.Mock).mockReset()
  })

  it('renders the header and app cards', () => {
    ;(useGetAppListings as jest.Mock).mockReturnValue({
      appListings: [
        appThatMatchesFilter,
        anotherApp,
        anotherAppThatMatchesFilter,
      ],
      isLoading: false,
    })

    renderComponent()

    // Assert that the header is rendered
    expect(screen.getByText('Apps Directory')).toBeInTheDocument()

    // Assert that the app cards are rendered
    expect(screen.getByText(appThatMatchesFilter.subtitle)).toBeInTheDocument()
    expect(
      screen.getByText(anotherAppThatMatchesFilter.subtitle)
    ).toBeInTheDocument()

    // Assert that app cards that do not match the filter are not rendered
    expect(screen.queryByText(anotherApp.subtitle)).not.toBeInTheDocument()
  })

  it('renders the loader when isLoading is true', () => {
    // Mock the useGetAppListings hook to return isLoading as true
    ;(useGetAppListings as jest.Mock).mockReturnValue({
      appListings: [],
      isLoading: true,
    })

    renderComponent()

    // Assert that the loader is rendered
    expect(screen.getByTestId('loader')).toBeInTheDocument()
  })

  it('renders the error bear when isError is true', () => {
    // Mock the useGetAppListings hook to return isError as true
    ;(useGetAppListings as jest.Mock).mockReturnValue({
      appListings: [],
      isError: true,
    })

    renderComponent()

    // Assert that the error message is rendered
    expect(screen.getByText('Oops. Something went wrong.')).toBeInTheDocument()
  })
})
