import { use<PERSON><PERSON>back, useEffect, useMemo, useRef, useState } from 'react'

import { shouldIgnoreHotkey } from '../../../../TagTable.utils'
import unescapeTagName from 'apps/tags/screens/tags-list/utils/unescapeTagName'
import { findLastFocusableNode } from 'hsds/utils/focus'

import useTagManagerContext from 'apps/tags/screens/tags-list/hooks/useTagManagerContext'
import useTagMutators from 'apps/tags/screens/tags-list/hooks/useTagMutators'
import { useClickHotkeys } from 'shared/hooks'

import { SCOPE_TAGS_LIST } from '../../../../../../../../../../common/constants'
import { TAGS_HOTKEYS, TAG_COLOR_MAP } from 'apps/tags/common/constants'

import ColorField from '../ColorField/ColorField'
import NameField from '../NameField/NameField'
import FloatingToolbar from 'hsds/components/floating-toolbar'
import Tag from 'hsds/icons/tag'

const simulateDocumentClick = () => {
  // This component disables automatic closing of the droplist on-select
  // so that focusing the text box, button etc. still works. So we can close
  // the droplist when needed by simulating a click outside of it.
  document.body.dispatchEvent(new Event('mousedown'))
}

const tagsColorsToRender = Object.keys(TAG_COLOR_MAP).filter(
  color => color !== 'none'
)

const BulkEdit = ({ clearSearch }: { clearSearch?: () => void }) => {
  const { selectedRows, selectedRowsData, getComponentRef } =
    useTagManagerContext()
  const { renameSelectedTag, changeSelectedTagsColor } = useTagMutators()
  const [isCustomListOpen, setIsCustomListOpen] = useState(false)

  // By default when tab is pressed, the FloatingToolbar will intercept it and focus
  // the next button. But we want to be able to focus through the fields in the menu.
  // We'll listen for tab keydowns and stop them propagating, but if we're on the last
  // focusable node in the menu, just let the FloatingToolbar intercept it.
  const handleKeyDown = useCallback((event: KeyboardEvent) => {
    // eslint-disable-next-line @typescript-eslint/no-unsafe-call
    const lastFocusableNode = findLastFocusableNode(
      editMenuRef.current?.popper
    ) as HTMLElement

    if (event.key === 'Tab') {
      if (lastFocusableNode !== document.activeElement) event.stopPropagation()
    }
  }, [])

  useEffect(() => {
    const currentEditMenuRef = editMenuRef.current?.popper as HTMLElement

    if (!currentEditMenuRef) return

    // Need to handle the keydown event in capture phase to stop the tab keydown.
    currentEditMenuRef.addEventListener('keydown', handleKeyDown, {
      capture: true,
    })

    return () => {
      currentEditMenuRef.removeEventListener('keydown', handleKeyDown, {
        capture: true,
      })
    }
  }, [handleKeyDown])

  const menuItems = useMemo(
    () =>
      selectedRows?.length === 0
        ? []
        : [
            ...(selectedRows?.length === 1
              ? [
                  {
                    type: 'inert',
                    label: 'tag_name',
                  },
                ]
              : []),
            {
              type: 'inert',
              label: 'tag_color',
            },
          ],
    [selectedRows]
  )

  const editMenuRef = useRef<HTMLElement & { popper?: HTMLElement }>()

  const onSubmitName = useCallback(
    async (name: string) => {
      if (!name.trim()) return
      await renameSelectedTag(name)

      simulateDocumentClick()
    },
    [renameSelectedTag]
  )

  const onSelectColor = useCallback(
    async (color: string) => {
      await changeSelectedTagsColor(color)
      clearSearch && clearSearch()

      simulateDocumentClick()
    },
    [changeSelectedTagsColor, clearSearch]
  )

  const [buttonRef] = useClickHotkeys(
    TAGS_HOTKEYS.EDIT,
    {
      // this is required for the hotkey to work when the focus is on the checkbox
      ignoredElementWhitelist: ['INPUT'],
      enabled: selectedRowsData.length,
      scopes: [SCOPE_TAGS_LIST],
    },
    shouldIgnoreHotkey
  )

  if (buttonRef.current) {
    getComponentRef('bulkEdit', buttonRef.current)
  }

  const renderCustomListItem = ({
    item,
  }: {
    item: {
      label?: string
    }
  }): JSX.Element | null => {
    if (!isCustomListOpen) return null
    if (item.label === 'tag_name') {
      const currentName = selectedRowsData.at(0)?.name
      return (
        <NameField
          placeholder="New Name"
          ariaLabel="Tag name"
          buttonAriaLabel="Rename tag"
          currentName={unescapeTagName(currentName) as string}
          // eslint-disable-next-line @typescript-eslint/no-misused-promises
          onSubmit={onSubmitName}
          inputRefName="bulkEditInput"
        />
      )
    } else if (item.label === 'tag_color') {
      return (
        <ColorField
          onSelectColor={onSelectColor}
          tagsColorsToRender={tagsColorsToRender}
        />
      )
    }
    return null
  }

  return (
    <FloatingToolbar.DropList
      icon={Tag}
      items={menuItems}
      tooltipHotkey={TAGS_HOTKEYS.EDIT.toUpperCase()}
      tooltipTitle="Edit"
      aria-label="Edit tag"
      renderCustomListItem={renderCustomListItem}
      menuWidth="100%"
      closeOnSelection={false}
      getTippyInstance={(instance: HTMLElement) =>
        (editMenuRef.current = instance)
      }
      togglerProps={{
        ref: buttonRef,
      }}
      onMenuFocus={() => {
        setIsCustomListOpen(true)
      }}
    />
  )
}

export default BulkEdit
