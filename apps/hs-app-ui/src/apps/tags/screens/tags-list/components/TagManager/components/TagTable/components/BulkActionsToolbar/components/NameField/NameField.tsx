import { MutableRefObject, memo, useEffect, useRef } from 'react'

import useTagManagerContext from 'apps/tags/screens/tags-list/hooks/useTagManagerContext'

import { ButtonUI, InputUI, NameFieldUI } from './NameField.css'

import Icon from 'hsds/components/icon'
import Checkmark from 'hsds/icons/check'

const NameField = memo(
  ({
    name = 'name',
    placeholder,
    ariaLabel,
    buttonAriaLabel,
    currentName,
    onSubmit,
    inputRefName,
  }: {
    name?: string
    placeholder: string
    ariaLabel: string
    buttonAriaLabel: string
    currentName?: string
    onSubmit: (newName: string) => void
    inputRefName?: string
  }) => {
    const { getComponentRef } = useTagManagerContext()
    const inputRef = useRef<HTMLInputElement>(
      null
    ) as MutableRefObject<HTMLInputElement>

    useEffect(() => {
      // Since our input is inside a menu controlled by hsds/downshift, spacebar events
      // are captured as 'select', and won't show as a space in the input. So to get around
      // that, we'll capture space events here and stop them from propagating.
      const eventHandler = (event: KeyboardEvent) => {
        if (event.code === 'Space') event.stopPropagation()

        if (event.code === 'Enter') onSubmit(inputRef.current.value)
      }

      if (inputRef.current) {
        inputRef.current.addEventListener('keydown', eventHandler)
      }

      return () => {
        if (inputRef.current) {
          inputRef.current.removeEventListener('keydown', eventHandler)
        }
      }
    }, [onSubmit])

    return (
      <NameFieldUI>
        <InputUI
          name={name}
          placeholder={placeholder}
          aria-label={ariaLabel}
          // eslint-disable-next-line jsx-a11y/no-autofocus
          autoFocus={true}
          forceAutoFocusTimeout={100}
          action={
            <ButtonUI
              size="sm"
              onClick={() => {
                if (!inputRef.current) return
                onSubmit(inputRef.current?.value)
              }}
              aria-label={buttonAriaLabel}
            >
              <Icon icon={Checkmark} size={24} inline />
            </ButtonUI>
          }
          value={currentName}
          innerRef={(node: HTMLInputElement | HTMLTextAreaElement | null) => {
            if (inputRefName) {
              getComponentRef(inputRefName, node as HTMLInputElement)
            }
            inputRef.current = node as HTMLInputElement
          }}
        />
      </NameFieldUI>
    )
  }
)

NameField.displayName = 'NameField'

export default NameField
