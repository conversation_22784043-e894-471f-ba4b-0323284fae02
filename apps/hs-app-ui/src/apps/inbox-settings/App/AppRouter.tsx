import {
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  MemoryRouter,
  Redirect,
  Route,
  Switch,
} from 'react-router-dom'

import { STEPS } from '@helpscout/inbox-setup-modals/src/common/constants'
import InboxSetup from '@helpscout/inbox-setup-modals/src/screens/inbox-setup/InboxSetup'

import { isTestEnv } from 'shared/utils/env'

import { Unauthorized } from '../common/components/Unauthorized'
import Layout from '../common/layout/Layout'
import { AutoReply } from '../screens/auto-reply/AutoReply'
import { CustomFields } from '../screens/custom-fields/CustomFields'
import { EditInbox } from '../screens/edit-inbox/EditInbox'
import Inboxes from '../screens/inboxes/Inboxes'
import { OutgoingEmail } from '../screens/outgoing-email/OutgoingEmail'
import { Permissions } from '../screens/permissions/Permissions'
import { Routing } from '../screens/routing/Routing'
import { SatisfactionRating } from '../screens/satisfaction-ratings/SatisfactionRatings'
import { SavedReplies } from '../screens/saved-replies/SavedReplies'

const basename = '/settings'

function Router({ children }: { children: JSX.Element }) {
  if (isTestEnv()) {
    return <MemoryRouter>{children}</MemoryRouter>
  }
  return <BrowserRouter basename={basename}>{children}</BrowserRouter>
}

function AppRouter() {
  return (
    <Router>
      <Switch>
        <Route path="/unauthorized" exact component={Unauthorized} />
        <Route
          path={['/inboxes', '/mailboxes']}
          exact
          component={() => (
            <>
              <Inboxes />
              <InboxSetup
                initialStep={STEPS.CREATE_MAILBOX}
                redirectOnClose={true}
              />
            </>
          )}
        />

        {['/inbox/:inboxId', '/mailbox/:inboxId'].map(basePath => (
          <Route path={basePath} key={basePath}>
            <Layout>
              <Switch>
                <Route
                  exact
                  path={`${basePath}`}
                  component={() => (
                    <>
                      <EditInbox />
                      <InboxSetup initialStep={STEPS.FORWARDER_CONNECT_EMAIL} />
                    </>
                  )}
                />
                <Route
                  path={`${basePath}/saved-replies/:savedReplyId?`}
                  component={SavedReplies}
                />
                <Route
                  exact
                  path={`${basePath}/custom-fields/:customFieldId?`}
                  component={CustomFields}
                />
                <Route
                  exact
                  path={`${basePath}/outgoing-email`}
                  component={() => (
                    <>
                      <OutgoingEmail />
                      <InboxSetup initialStep={STEPS.FORWARDER_CONNECT_EMAIL} />
                    </>
                  )}
                />
                <Route
                  exact
                  path={`${basePath}/permissions`}
                  component={Permissions}
                />
                <Route path={`${basePath}/routing`} component={Routing} />
                <Route path={`${basePath}/auto-reply`} component={AutoReply} />
                <Route
                  path={`${basePath}/satisfaction-ratings`}
                  component={SatisfactionRating}
                />
                <Route
                  path={`${basePath}/satisfaction-translations`}
                  component={SatisfactionRating}
                />
              </Switch>
            </Layout>
          </Route>
        ))}
        {/* legacy routes */}
        <Redirect
          from="/saved-replies/:inboxId"
          to="/inbox/:inboxId/saved-replies"
        />
        <Redirect
          from="/custom-fields/:inboxId"
          to="/inbox/:inboxId/custom-fields"
        />
        <Redirect
          from="/outgoing-email/:inboxId"
          to="/inbox/:inboxId/outgoing-email"
        />
        <Redirect
          from="/permissions/:inboxId"
          to="/inbox/:inboxId/permissions"
        />
        <Redirect from="/routing/:inboxId" to="/inbox/:inboxId/routing" />
      </Switch>
    </Router>
  )
}

export default AppRouter
