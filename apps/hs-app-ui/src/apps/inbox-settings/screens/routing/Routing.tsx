import React from 'react'

import {
  SettingsPage,
  SettingsPageCard,
} from 'apps/inbox-settings/common/components/SettingsPage/SettingsPage'
import Page from 'hsds/components/page'

export function Routing() {
  return (
    <SettingsPage>
      <SettingsPageCard>
        <Page.Header
          data-testid="PageLayout.Header"
          render={({
            Subtitle,
            Title,
          }: {
            Subtitle: React.ElementType
            Title: React.ElementType
          }) => (
            <>
              <Title>Routing</Title>
              <Subtitle>
                Automatically assign conversations to the most available
                teammate.
              </Subtitle>
            </>
          )}
          withBorder={true}
        />
      </SettingsPageCard>
    </SettingsPage>
  )
}
