import styled from 'styled-components'

import { getColor } from 'hsds/utils/color'

import ButtonIcon from 'hsds/components/button-icon'
import Icon from 'hsds/components/icon'
import Input from 'hsds/components/input'
import Pagination from 'hsds/components/pagination'

export const SearchInputUI = styled(Input)<{ $hasSearchTerm?: boolean }>`
  box-sizing: content-box;
  margin-bottom: 48px;
  max-width: 100%;
  padding-right: 6px;
  margin-top: -3px;

  .c-Input__item.is-suffix {
    height: 24px;
    opacity: 1;
    ${({ $hasSearchTerm }) => !$hasSearchTerm && 'top: -1px'};
  }
`

/*
 * * Use `> div button` selector to target the pagination arrow buttons to offset them enough to right-align with the grid
 *
 * * Unset padding to minimize left- and right-alignment mismatch
 */
export const PaginationUI = styled(Pagination)`
  margin-top: 14px;
  padding: unset;
  margin-left: 10px;
  width: calc(100% - 24px);

  > div button {
    left: 6px;
  }
`

export const EmptyUI = styled.p`
  padding-top: 8px;
  padding-bottom: 30px;
  margin: 0;
  color: ${getColor('charcoal.600')};
`

export const IconUI = styled(Icon)`
  color: ${getColor('charcoal.500')};
`

export const ClearButtonUI = styled(ButtonIcon)`
  margin-top: -3px;
`
