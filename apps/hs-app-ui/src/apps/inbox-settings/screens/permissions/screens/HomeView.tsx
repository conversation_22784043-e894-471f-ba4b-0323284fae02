import { debounce } from 'lodash'
import { Fragment, useCallback, useEffect, useRef } from 'react'
import { useDispatch, useSelector } from 'react-redux'
import { useHistory, useLocation } from 'react-router'

import useGetSearchParams from '../hooks/useGetSearchParams/useGetSearchParams'
import useUpdateSearchParams from '../hooks/useUpdateSearchParams/useUpdateSearchParams'

import {
  PAGE_PARAM,
  PAGE_SIZE,
  SEARCH_TERM_PARAM,
} from '../constants/constants'

import {
  ClearButtonUI,
  EmptyUI,
  IconUI,
  PaginationUI,
  SearchInputUI,
} from './HomeView.css'

import { PageLayout } from '../components/PageLayout/PageLayout'
import { UserList } from '../components/UserList/UserList'
import { usersActions, usersOperations, usersSelectors } from '../state/users'
import Circle<PERSON>ross from 'hsds/icons/cross-circle'
import SearchMedium from 'hsds/icons/search'

const SEARCH_INPUT_DEBOUNCE_TIMEOUT = 300

export function HomeView() {
  const location = useLocation()
  const { search } = location
  const history = useHistory()
  const dispatch = useDispatch()

  const fetchPermissions = useCallback(
    // @ts-ignore
    // eslint-disable-next-line @typescript-eslint/no-unsafe-argument
    (...args) => dispatch(usersOperations.fetchPermissions(...args)),
    [dispatch]
  )
  const updateDisplayValues = useCallback(
    (values: number[]) => dispatch(usersActions.updateDisplayValues(values)),
    [dispatch]
  )
  const resetValues = useCallback(
    () => dispatch(usersActions.resetValues()),
    [dispatch]
  )
  const savePermissions = useCallback(
    // @ts-ignore
    // eslint-disable-next-line @typescript-eslint/no-unsafe-argument
    (...args) => dispatch(usersOperations.savePermissions(...args)),
    [dispatch]
  )
  const updateUserPermission = useCallback(
    ({ isChecked, userId }: { isChecked: boolean; userId: number }) =>
      dispatch(usersActions.updateUserPermission({ isChecked, userId })),
    [dispatch]
  )

  const currentPage = useSelector(usersSelectors.getCurrentPage)
  const displayValues = useSelector(usersSelectors.getValues)
  const isDirty = useSelector(usersSelectors.getIsDirty)
  const isInitialLoad = useSelector(usersSelectors.getIsInitialLoad)
  const isLoading = useSelector(usersSelectors.getIsLoading)
  const isSaving = useSelector(usersSelectors.getIsSaving)
  const totalPagesCount = useSelector(usersSelectors.getTotalPagesCount)
  const totalUsers = useSelector(usersSelectors.getTotalUsersCount)
  const users = useSelector(usersSelectors.getUsers)

  const IS_EMPTY = totalPagesCount === 0

  const wasSearchInputFocused = useRef(false)

  const updateSearchParams = useUpdateSearchParams(history, search)
  const loadingData = isInitialLoad || isLoading

  const getSearchPrams = useGetSearchParams(search)

  const handleOutOfBoundsRequest = useCallback(
    lastPageNumber => updateSearchParams(PAGE_PARAM, lastPageNumber, true),
    [updateSearchParams]
  )

  const { inboxNameNoun } = window.hsGlobal.features // this is related to isRenameMailboxToInboxEnabled FF

  useEffect(() => {
    // @ts-ignore
    fetchPermissions(getSearchPrams(), handleOutOfBoundsRequest)
  }, [fetchPermissions, getSearchPrams, handleOutOfBoundsRequest])

  useEffect(() => {
    if (!isLoading) {
      window.scrollTo(0, 0)
    }
  }, [isLoading])

  function handleChangeActivePage(page: number) {
    updateSearchParams(PAGE_PARAM, page)
  }

  const handleSearchTextChange = debounce(function (searchValue) {
    // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment, @typescript-eslint/no-unsafe-call, @typescript-eslint/no-unsafe-member-access
    const sanitizedSearchValue = searchValue.toLowerCase()

    updateSearchParams(SEARCH_TERM_PARAM, sanitizedSearchValue)
  }, SEARCH_INPUT_DEBOUNCE_TIMEOUT)

  function handleClearSearchTextChange() {
    updateSearchParams(PAGE_PARAM, 1, true)
    wasSearchInputFocused.current = true
  }

  function handleChangeDisplayValues(values: []) {
    updateDisplayValues(values)
  }

  function handleUserMailboxPermissionChange(data: {
    isChecked: boolean
    userId: number
  }) {
    updateUserPermission(data)
  }

  function handleSave() {
    const pageParam = getSearchPrams()[PAGE_PARAM]

    // @ts-ignore
    savePermissions({ page: pageParam })
  }

  function renderSearchInput() {
    const searchTerm = getSearchPrams()[SEARCH_TERM_PARAM]
    const disableInput = isLoading || (IS_EMPTY && !searchTerm)

    const icon = (
      <IconUI
        data-testid="SearchIcon"
        data-cy="MailboxPermissions.SearchIcon"
        // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment
        icon={SearchMedium}
        size="24"
      />
    )

    const clearButton = (
      <ClearButtonUI
        seamless
        size="sm"
        data-testid="ClearIcon"
        data-cy="MailboxPermissions.ClearIcon"
        onClick={handleClearSearchTextChange}
        // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment
        icon={CircleCross}
      />
    )

    return (
      <SearchInputUI
        aria-label="Search users"
        autoComplete="off"
        disabled={disableInput}
        $hasSearchTerm={!!searchTerm}
        inlineSuffix={searchTerm ? clearButton : icon}
        isFocused={!disableInput && wasSearchInputFocused.current}
        onBlur={() => (wasSearchInputFocused.current = false)}
        onChange={handleSearchTextChange}
        onFocus={() => (wasSearchInputFocused.current = true)}
        name="quick-search-users"
        placeholder="Quick search"
        value={searchTerm}
      />
    )
  }

  function renderUsersList() {
    return (
      <UserList
        displayValues={displayValues}
        isInitialLoad={isInitialLoad}
        // @ts-ignore
        onChangeDisplayValues={handleChangeDisplayValues}
        onUserMailboxPermissionChange={handleUserMailboxPermissionChange}
        users={users}
        // @ts-ignore
        totalUsers={totalUsers}
      />
    )
  }

  function renderPaginationUI() {
    const usersExceedPageSize = totalUsers > PAGE_SIZE

    if (IS_EMPTY || !usersExceedPageSize) {
      return null
    }

    return (
      <PaginationUI
        isLoading={isInitialLoad || isLoading || isSaving}
        data-testid="UserList-Pagination"
        activePage={currentPage}
        totalItems={totalUsers}
        rangePerPage={PAGE_SIZE}
        subject="User"
        onChange={handleChangeActivePage}
      />
    )
  }

  function renderEmptyMessage() {
    if (!loadingData && IS_EMPTY) {
      const hasSearchParams = !!getSearchPrams()[SEARCH_TERM_PARAM]

      if (hasSearchParams) {
        return <EmptyUI>No one matches your search.</EmptyUI>
      }
      return (
        <EmptyUI>{`No one is available to be added to this ${inboxNameNoun}.`}</EmptyUI>
      )
    }

    return null
  }

  return (
    <PageLayout
      subtitle={`People may be granted access to this ${inboxNameNoun} individually, or from their teams.`}
      title={`Choose who can access this ${inboxNameNoun}`}
      withHeaderBorder={false}
      isSaving={isSaving}
      isDirty={isDirty}
      isEmpty={IS_EMPTY}
      pageActions={{ save: handleSave, discard: resetValues }}
    >
      <Fragment>
        {renderSearchInput()}
        {renderUsersList()}
        {renderPaginationUI()}
        {renderEmptyMessage()}
      </Fragment>
    </PageLayout>
  )
}

export default HomeView
