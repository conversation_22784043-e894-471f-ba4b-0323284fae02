import PropTypes from 'prop-types'
import React, { Component } from 'react'

import { ENC<PERSON><PERSON>TI<PERSON>, OUTMETHOD, STATUS, STRINGS } from '../../constants'

import {
  CustomSettingsContentUI,
  CustomSettingsFormUI,
  SelectTagUI,
} from './CustomSettings.css'

import <PERSON><PERSON> from 'hsds/components/alert'
import Button from 'hsds/components/button'
import DropList from 'hsds/components/drop-list'
import FormGroup from 'hsds/components/form-group'
import FormLabel from 'hsds/components/form-label'
import Grid from 'hsds/components/grid'
import Heading from 'hsds/components/heading'
import Input from 'hsds/components/input'

type Props = {
  hasPwd: boolean
  inboxId: number
  onTestSettings: (data: any) => void
  server: string
  username: string
  port: string
  encryption: string | number
  testErrors: string[]
  testStatus: string
  testInProgress: boolean
  updateOutgoingSettings: (data: any) => void
}

type State = {
  validationErrors: { [key: string]: string | boolean }
  server: string
  username: string
  password: string
  port: string
  encryption: string | number
  showPasswordInput: boolean
  hasRunTest: boolean
}

export class OtherSMTPSettings extends Component<Props, State> {
  static propTypes = {
    hasPwd: PropTypes.bool,
    inboxId: PropTypes.number,
    onTestSettings: PropTypes.func,
    server: PropTypes.string,
    username: PropTypes.string,
    port: PropTypes.string,
    encryption: PropTypes.string,
    testErrors: PropTypes.array,
    testStatus: PropTypes.string,
    testInProgress: PropTypes.bool,
    updateOutgoingSettings: PropTypes.func,
  }

  constructor(props: Props) {
    super(props)
    const { server, username, port, encryption, hasPwd } = props

    this.state = {
      validationErrors: {},
      server: server ? server : '',
      username: username ? username : '',
      password: '',
      port: port ? port : '',
      encryption: encryption ? encryption : ENCRYPTION.NONE,
      showPasswordInput: !hasPwd,
      hasRunTest: false,
    }
  }

  validateForm() {
    const { hasPwd } = this.props
    const { server, username, password, port, validationErrors } = this.state
    const updatedValidationErrors = Object.assign({}, validationErrors)

    const serverErrors = this.updateFieldValidation(
      'server',
      server,
      updatedValidationErrors
    )
    const usernameErrors = this.updateFieldValidation(
      'username',
      username,
      updatedValidationErrors
    )
    let passwordErrors = false
    if (!hasPwd && !password) {
      passwordErrors = this.updateFieldValidation(
        'password',
        password,
        updatedValidationErrors
      )
    }
    const portErrors = this.updateFieldValidation(
      'port',
      port,
      updatedValidationErrors
    )

    this.setState({
      validationErrors: updatedValidationErrors,
    })

    return serverErrors || usernameErrors || passwordErrors || portErrors
  }

  updateFieldValidation(
    field: string,
    value: string,
    validationErrors: State['validationErrors']
  ) {
    const { hasPwd } = this.props
    let hasErrors = false
    switch (field) {
      case 'server':
        validationErrors.google = false
        if (value.length <= 0) {
          validationErrors[field] = STRINGS.OTHER_SERVER_VALIDATION
          hasErrors = true
        } else if (
          value.indexOf('gmail.com') > 0 ||
          value.indexOf('google.com') > 0
        ) {
          validationErrors.google = STRINGS.OTHER_SERVER_GOOGLE_VALIDATION
          hasErrors = true
        }
        break
      case 'username':
        if (value.length <= 0) {
          validationErrors[field] = STRINGS.OTHER_USER_VALIDATION
          hasErrors = true
        }
        break
      case 'password':
        if (value.length <= 0 && !hasPwd) {
          validationErrors[field] = STRINGS.OTHER_PASSWORD_VALIDATION
          hasErrors = true
        }
        break
      case 'port':
        if (value.length <= 0) {
          validationErrors[field] = STRINGS.OTHER_PORT_VALIDATION
          hasErrors = true
        }
        break
      default:
        break
    }
    return hasErrors
  }

  handleUpdate = (field: string, value: string) => {
    const { updateOutgoingSettings } = this.props
    let newValue = value ? value.trim() : ''

    if (field === 'server') {
      newValue = newValue.toLowerCase()
    }

    const updates: { [key: string]: any } = {}
    updates[field] = newValue

    // reset validation of this field, and re-run the validation check
    const validationErrors = Object.assign({}, this.state.validationErrors)
    validationErrors[field] = false
    this.updateFieldValidation(field, newValue, validationErrors)
    updates.validationErrors = validationErrors

    // @ts-ignore
    this.setState(updates, () => {
      updateOutgoingSettings({
        ...updates,
        outMethod: OUTMETHOD.CUSTOM,
      })
    })
  }

  handleTest = (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault()

    const hasErrors = this.validateForm()

    if (!hasErrors) {
      const { onTestSettings, inboxId } = this.props
      const { server, username, password, port, encryption } = this.state

      this.setState({ hasRunTest: true })
      onTestSettings({
        inboxId,
        data: {
          server,
          username,
          password,
          port,
          security: encryption,
        },
      })
    }
  }

  handleEncryptionChange = (value: string) => {
    const { updateOutgoingSettings } = this.props
    this.setState(
      {
        encryption: value,
      },
      () => {
        updateOutgoingSettings({
          encryption: this.state.encryption,
          outMethod: OUTMETHOD.CUSTOM,
        })
      }
    )
  }

  toggleShowPassword = () => {
    this.setState({ showPasswordInput: !this.state.showPasswordInput })
  }

  renderAlert() {
    const { testErrors, testStatus, testInProgress } = this.props
    const { hasRunTest } = this.state

    if (!hasRunTest || testInProgress) {
      return null
    }

    if (testStatus === STATUS.SUCCESS) {
      return (
        <Alert
          data-cy="OutgoingEmail.CustomSettings.OtherSMTPSettings.Alert"
          status="success"
        >
          <Heading size="h5">
            {STRINGS.OTHER_CONNECTION_SUCCESS_MESSAGE}
          </Heading>
        </Alert>
      )
    }

    let errorMessage = 'There was a problem connecting to the SMTP server.'

    if (testErrors && testErrors.length > 0) {
      errorMessage = testErrors[0]
    }

    return (
      <Alert
        data-cy="OutgoingEmail.CustomSettings.OtherSMTPSettings.Alert"
        status="error"
      >
        <Heading size="h5">{STRINGS.OTHER_CONNECTION_ERROR_MESSAGE}</Heading>
        <p>{errorMessage}</p>
      </Alert>
    )
  }

  render() {
    const { hasPwd, testInProgress } = this.props
    const {
      validationErrors,
      server,
      username,
      password,
      port,
      encryption,
      showPasswordInput,
    } = this.state

    const dropdownId = 'OutgoingEmail.OtherSMTP.SecurityDropdown'
    const selectedOption = ENCRYPTION.OPTIONS.find(
      item => item.value === encryption
    )

    return (
      <CustomSettingsContentUI>
        {this.renderAlert()}
        <CustomSettingsFormUI
          actionDirection={'left'}
          onSave={this.handleTest.bind(this)}
          saveText={
            testInProgress
              ? STRINGS.IS_TESTING_BUTTON_LABEL
              : STRINGS.TEST_CONNECTION_BUTTON_LABEL
          }
          saveButtonProps={{ size: 'md' }}
        >
          <FormGroup>
            <FormLabel label={STRINGS.OTHER_SERVER_LABEL}>
              <Input
                errorMessage={
                  validationErrors.google
                    ? validationErrors.google
                    : validationErrors.server
                }
                state={
                  validationErrors.server || validationErrors.google
                    ? 'error'
                    : 'default'
                }
                value={server}
                onChange={this.handleUpdate.bind(this, 'server')}
              />
            </FormLabel>
          </FormGroup>
          <FormGroup>
            <FormLabel label={STRINGS.OTHER_USER_LABEL}>
              <Input
                value={username}
                errorMessage={validationErrors.username}
                state={validationErrors.username ? 'error' : 'default'}
                onChange={this.handleUpdate.bind(this, 'username')}
              />
            </FormLabel>
          </FormGroup>
          <FormGroup>
            <FormLabel label="Password">
              {hasPwd && !showPasswordInput ? (
                <Button
                  size="sm"
                  outlined
                  color="grey"
                  onClick={this.toggleShowPassword}
                >
                  Edit Password
                </Button>
              ) : (
                <Input
                  type={'password'}
                  value={password}
                  errorMessage={validationErrors.password}
                  state={validationErrors.password ? 'error' : 'default'}
                  onChange={this.handleUpdate.bind(this, 'password')}
                />
              )}
            </FormLabel>
          </FormGroup>

          <FormGroup>
            <Grid>
              <Grid.Row>
                <Grid.Col size="6">
                  <FormLabel label={STRINGS.OTHER_PORT_LABEL}>
                    <Input
                      type={'number'}
                      value={port}
                      errorMessage={validationErrors.port}
                      state={validationErrors.port ? 'error' : 'default'}
                      onChange={this.handleUpdate.bind(this, 'port')}
                    />
                  </FormLabel>
                </Grid.Col>
                <Grid.Col size="6">
                  <FormLabel label={STRINGS.OTHER_SECURITY_LABEL}>
                    <DropList
                      data-cy={dropdownId}
                      id={dropdownId}
                      name={dropdownId}
                      items={ENCRYPTION.OPTIONS}
                      selection={selectedOption}
                      onSelect={({ value }: { value: string }) => {
                        this.handleEncryptionChange(value)
                      }}
                      toggler={
                        <SelectTagUI
                          text={selectedOption ? selectedOption.label : ''}
                        />
                      }
                    />
                  </FormLabel>
                </Grid.Col>
              </Grid.Row>
            </Grid>
          </FormGroup>
        </CustomSettingsFormUI>
      </CustomSettingsContentUI>
    )
  }
}

export default OtherSMTPSettings
