import { builder } from 'shared/testUtils/builder'

export const aHsGlobal = () => {
  const hsGlobal = {
    companyId: 1,
    memberId: 1,
    features: {
      isCustomFieldsEnabled: true,
      isAiDraftsHideSupportAgentEnabled: true,
      isAutoReplyEnabled: true,
      isHsAppUiMailboxRewriteEnabled: true,
      isRenameMailboxToInboxEnabled: true,
      isFancyTemplateFlagEnabled: true,
      isEditorInSavedRepliesEnabled: true,
      inboxNameNoun: 'inbox',
      inboxNamePlural: 'inboxes',
      isInlineImagesInNewSavedReplyEditorEnabled: true,
      isAiTextExpansionEnabled: true,
      isHtmlBlockComposer: true,
      isSatisfactionRatingsEnabled: true,
      isRoutingV1Enabled: true,
    },
    pusher: {
      namespace: 'foo',
      pusherAppKey: 'foo',
      pusherCluster: 'foo',
    },
    isSidenavCollapsed: false,
    memberPermissions: {
      manageMailboxSettings: true,
      manageWorkflows: true,
      manageSavedReplies: true,
      manageCustomFields: true,
      manageAccount: true,
    },
    timezone: '420',
    timeFormat: 1 as 1 | 2,
    accountDropdown: {
      isOwner: false,
    },
    planName: 'free',
  }

  return builder(hsGlobal)
}

export const anAppData = () => {
  const appData = {
    isAdmin: true,
    imagePath: '', // When in hs-app, this will be set.
    isOwner: false,
    showPromoBanner: false,
    upsells: {
      showCustomFieldsPlusPromo: false,
    },
    canAddMoreInboxes: true,
  }

  return builder(appData)
}
