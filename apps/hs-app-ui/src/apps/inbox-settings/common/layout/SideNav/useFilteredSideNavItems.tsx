import { fetchSavedReplies } from '../../../screens/saved-replies/api/api'
import { fetchAutoReply } from 'apps/inbox-settings/screens/auto-reply/api/api'
import { fetchCustomFields } from 'apps/inbox-settings/screens/custom-fields/api/api'
import { fetchSatisfactionRating } from 'apps/inbox-settings/screens/satisfaction-ratings/api/api'
import { capitalize } from 'shared/utils/StringUtils'

import { useFeatureFlagsList } from 'shared/hooks'
import { usePermission } from 'shared/hooks'
import { useHsAppContext } from 'shared/hooks'

import { SIDENAV_LABELS } from '../../constants'

import { Item } from './Item'
import ArrowSwapHorizontal from 'hsds/icons/arrow-swap-horizontal'
import Workflow from 'hsds/icons/bolt'
import ChatSquareCheck from 'hsds/icons/chat-square-check'
import Fields from 'hsds/icons/fields'
import InboxArrowUp from 'hsds/icons/inbox-arrow-up'
import InboxEdit from 'hsds/icons/inbox-edit'
import LockClosed from 'hsds/icons/lock-closed'
import PencilSparkle from 'hsds/icons/pencil-sparkle'
import SavedReplyIcon from 'hsds/icons/saved-reply'
import ThumbsUp from 'hsds/icons/thumbs-up'
import { LegacyUpsellBadge } from 'shared/components/LegacyUpsellBadge/LegacyUpsellBadge'

export function useFilteredSideNavItems(inboxId: string) {
  const {
    isAiDraftsHideSupportAgentEnabled,
    isEligibleForSaiMigration,
    isAutoReplyEnabled,
    inboxNameNoun,
    isSatisfactionRatingsEnabled,
    isRoutingV1Enabled,
  } = useFeatureFlagsList()

  const {
    appData: { upsells = {} },
  } = useHsAppContext()

  const canManageMailboxSettings = usePermission('manageMailboxSettings')
  const canManageWorkflows = usePermission('manageWorkflows')
  const canManageSavedReplies = usePermission('manageSavedReplies')

  const items = []

  if (canManageMailboxSettings) {
    // eslint-disable-next-line @typescript-eslint/no-unsafe-call
    const label = `Edit ${capitalize(inboxNameNoun as string) || 'Inbox'}`
    items.push(
      <Item
        path={`/${inboxNameNoun}/${inboxId}`}
        label={label}
        icon={InboxEdit}
        exact
        key={label}
        queryKey="inboxSettings"
      />
    )
  }

  if (canManageWorkflows) {
    items.push(
      <Item
        path={`/settings/workflows/${inboxId}/`}
        label={SIDENAV_LABELS.WORKFLOWS}
        icon={Workflow}
        isExternal={true}
        key={SIDENAV_LABELS.WORKFLOWS}
        queryKey="workflows"
      />
    )
  }

  if (isRoutingV1Enabled) {
    items.push(
      <Item
        path={`/${inboxNameNoun}/${inboxId}/routing`}
        label={SIDENAV_LABELS.ROUTING}
        icon={ArrowSwapHorizontal}
        key={SIDENAV_LABELS.ROUTING}
        queryKey="routing"
      />
    )
  }

  if (canManageSavedReplies) {
    items.push(
      <Item
        path={`/${inboxNameNoun}/${inboxId}/saved-replies`}
        label={SIDENAV_LABELS.SAVED_REPLIES}
        icon={SavedReplyIcon}
        key={SIDENAV_LABELS.SAVED_REPLIES}
        queryKey="savedReplies"
        prefetchFunction={() => fetchSavedReplies(parseInt(inboxId))}
      />
    )
  }

  if (canManageMailboxSettings) {
    items.push(
      <Item
        path={`/${inboxNameNoun}/${inboxId}/custom-fields`}
        label={SIDENAV_LABELS.CUSTOM_FIELDS}
        icon={Fields}
        key={SIDENAV_LABELS.CUSTOM_FIELDS}
        queryKey="customFields"
        prefetchFunction={() => fetchCustomFields(parseInt(inboxId))}
        badge={upsells.showCustomFieldsPlusPromo && <LegacyUpsellBadge />}
      />
    )
  }

  if (
    (isAiDraftsHideSupportAgentEnabled || isEligibleForSaiMigration) &&
    canManageMailboxSettings
  ) {
    items.push(
      <Item
        path={`/settings/ai-drafts/${inboxId}`}
        label={SIDENAV_LABELS.AI_DRAFTS}
        icon={PencilSparkle}
        isExternal={true}
        key={SIDENAV_LABELS.AI_DRAFTS}
        queryKey="aiDrafts"
      />
    )
  }

  if (canManageMailboxSettings) {
    items.push(
      <Item
        path={`/${inboxNameNoun}/${inboxId}/outgoing-email`}
        label={SIDENAV_LABELS.OUTGOING_EMAIL}
        icon={InboxArrowUp}
        key={SIDENAV_LABELS.OUTGOING_EMAIL}
        queryKey="outgoingEmail"
      />,
      <Item
        path={`/${inboxNameNoun}/${inboxId}/permissions`}
        label={SIDENAV_LABELS.PERMISSIONS}
        icon={LockClosed}
        key={SIDENAV_LABELS.PERMISSIONS}
        queryKey="permissions"
      />
    )
  }

  if (isAutoReplyEnabled && canManageMailboxSettings) {
    items.push(
      <Item
        path={`/${inboxNameNoun}/${inboxId}/auto-reply`}
        label={SIDENAV_LABELS.AUTO_REPLY}
        icon={ChatSquareCheck}
        key={SIDENAV_LABELS.AUTO_REPLY}
        queryKey="autoReply"
        prefetchFunction={() => fetchAutoReply(inboxId)}
      />
    )
  }

  if (canManageMailboxSettings && isSatisfactionRatingsEnabled) {
    items.push(
      <Item
        path={`/${inboxNameNoun}/${inboxId}/satisfaction-ratings`}
        label={SIDENAV_LABELS.SATISFACTION_RATINGS}
        icon={ThumbsUp}
        key={SIDENAV_LABELS.SATISFACTION_RATINGS}
        queryKey="satisfactionRatings"
        prefetchFunction={() => fetchSatisfactionRating(inboxId)}
      />
    )
  }

  return items
}
