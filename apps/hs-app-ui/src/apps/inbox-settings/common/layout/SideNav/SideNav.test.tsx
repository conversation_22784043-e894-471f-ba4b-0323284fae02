import nock from 'nock'

import { screen, waitFor } from '@testing-library/react'
import userEvent from '@testing-library/user-event'

import { aMailbox } from 'apps/inbox-settings/devtools/apiResponseBuilders'

import { SIDENAV_LABELS } from '../../constants'

import { SideNav } from './SideNav'
import { Mailbox } from 'apps/inbox-settings/common/types'
import { mockMailboxesResponse } from 'apps/inbox-settings/devtools/nocks'
import { renderWithProviders } from 'apps/inbox-settings/devtools/renderWithProviders'
import {
  aHsGlobal,
  anAppData,
} from 'apps/inbox-settings/devtools/windowDataTestBuilders'
import { waitForRequestsToFinish } from 'shared/testUtils/async-utils'

const baseHsGlobal = aHsGlobal().build()
const baseAppData = anAppData().build()

const assertLinkExists = (label: string) => {
  expect(screen.getByRole('link', { name: label })).toBeInTheDocument()
}

const assertLinkDoesNotExist = (label: string) => {
  expect(screen.queryByRole('link', { name: label })).toBeNull()
}

const setup = async () => {
  renderWithProviders({ component: <SideNav /> })
  mockMailboxesResponse()
  await waitForRequestsToFinish()
}

const setupInboxSelector = async (mailboxes: Mailbox[]) => {
  const { history } = renderWithProviders({
    component: <SideNav />,
  })
  mockMailboxesResponse({ mailboxes })
  await waitForRequestsToFinish()
  return { history }
}

describe('SideNav', () => {
  beforeEach(() => {
    // @ts-ignore
    window.hsGlobal = baseHsGlobal
    window.appData = baseAppData
  })
  it('should render', async () => {
    await setup()
  })

  it('should render :all-the-things: if all FFs and permissions are available', async () => {
    await setup()

    const labelArray = Object.values(SIDENAV_LABELS)

    labelArray.forEach(label => {
      assertLinkExists(label)
    })
  })

  it('should not render edit inbox, outgoing email, permissions, auto-reply, custom fields, or satisfaction ratings if the user does not have manage mailbox settings permission', async () => {
    // @ts-ignore
    window.hsGlobal = aHsGlobal()
      .withMemberPermissions({
        ...baseHsGlobal.memberPermissions,
        manageMailboxSettings: false,
      })
      .build()

    await setup()

    assertLinkDoesNotExist('Edit Inbox')
    assertLinkDoesNotExist(SIDENAV_LABELS.OUTGOING_EMAIL)
    assertLinkDoesNotExist(SIDENAV_LABELS.PERMISSIONS)
    assertLinkDoesNotExist(SIDENAV_LABELS.AUTO_REPLY)
    assertLinkDoesNotExist(SIDENAV_LABELS.SATISFACTION_RATINGS)
    assertLinkDoesNotExist(SIDENAV_LABELS.CUSTOM_FIELDS)
  })

  it('should not render workflows if user does not have manage workflows permission', async () => {
    // @ts-ignore
    window.hsGlobal = aHsGlobal()
      .withMemberPermissions({
        ...baseHsGlobal.memberPermissions,
        manageWorkflows: false,
      })
      .build()

    await setup()

    assertLinkDoesNotExist(SIDENAV_LABELS.WORKFLOWS)
  })

  it('should not render saved replies if user does not have manage saved replies permission', async () => {
    // @ts-ignore
    window.hsGlobal = aHsGlobal()
      .withMemberPermissions({
        ...baseHsGlobal.memberPermissions,
        manageSavedReplies: false,
      })
      .build()

    await setup()

    assertLinkDoesNotExist(SIDENAV_LABELS.SAVED_REPLIES)
  })

  it('should not render satisfaction ratings if FF is not enabled', async () => {
    // @ts-ignore
    window.hsGlobal = aHsGlobal()
      .withFeatures({
        ...baseHsGlobal.features,
        isSatisfactionRatingsEnabled: false,
      })
      .build()

    await setup()

    assertLinkDoesNotExist(SIDENAV_LABELS.SATISFACTION_RATINGS)
  })

  it('should not render AI Drafts if FF is not enabled', async () => {
    // @ts-ignore
    window.hsGlobal = aHsGlobal()
      .withFeatures({
        ...baseHsGlobal.features,
        isAiDraftsHideSupportAgentEnabled: false,
      })
      .build()

    await setup()

    assertLinkDoesNotExist(SIDENAV_LABELS.AI_DRAFTS)
  })

  it('should not render Auto Reply if FF is not enabled', async () => {
    // @ts-ignore
    window.hsGlobal = aHsGlobal()
      .withFeatures({
        ...baseHsGlobal.features,
        isAutoReplyEnabled: false,
      })
      .build()

    await setup()

    assertLinkDoesNotExist(SIDENAV_LABELS.AUTO_REPLY)
  })

  it('should not render Routing if FF is not enabled', async () => {
    // @ts-ignore
    window.hsGlobal = aHsGlobal()
      .withFeatures({
        ...baseHsGlobal.features,
        isRoutingV1Enabled: false,
      })
      .build()

    await setup()

    assertLinkDoesNotExist(SIDENAV_LABELS.ROUTING)
  })

  it('should include an external link for workflows', async () => {
    await setup()

    const workflowLink = screen.getByRole('link', {
      name: SIDENAV_LABELS.WORKFLOWS,
    })

    const href = workflowLink.getAttribute('href')

    expect(href).toBe('/settings/workflows/1/')
  })

  it('should include an external link for ai drafts', async () => {
    await setup()

    const workflowLink = screen.getByRole('link', {
      name: SIDENAV_LABELS.AI_DRAFTS,
    })

    const href = workflowLink.getAttribute('href')

    expect(href).toBe('/settings/ai-drafts/1')
  })

  it('should include an external link for ai drafts when eligible for SAI migration', async () => {
    window.hsGlobal.features.isAiDraftsHideSupportAgentEnabled = false
    window.hsGlobal.features.isEligibleForSaiMigration = true
    await setup()

    const workflowLink = screen.getByRole('link', {
      name: SIDENAV_LABELS.AI_DRAFTS,
    })

    const href = workflowLink.getAttribute('href')

    expect(href).toBe('/settings/ai-drafts/1')
  })
})

describe('Inbox Selector', () => {
  beforeEach(() => {
    // @ts-ignore
    window.hsGlobal = baseHsGlobal
    window.appData = baseAppData
    nock.cleanAll()
  })

  const getInboxSelector = () =>
    screen.getByRole('button', { name: /select inbox/i })

  it('should render just text if there is only one mailbox', async () => {
    await setupInboxSelector([aMailbox().withName('Justin').build()])

    await waitFor(() => {
      expect(screen.getByText('Justin')).toBeInTheDocument()
      expect(screen.queryByRole('button', { name: /select inbox/i })).toBeNull()
    })
  })

  it('should render a dropdown if there are multiple mailboxes', async () => {
    await setupInboxSelector([
      aMailbox().withName('Justin').build(),
      aMailbox().withName('Maxi').withId(2).build(),
    ])

    await waitFor(() => {
      expect(screen.getByText('Justin')).toBeInTheDocument()
    })

    userEvent.click(getInboxSelector())
    expect(screen.getByRole('option', { name: /justin/i })).toBeInTheDocument()
    expect(screen.getByRole('option', { name: /maxi/i })).toBeInTheDocument()
  })

  it('should navigate to the chosen mailbox settings', async () => {
    const { history } = await setupInboxSelector([
      aMailbox().withName('Justin').build(),
      aMailbox().withName('Maxi').withId(2).build(),
    ])

    await waitFor(() => {
      expect(screen.getByText('Justin')).toBeInTheDocument()
    })

    expect(history.location.pathname).toBe('/inbox/1')

    mockMailboxesResponse()

    userEvent.click(getInboxSelector())
    userEvent.click(screen.getByRole('option', { name: /maxi/i }))

    await waitFor(() => {
      expect(history.location.pathname).toBe('/inbox/2/')
    })
  })

  it('should show Plus badge on Custom Fields when showCustomFieldsPlusPromo is true', async () => {
    window.appData = anAppData()
      .withUpsells({ showCustomFieldsPlusPromo: true })
      .build()

    await setup()

    expect(screen.getByText('Plus')).toBeVisible()
  })

  it('should not show Plus badge on Custom Fields when showCustomFieldsPlusPromo is false', async () => {
    window.appData = anAppData()
      .withUpsells({ showCustomFieldsPlusPromo: false })
      .build()

    await setup()

    expect(screen.queryByText('Plus')).toBeNull()
  })
})
