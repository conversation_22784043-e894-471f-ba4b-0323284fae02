# Creating an SPA

## A Short Guide to Hello World

See the sections after this one for more detail on the structure. In the future, we hope to use generators to reduce the manual work required to start a new SPA.

1. If building in HSAPP, create the directory using the `package-` prefix so that all apps built on new standards and destined to be ported over to HSJS are organized together alphabetically (see the `package-properties` folder as an example) - if and when the app is ported to the HSJS repo, the prefix should be removed. If the app is being built directly in HSJS, no prefix is necessary.
1. Create `/app` folder with [relevant files](https://paper.dropbox.com/doc/JavaScript-at-Help-Scout-A0QblrFO8PLh7yiXXhLqvDVgAg-cQl37qhgfYqUhsiwPrC4V#:uid=265001390280891599765214&h2=/app---App-&-AppRouter). For a Hello World setup, you can ignore AppRouter.
1. Create init.js to mount the app. See the `/package-properties` app for boilerplate and tweak as necessary. Search for “init.js” in this doc for more information on what’s happening in this file.
1. Add SPA to Webpack build.
   1. Update `helpscout.apps.js` to include the new directory name for your app, e.g. `package-properties`
   1. Create a template named after your app, e.g. `site/includes/templates/helpscout/settings/<app-name>`. This will include the DOM node we mount the app to that is referenced in `init.js`, any `appData` we are mounting the app with, and the webpack bundle. See `site/includes/templates/helpscout/settings/properties` for an example.
1. Create feature flag in the `sumo-support` repo. [How To Create a Feature Flag](https://helpscout.slab.com/drafts/0nm14fmu) Use the identifier “Package: “ to group apps that use these new standards together in the Sumo “properties” table for easier identification.

![](_media/creating-spas/feature-flag-sumo.png)

1. Make the feature flag accessible to the frontend (see &quot;How To Create A Feature Flag&quot; doc linked to in step 5). The flag can than be found on the `features` property:
   1. Example of using the feature flag to display a link in the nav bar - manage.tpl

![](_media/creating-spas/feature-flag-usage-1.png)

b. Example of accessing the feature flag in a React app.

![](_media/creating-spas/feature-flag-usage-2.png)

At this point, you should be able to display Hello World from your App.js file. Next steps are adding the Router, Redux, and API as you expand the app’s functionality.

## Code Style

Prefer using _function_ over _const unless you’re trying to avoid binding_ this.

    - Stack traces will contain the name if you use _function_
    - You don’t need to worry about the order in which things are declared if you use _function_
    - Consistency in a codebase is nice
    - However - if you need to deal with _this,_ then _const_ is useful since you don’t need to bind _this_.

## Recommended SPA Structure

You can use the Properties SPA at `site/js/apps/package-properties/` as an example of the setup we describe below.

**_Directory Name_**

Eventually, we intend on moving our SPAs into a monorepo and exporting each one as a package. To this end, we want to flag the SPAs that are “ready to export” by using the prefix package in the directory name. For example, the Properties SPA was rebuilt using these standards in the `site/js/apps/package-properties directory`.

**_A note on folder structure_**

The goal is to avoid a deeply nested hierarchy of folders, so to this end we are co-locating test files and utility files within a component directory (`/MyComponent/MyComponent.utils.js`) instead of breaking them out into `/MyComponent/utils` or `/MyComponent/__tests__` directories. We prefer to contain all utils for the component within the singular util file, but use your best judgement. Maybe you have enough complex utils that you think organizing them in a “one util per file” structure would be best and containing those files within `/MyComponent/utils` would make things cleaner - that makes sense! The majority of the time, the simplicity of having a flat folder structure is preferred.

Below is the recommended top-level structure of an SPA.

| Name                                                                                                                                                                | Type   | Description                                                                                                                                                                                                                                                                                                                         |
| ------------------------------------------------------------------------------------------------------------------------------------------------------------------- | ------ | ----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| [/app](https://paper.dropbox.com/doc/JavaScript-at-Help-Scout-A0QblrFO8PLh7yiXXhLqvDVgAg-cQl37qhgfYqUhsiwPrC4V#:uid=265001390280891599765214&h2=/app-and-Routing)   | Folder | Contains `App.js` which is the entry point for the App and `AppRoutes.js` which has the top-level Routes used to map paths to Views.                                                                                                                                                                                                |
| [/components](https://paper.dropbox.com/doc/JavaScript-at-Help-Scout-AxsR9ZhA005XMplhRfWIaRFpAg-cQl37qhgfYqUhsiwPrC4V#:uid=648028339311648589102205&h2=Components)  | Folder | Components are small building blocks that compose a user interface. They generally have emphemeral state, such as tracking whether a modal is opened or closed. They generally do not connect to global application state.                                                                                                          |
| [/constants](https://paper.dropbox.com/doc/JavaScript-at-Help-Scout-AxsR9ZhA005XMplhRfWIaRFpAg-cQl37qhgfYqUhsiwPrC4V#:uid=908389557252751411581319&h2=/constants)   | Folder | Stores files with strings that are reused in multiple places and are more complex, like URLs, tokens, or long IDs. Note: We are deliberately moving away from moving ANY string constant into a file. If it would be annoying to update it across multiple components and tests, create a shared constant. Use your best judgement. |
| [/server](https://paper.dropbox.com/doc/JavaScript-at-Help-Scout-A0QblrFO8PLh7yiXXhLqvDVgAg-cQl37qhgfYqUhsiwPrC4V#:uid=945358483102183737447009&h2=Mock-API-Server) | Folder | Contains a server for API mocking                                                                                                                                                                                                                                                                                                   |
| [/state](https://paper.dropbox.com/doc/JavaScript-at-Help-Scout-A0QblrFO8PLh7yiXXhLqvDVgAg-cQl37qhgfYqUhsiwPrC4V#:uid=506190218042755037317316&h2=/state-and-Redux) | Folder | Contains all Redux related files - reducers, actions, store, etc.                                                                                                                                                                                                                                                                   |
| /utils                                                                                                                                                              | Folder | Contains any utilities that might be used across multiple components or files in the app. Individual components can have their own `MyComponent.utils.js` file colocated with the component. You should aim to hoist to the shared `/utils` directory only if the function is used in more than one place.                          |
| [/views](https://paper.dropbox.com/doc/JavaScript-at-Help-Scout-AxsR9ZhA005XMplhRfWIaRFpAg-cQl37qhgfYqUhsiwPrC4V#:uid=719541159365921127799879&h2=Views)            | Folder | View components are an entire page or screen in a user interface. They generally connect to global application state and orchestrate loading and persisting entities. Each SPA should have at least one sub-directory in `/views` , `/HomeView`, that represents the first screen a person would visit.                             |
| .babelrc.js                                                                                                                                                         | File   | A config file that indicates whether to use HSDS V2 or V3                                                                                                                                                                                                                                                                           |
| init.js                                                                                                                                                             | File   | Initialize the App and mount it in the DOM. By convention, every app has an `init.js` file as its entry point.                                                                                                                                                                                                                      |

---

## /components

Components are the building blocks of a user interface. They should be small and, as much as possible, have a single purpose. Well designed components can be combined together to form more complicated components.

Components should be organized within the components folder, having one component per sub-folder. The components folder shouldn’t contained nested folders unless you have an odd case such as a number of util files that would be better contained within their own directory.

| File / Folder               | Purpose                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            |
| --------------------------- | -------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| index.js                    | This file is the entry point for the component. It should have at least a `default` export. The `default` export exports the connected component, ready for use in the application.                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                |
| ComponentName.js            | This file defines the component. If the component is connected to the store, then it should have two exports - a default export and a named export. The default export exports the connected component, e.g. `export default EditPropertyForm` / `import EditPropertyForm from ...` This is re-exported in the index.js file. The named export exports the base component, e.g. `export function EditPropetyForm() {}` / `import { ComponentName } from ...`. This should not be connected to the global state or router. This export exists to make it simple to test the component in isolation by directly manipulating its props. Note: If the component is NOT connected, then it should have only have a default export because we don’t need to avoid the complexity of a store in testing. |
| ComponentName.css.js        | This file defines styled components used to style the component. It should have one named export per style. The convention is to suffix the name with `UI`, e.g. `export const TitleUI = styled('h1')`.                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            |
| ComponentName.test.js       | This file includes **_unit tests_** using `jest` and `react-testing-library`.                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                      |
| ComponentName.utils.js      | This file can be used to collect one-off utilities specific to the component. Often these will be included as functions private to the component module; however, there are a couple reasons why you may want to consider moving them out into a utils file: 1. They contain critical logic that benefits from unit testing or 2. They are substantial in number, size or scope and moving them into a separate module will help improve the readability of the component itself.                                                                                                                                                                                                                                                                                                                  |
| ComponentName.utils.test.js | This file includes **_unit tests_** using `jest` and `react-testing-library`.                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                      |
| utils.test.js               | A test file for your utils.                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        |

---

## /views

View components are similar to components, except they represent an entire page or screen within the user interface. Like components, they should be organized within the views folder, having one view per sub-folder.

| File / Folder    | Purpose                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                             |
| ---------------- | --------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| index.js         | This file is the entry point for the view. It should have at least a default export. The `default` export exports the connected view, ready for use in the application. Generally, one view is mapped to one route.                                                                                                                                                                                                                                                                                                                                                                                                                                 |
| ViewName.js      | This file defines the view. It should have two exports: 1. The `default` export exports the connected view. This is re-exported in the `index.js` file. 2. The named export, e.g. `{ ViewName }` exports the base view. This should not be connected to the global state or the router. This export exists to make it simple to test the view in isolation by directly manipulating its props. Note: Our preferred naming convention is for each SPA to have at least one view named `HomeView.js` to indicate at a glance what view is the entry point for the app. If your app is small, it may only have a `HomeView.js` in the `/views` folder. |
| ViewName.css.js  | This file defines styled components used to style the view. It should have one named export per style. The convention is to suffix the name with `UI`, e.g. `export const TitleUI = styled('h1')`.                                                                                                                                                                                                                                                                                                                                                                                                                                                  |
| ViewName.test.js | This file includes **_integration tests_** using `jest` and `react-testing-library`.                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                |
| utils.js         | This file can be used to collect one-off utilities specific to the component. Often these will be included as functions private to the component module; however, there are a couple reasons why you may want to consider moving them out into a utils file: 1. They contain critical logic that benefits from unit testing or 2. They are substantial in number, size or scope and moving them into a separate module will help improve the readability of the component itself.                                                                                                                                                                   |
| util.test.js     | A test file for your utils.                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                         |

Note that we do not include a `ViewName.stories.js` file in the above table. This is because **it is not recommended to create stories for views** since views are generally complex with several different states and a lot of integration code.

---

## /app - App &amp; AppRouter

Ensure `react-router-dom` has been added to the project’s `dependencies`. Your `package.json` file should look like this (it’s okay if you are not using the exact same version):

_package.json_

```

{
  ...,
  "dependencies": {
    ...,
    "react-router-dom": "^5.1.2"
  }
}
```

Create an `AppRouter.js` file in `/app`. This is where the top-level routes for the SPA will reside. The routes will map paths to `Views`.

**Router Types**

We will use one of two kinds of routers in the SPA:

1. `MemoryRouter` is to be used inside tests. This router keeps the route in memory and so it’s safe to use outside of a browser or in an iframe.
1. `BrowserRouter` is to be used in all other environments. This router uses the HTML5 History API.

We can dynamically choose which router is used to build our routes within the `AppRouter.js` file and it will adhere to the rules for which router to use that we laid out.

_/AppRouter.js_

```
import { isTestEnv } from '@common/utils/env'
import { BrowserRouter, MemoryRouter } from 'react-router-dom'

const Router = isTestEnv() ? MemoryRouter : BrowserRouter
```

Carrying on, we can define our routes within the dynamically constructed Router.

```
import { NotFound } from '@common/react/components'
import { isTestEnv } from '@common/utils/env'
import {
  BrowserRouter,
  MemoryRouter,
  Redirect,
  Route,
  Switch
} from 'react-router-dom'

import { HomeView } from '../views'

const Router = isTestEnv() ? MemoryRouter : BrowserRouter

function AppRouter({ basename = '/', redirect }) {
  return (  return (
    <Router basename={basename}>
      <Switch>
        {redirect && <Redirect exact from="/" to={redirect} />}
        <Route exact path="/" component={HomeView} />
        <Route component={NotFound} />
      </Switch>
    </Router>
  )
}

export default AppRouter
```

There are a couple key concepts to point out in the above code sample:

1. Your router should receive a `basename`. Generally your app will hand off another route in hs-app (e.g. `/settings`) and not the root route (e.g. `/`). For example, if the SPA is to live at `/settings`, then the `basename` would be `settings` and this would be mean that `/settings` would map to the `/`` route defined in the router.
1. Your router should have a minimum of three routes:
   1. An optional redirect route that can be used to start the app at a specific route. This is useful for test environments.
   1. A root route (e.g. /) which always maps to the `HomeView` component.
   1. A fallback route with no path that maps to the `NotFound` component. The fallback route will be hit if no routes match and the `NotFound` component handles redirecting to the 404 page.

**Nested Routes**

You are not limited to this top-level router. You can include additional Switches with Routes anywhere below the App component in the component tree in order to have nested routes.

See [https://reacttraining.com/react-router/web/example/nesting](https://reacttraining.com/react-router/web/example/nesting) for an example of nested routes.

**Preserving Filter Values / State in URLs**

In any application there are many kinds of state. Some state, particularly ephemeral state (e.g. sort order and filters), can and often should be stored in the URL. This allows:

- Use the browser back and forward buttons to change filters / state.
- Paste a URL into the address bar to get a specific set of filter values/state to load on the page.

This functionality is often very desirable for end-users as it allows them to bookmark and share deep links to specific states of the application.

The standard way of storing this state in the URL is to encode it as query parameters in the URL query string. The query string is passed to any component connected to the router via `props.location.search`. The react-router-dom library has no opinion about URL query strings are parsed so it is up to us to deserialize them.

_Example of deserialization_

```
const queryParams = new URLSearchParams(props.location.search)
const decodedParams = {}
queryParams.forEach((value, key) => decodedParams[key] = decodeURIComponent(value))
```

_URLSearchParams is only safe to use if you do not support IE11._

Similarly, we have to encode the state before pushing the query string onto the history.

_Example of serialization_

```
const search = Object.keys(decodedParams)
  .map(key => {
    const value = encodeURIComponent(decodedParams[key]
    return `${key}=${value}`
  })
  .join('&')
props.history.push({ search })
```

The react-router-dom library does provide us with a handy `useLocation` hook that can be leveraged to automatically parse the query string on location changes so that your component always uses the most recent value from the query-string. See [https://reacttraining.com/react-router/web/example/query-parameters](https://reacttraining.com/react-router/web/example/query-parameters) for the recipe.

---

## /app - App &amp; API Client

Ensure axios has been added to the project’s dependencies. Your package.json file should look like this (it’s okay if you are not using the exact same version):

```
{
  ...
  "axios": "0.18.1",
  ...
}
```

We do not use Axios directly in hs-app. Instead we utilize `hsApiClient` from `@common/utils/clients`. This is a pre-configured Axios client that includes necessary headers for making requests to the hs-app backend. This client can be customized by changing its `baseUrl`, `headers`, and `options`; however, it should just work as is.

We have an opinionated way of utilizing the API client to make requests which we will cover in more detail in the section on Redux. At this point, we will focus on getting it setup.

**_Creating the API Client_**

In our main App.js file, we will create an instance of hsApiCient.

_Example initialization in App.js_

```

import { hsApiClient } from '@common/utils/clients'

const apiClient = hsApiClient()
```

By default, the client will have a base URL of `/api/v0`. You can change it by passing an alternate base URL as the first argument. You may pass additional headers as the second argument. Finally, you can pass additional options as the third argument.

The hsApiClient includes a timestamp interceptor by default. This adds the `ts` query param to every request. We can add additional interceptors. We recommend adding the `redirectInterceptor` to every app. The redirectInterceptor handles 301 and 302 responses that may be returned by API endpoints.

_Example initialization with use of redirectInterceptor_

```
import { hsApiClient, useRedirectInterceptor } from '@common/utils/clients'

const apiClient = hsApiClient();
useRedirectInterceptor(apiClient);
```

We’ll dive a bit deeper into the redirectInterceptor in the section on [Redirect Errors](https://paper.dropbox.com/doc/JavaScript-at-Help-Scout-AxsR9ZhA005XMplhRfWIaRFpAg-cQl37qhgfYqUhsiwPrC4V#:h2=Redirect-Errors).

**_Injecting the API Client into Redux_**

We use the `redux-thunk` middleware. It supports an `extraArgument` that can be passed to the middleware and then made available to your action creators.

We have abstracted out creating the Redux store and adding the `redux-thunk` middleware in the `configureStore` method from `@common/redux/utils`. (Note: There is also a `createStore` method in `@common/redux/utils` that should be ignored in favor of `configureStore`. We’re keeping `createStore` for now as some apps rely on it, but a comment has been added to it pointing people towards the newer file.) You can import `configureStore` into `App.js` as seen below.

The `configureStore` method takes three arguments:

1. The redux reducers object
1. The redux intial state / preloaded state object
1. The extra arguments object for the thunk middleware

Back in our main `App.js` file we will add our API client to the thunk middleware in our Redux store.

_Example App.js_

```
import { Provider as ReduxProvider } from 'react-redux'
import { configureStore } from '@common/redux/utils'
import { hsApiClient } from '@common/utils/clients'

...

export const App = ({ basename = '/', initialState = {} }) => {
  const apiClient = hsApiClient()
  const store = configureStore(reducers, initialState, { apiClient })

  return (
    <ReduxProvider store={store}>
      ...
    </ReduxProvider>
  )
}
```

With all of this setup, you will now be able to utilize the API client from your operations in Redux.

_Example of accessing the API client in an operation that returns a thunk_

```
export const createProperty = (data, history) => {
  return (dispatch, getState, { apiClient }) => {
    api
      .post('/customer-properties', data)
      .then(() => {
        // on success, dispatch action to update local state with item
        dispatch(setItems(data.items))
      })
      .catch(error => {
        // on failure, dispatch action to display error
        dispatch(setErrors(error))
      })
  }
}
```

**_Errors_**

API-related errors are some of the most commonly encountered errors. Generally there is a cascading approach to how they are handled, from more granular to less granular.

1. Validation errors (422 Unprocessable Entity in the Laravel Router; 400 Bad Request in the old router) should be handled by highlighting inputs with invalid values, typically by changing the border color to red. An error message should be shown for each input, typically below the input. In some cases, more than one message may be shown for an input.
   1. In cases where a validation-related error message cannot be shown on a per-input level or it is not appropriate to do so, the message should be associated with the form, typically by placing it at the top of the form. An example of such a case is a login form. Since we do not want to leak whether the email or password was invalid, on a failed login attempt an error message, such as &quot;_The email or password was incorrect_”, will be shown at the form-level.
1. For all other errors (except 301, 302, and 500), an error noty should be flashed on screen. The noty should display the message provided by the server. Should the server not provide an error message, a generic message, like “_Oops. Something went wrong._” should be displayed.
1. For 500 Application Error errors, a noty should display a generic error message, like “_Oops. Something went wrong._”

**_Redirect Errors_**

301 Moved Permanently and 302 Found are special error cases. Typically, these will be encountered when the user makes a request after their session has been terminated or after their payment fails. In such a case, they should be immediately redirected to a page specified by the server or they should be redirected to the login page and after login they should be redirected back to their last known location. There is a `redirectInterceptor` that can be used with the API client that will handle this automatically.

_Example usage of redirectInterceptor in App.js_

```
import { hsApiClient, useRedirectInterceptor } from '@common/utils/clients'
...

const client = hsApiClient()
useRedirectInterceptor(client)

...
```

**_Validation Errors_**

Validation errors, denoted by a response with a 422 Unprocessable Entity can be expected to contain an `errors` object in the response body that conforms to the output produced by the `validate` method used in Laravel controllers. Here is an example response body for a 422 response:

```
{
  "data": {
    "message": "The given data was invalid.",
    "errors": {
      "name": [
        "May not be null"
      ],
      "email": [
        "May not be null"
      ]
    }
  }
}
```

Note that the errors key will contain a list of errors for each field keyed by the input name. The key can be used to determine the state (e.g. error) of an input and to display one or more errors below the input. Even in cases where there is one error for a field, the error will be wrapped in a list.

It’s possible in some older SPAs that you will not receive validation errors in this format. The old router (pre-Laravel Router) returns validation errors with a 400 Bad Request response so that’s a good indication that you’re going to have a bit of extra work to do. If validation errors are not “Laravel-style”, we should investigate migrating the backend to the new Laravel Router and use Laravel-style validation errors. As a last resort, we should transform the errors into the Laravel-style validation error format on the client. The errors should always be presented to the component that will render the errors in the Laravel-style format specified in this section of the document.

**_Other Errors_**

Other errors (_4xx_, _5xx_) are presented inconsistently within hs-app so it is important to verify the format of these errors for the particular endpoints that you are working with.

When working within hs-app, the legacy `site/js/core/common.js` file has a `handleFail` method exposed through the `fail` method on the `Utils.Ajax` singleton. This can be used as a catch all when working with hs-app endpoints.

_Example usage of HS.Utils.Ajax.fail_

```
import axios from 'axios'
import 'hs-core'

axios
  .get('/api/v0/things')
  .then(() => {})
  .catch(({ response: { data, status } }) => HS.Utils.Ajax.fail(data, status))
```

See [source code](https://github.com/helpscout/hs-app/blob/develop/site/js/core/common.js#L639) for implementation details.

Generally, we will centralize error handling for the situations described in this section in Redux. This will be covered in more detail in the section on Redux. See [https://www.pluralsight.com/guides/centralized-error-handing-with-react-and-redux](https://www.pluralsight.com/guides/centralized-error-handing-with-react-and-redux).

---

## /state and Redux

In order to make state management easier to understand and find, all of our Redux related files will be contained within a top-level /state folder. This folder breaks down as follows:

```
/state
  /errors // The error reducer will clear and set errors
    actions.js
    index.js
    reducers.js
  /<appReducer> // e.g. /properties
    actions.js // contains pure action creators
    index.js
    operations.js // contains thunks
    reducers.js
    utils.js // util functions used in operations.js or reducers.js
    utils.test.js
  index.js
```

**_A note on testing_**

We don’t test actions, reducers, or operations files individually, as this behavior should be covered by integration tests.

As with other directories, we will export everything from the `index.js` file.

_Example reducers/index.js:_

```
import reducer from './reducers'
import * as propertiesActions from './actions'
import * as propertiesOperations from './operations'
export { propertiesActions, propertiesOperations }
export default reducer
```

**_reducers.js_**

Reducers use [immer](https://immerjs.github.io/immer/docs/introduction) to maintain immutable state while making changes. The linked page provides a free egghead tutorial on immer and you can watch an EngU presentation for a high-level introduction to it ([video](https://helpscout.wistia.com/medias/040uhhpk0n), [slides](https://docs.google.com/presentation/d/1CLh-BvseWrltA8nZwS6YJas6ocI6hf4eXnLQ3iUky_M/edit?usp=sharing)). The reducer file should provide the state shape for easy reference.

Note: You’ll need to disable the eslint rule that requires a _default_ in a switch/case statement - when using immer, this step is unnecessary and results in cleaner code. Add the override to the top of your reducer file as seen below.

_Example “reducers.js”:_

```
/* eslint-disable default-case */
import { SET_IS_SAVING, SET_ITEMS, SET_OPEN_MODAL } from './actions'
import { produce } from 'immer'

/* Properties State Shape
{
  data: {
    items: [
      deleted: bool, // not used on frontend
      name: string,
      position: number,
      slug: string,
      type: string,
    ]
  }
  isSaving: bool
}
*/

const propertiesReducer = produce((draft, action) => {
  const { data, type } = action
  switch (type) {
    case SET_IS_SAVING:
      draft.isSaving = data
      break
    case SET_ITEMS:
      draft.data.items = data
  }
  return draft
}, {})
```

**_actions.js_**

Action files store pure action creators that return an object. The type constants are organized at the top of the file.

_Example “actions.js”_

```
export const SET_IS_SAVING = 'SET_IS_SAVING'
export const SET_ITEMS = 'SET_ITEMS'

const setIsSaving = data => ({
  data,
  type: SET_IS_SAVING,
})

const setItems = data => ({
  data,
  type: SET_ITEMS,
})

export { setIsSaving, setItems }
```

**_operations.js_**

Operations files store thunks. Separating them out of “actions.js” makes each file easier to parse and makes it more obvious what is happening when dispatching actions in a connected component - e.g. “propertiesActions.setItems” is going to be a pure action creator whereas “propertiesOperations.createProperty” is going to involve a redux thunk and likely be an API call.

_Example “operations.js”_

```
import { setItems } from './actions'

function handleSetErrors(error, dispatch, slug) {
  // dispatch setError actions when necessary
}
const createProperty = (data, history) => {
  return (dispatch, getState, { api }) => {
    api
      .createProperty({ data })
      .then(() => {
        // [...] manipulate the data
        dispatch(setItems(data))
        dispatch(setOpenModal(''))
        dispatch(updatePositions(data))
        history.replace(`/${encodeURIComponent(data.slug)}`)
        HS.Utils.Main.success('Your property has been saved.')
      })
      .catch(error => handleSetErrors(error, dispatch))
  }
}

export { createProperty }
```

**_A note about the Redux store_**

We import the `configureStore.js` util at `site/js/apps/common/redux/utils/configureStore.js` ** into `App.js` ** rather than having a `store.js` in every new SPA. Be sure to import `configureStore.js` and not `createStore.js`, as the latter is deprecated and will be eventually removed. There is a note at the top of `createStore.js` indicating that the file is deprecated and which file to use instead.

_Example “App.js” importing the configureStore util_

```
import { configureStore } from '@common/redux/utils'
import { Provider } from 'hsds/components/hsds'
// [...] Other imports

export function App({ initialState = {}, scope, ...rest }) {
  // [...] Create api client

  const store = configureStore(reducers, initialState, { api })

  return (
    <Provider>
      <ReduxProvider store={store}>
        <AppRouter {...rest} />
      </ReduxProvider>
    </Provider>
  )
}
```

---

## /constants

The constants directory should hold strings that are reused in multiple places and are more complex, like URLs, tokens, or long IDs. In the past, we’ve pulled strings out of the code and into `/constants` no matter how often they were used - we want to keep things simpler and easier to understand. We should follow the established pattern of having an index.js file within `/constants` that exports the constants.

    - If a string needs to be used in more than one place in a single component or file, then it can be stored in a `CONSTANT_VARIABLE` at the top of that file. If it would be useful to reuse it in tests, we can export the `CONSTANT_VARIABLE` from the component file to the test file.
    - If a string needs to be used in more than one component and it’s important/complex, then it can be placed in a `constants/constants.js` and exported from `constants/index.js` to be consistent with how we export from other directories.

_Example “constants.js”:_

```
export const ARTICLE_ID = '5dd8646104286364bc921b4f'
export const ARTICLE_LINK =
'https://docs.helpscout.com/article/1385-customer-properties'

export const TYPE_OPTIONS = [
  { label: 'Text', value: 'text' },
  { label: 'Number', value: 'number' },
  { label: 'URL', value: 'url' },
  { label: 'Yes or No', value: 'bool' },
  { label: 'Date', value: 'date' },
]
```

---

## /server

It can be helpful to utilize a Mock API Server in your project. In case you need some convincing:

1. It serves to document the parts of the API your SPA interacts with.
1. You can begin work on your SPA before work on the API has completed (or even begun).
1. In general, you can work on features independently of the API, which requires less coordination to get things done.
1. You can quickly and easily put your application into different states without relying on database changes. This is useful for both manual and automated testing.

For Mock API’s there are a number of approaches that can be taken. We recommend using Mirage which is a full featured Mock API server with an internal database and seed factories. If you are interested in how it compares against these “other” approaches, consult this article from the Mirage website: [Comparison with other tools](https://miragejs.com/docs/comparison-with-other-tools/).

**_Mirage_**

Why Mirage? We tried a number of approaches in the past from maintaining an express server to using mock adapters for API clients. These approaches have short comings from requiring extra services to be maintained and run to adding complexity to API client code.

Mirage has a lot of useful features, from factories to a database to request handlers. None of which need to be reinvented. Mirage also runs in the browser, meaning there are no additional services to run. Finally, Mirage intercepts Ajax requests in the browser, meaning that it does not need to know anything about how the SPA works and requires no integration with application code - it need only be started.

In our case, Mirage is replacing Axios Mock Adapter and Helix (schemas and factories). If you are new to Mirage, it is a good idea to read their [guides](https://miragejs.com/docs/getting-started/introduction/) and refer to the [API docs](https://miragejs.com/api/classes/association/) as necessary.

**_Configuring Mirage_**

Create a `server/server.js` file in your SPA. Also create a `server/index.js` that exports everything, e.g. `export * from ./server`.

Your `server.js` file should take the following form:

_Example Mirage Server defined in server/server.js_

```
import { faker } from '@faker-js/faker'
import { Factory, Model, Response, Server } from 'miragejs'

// Make the server a singleton.
let server = null

// Define factories here. These are blueprints for creating entities.
const Property = Factory.extend({
  name(id) {
    return `Custom property ${id}`
  },
  type() {
    return faker.helpers.arrayElement(['date', 'text'])
  },
  slug(id) {
    return `customProperty${id}`
  },
})

// Export a make server function. This should instantiate the server
// singleton with the instance of the Mirage Server. It can be useful to
// add options. In this case `numProperties` is used to dynamically control
// how many property entities the server's database is seeded with.
export function makeServer({
  environment = 'development',
  numProperties = 10
} = {}) {
  // Try to shutdown the server before creating a new instance. This is to
  // support restarts.
  shutdownServer()

  server = new Server({
    environment,

    // Define your models here. These become collections in your database
    // by being pluralized.
    models: {
      property: Model,
    },

    // Define your factories here. These are used to create instances of
    // models.
    factories: {
      property: PropertyFactory,
    },

    routes() {
      // The namespace is prefixed to all routes
      this.namespace = 'api/v0'

      // Define route handlers here
      this.post('/customer-properties', (schema, request) => {
        // You can be creative here in simulating actual server behavior.
        const attrs = JSON.parse(request.requestBody)
        const errors = validateProperty(attrs)
        return errors
          ? new Response(422, {}, { data: { errors } })
          : new Response(201)
      })
    },

    // Seed the server's database on boot.
    seeds(server) {
      // Use the name of the factory and provide the number of entities
      // to create.
      server.createList('property', numProperties)
    }
  })

  return server
}

// Export a function to get the database from the server singleton. It is a
// common pattern in hs-app to preload SPA state with data bootstrapped
// from the server. If we can get the seed data out of the server, we can
// simulate this behavior in our SPAs when using mocking. E.g.
// `server.db.properties` will be an array of the seeded properties.
export function getServerData() {
  if (!server || !server.db) {
    return {}
  }
  return server.db
}

// We can only have one server on the page so it is useful to expose a way
// to shutdown the server.
export function shutdownServer() {
  if (server && typeof server.shutdown === 'function') {
    server.shutdown()
  }
}

// Include helper methods here, like `validateProperty` which is a helper
// method used in this server to validate a property entity.
```

**_Intercepting Requests with Mirage_**

To intercept requests with Mirage, you will need to use the makeServer method defined in `server/server.js` to create and start the server. You may do this in hs-app; you may also want to do this in Cypress prevent having to rewrite mocks in Cypress. If you are interested in integrating with Cypress, see this guide to [Mock Network Requests in Cypress with Mirage](https://miragejs.com/quickstarts/cypress/#gatsby-focus-wrapper). In this section, we will cover using the Mock Server in your SPA in hs-app.

By convention, SPAs in hs-app have an `init.js` file at the root. This is where the main `App.js` component is mounted in the DOM and this is also the perfect place to start the Mock Server. This way, it will only be used in the context of the App and not when using components in isolation in unit tests.

There are a couple different strategies to use and which one you choose will depend on your circumstances.

In both strategies, we will import the server inside a guard, which ensures that the server is compiled away in production builds - DO NOT INCLUDE MIRAGE IN PRODUCTION.

_Strategy: Intercept requests by default_

This strategy is appropriate to use if you are working on SPA before the API is ready. In this strategy, all network requests will be intercepted by the mock server, by default.

_Example implementation of Mock Server in init.js_

```
if (process.env.NODE_ENV === 'development') {
  // Import the server inside this block. This is very important due to the
  // way we have webpack configured. The server will not be tree shaked
  // away if imported outside this block.
  const { makeServer, getServerData } = require('./server')

  // Create and start the Mirage Server
  makeServer()

  // This next part is optional. You can optionally restart the application
  // using seed data extracted from the Mock server as the initial state.
  const serverData = getServerData()
  window.App.restart({
    initialState: {
      properties: {
        data: { items: serverData.properties },
      },
    },
  })
}
```

_Strategy: Intercept requests on demand_

This strategy is appropriate to use if you are working on SPA that has a functioning API. In this strategy, we extend the `window.App` method to support two new methods:

1. `window.App.mock(options)` Will start the mock server with the provided options and then restart the app.
1. `window.App.unmock()` Will stop the mock server and restore the App to it’s original state when the page was first loaded.

It’s useful to keep the mock server in development and these methods allow you to easily opt into using it by invoking it from the browser console. Mock can be called over and over again with different options, allowing you to quickly morph the application into different states, such as blank slate, few records, maximum number of records, etc.

_Example implementation of Mock Server in init.js_

```
if (process.env.NODE_ENV === 'development') {
  // Import the server inside this block. This is very important due to the
  // way we have webpack configured. The server will not be tree shaked
  // away if imported outside this block.
  const { makeServer, getServerData, shutdownServer } = require('./server')

  window.App.mock = (options = {}) => {
    makeServer(options)

    // This next part is optional. You can optionally restart the
    // application using seed data extracted from the Mock server as the
    // initial state.
    const serverData = getServerData()
    window.App.restart({
      initialState: {
        properties: {
          data: { items: serverData.properties },
        },
      },
    })
  }

  window.App.unmock = () => {
    // Stop intercepting requests
    shutdownServer()

    // This next part is optional. You only need to implement this if you
    // implemented restart in the `window.App.mock()` method above.
    window.App.restart({
      initialState: {
        properties: {
          data: { items: window.appData.properties || [] },
        },
      },
    })
  }
}
```

**_Simulating Search_**

For simulating search endpoints, we have a library installed in `hs-app` called `fuse.js`. This library can be used in conjunction with Mirage. Inside one of your handlers, you will need to initialize an instance of Fuse with a list of data from the Mirage database.

_Example of using Fuse.js inside a Mirage route handler to search through property entities in the Mirage database._

```
const fuse = new Fuse(this.db.properties)
...
const result = fuse.search('stuff')
```

See the [Fuse.js documentation](https://fusejs.io/) for more information.

---

## App Initialization and Initial State

**_Bootstrap Data_**

APIs are not the only way to get data into an SPA. The other common technique used in hs-app is to bootstrap data on the server which the SPA can then access.

The data is embedded in the HTML source in one of two JavaScript variables:

1. `appData` - This is unique data required for the SPA you are working on. Typically, this will be a first page of results, though it may also include things like data used to populate select controls.
1. `hsGlobal` - The is global data that any SPA may need. This includes feature flags (`hsGlobal.features`) and permissions (`hsGlobal.memberPermissions`).

**_Limit Use of Global Variables_**

You can access these variables via the global `window` object; for example `window.appData` and `window.hsGlobal`.

It is not good practice to use global variables as it makes unit testing more difficult by making setup required. As such, by convention, we aim to limit access to the these global variables in the SPA’s initialization file, which is `init.js` at the root of the SPA directory.

So if these variables should not be accessed directly, how should they find their way to the components that require them? There are a couple of approaches.

1. **Prop drilling**. You can pass data from these variables to components through props. Depending where the data needs to be consumed, this could involve passing data via props through many layers of components. This can make the SPA more difficult to reason about and maintain. As such, this approach is generally not recommended.
1. **Initial State**. You can preload a Redux store with initial state. Any component that requires the data can then connect to the store. This is the preferred approach as data is only passed in via props to the components that consume the data. Since we tend to store API responses in the Redux store and appData usually contains a preloaded API response, this approach is highly compatible with the broader architecture of the SPA.

**_Initial State_**

We already saw an example of how to preload the Redux store with initial state in the section on Mocking, but we will review it here too.

By convention, the main component (`App.js`), should accept an `initialState` prop. Inside that component, the store should be created with the initial state, before adding the store to the Redux provider. For simplicity, extraneous code is omitted from the following example.

_Example App.js_

```
import { Provider as ReduxProvider } from 'react-redux'
import { createStore } from '@common/redux/utils'
import reducers from '../reducers'

export const App = ({ initialState = {} }) => {
  ...
  const store = createStore(reducers, initialState)
  return (
    <ReduxProvider store={store}>
      ...
    </ReduxProvider>
  )
}
```

**_Start React App_**

In the initialization file, we mount the App in the DOM using bootstrapped data in the initial state. To do this, we use the `startReactApp` function. This handles mounting the App in a DOM node and creating a Marionette app that our global scripts can hook into. If you need to include a Marionette-based legacy modal, like Conversation Preview in the Customer&#39;s app (see screenshot below) , add the `includeModalRegion` property to the config object and set it to `true`.

_Example Marionette-based legacy modal:_

![](_media/creating-spas/Marionette-based-legacy-modal.png)

_Example init.js_

```
import startReactApp from '@common/utils/startReactApp'
import App from './App'

...

window.App = startReactApp({
  App,
  props: {
    initialState: {
      properties: {
        data: {
          items: window.appData.properties || []
        },
      },
    },
  },
  id: '#root',
  includeModalRegion: true, // only include if you need Marionette-based modals
})
```

At a most basic level, this is similar to:

```
import React from 'react'
import { render } from 'react-dom'

render(<App initialState={{
  properties: {
    data: {
      items: window.appData.properties || [],
    },
  },
}} />, document.getElementById('root')
```

Notably, `startReactApp` also creates a Marionette App for global scripts to hook into. Furthermore, it creates a `restart` method that can be used to restart a React app. The restart method takes an argument, which is new props which get merged with the original props. This can be invoked to reboot the app in a new starting state.

_Example of rebooting the app with a Blank Slate:_

`App.restart({ initialState: { data: { items: [] } } })`

In the section on Mocking, we demonstrate how to implement an on demand Mock API Server that utilizes this `App.restart` method under the hood.

---

## Testing

**_Unit &amp; Integration Testing_**

We use Jest and React Testing Library for unit and integration testing. React Testing Library works in tandem with Jest, so it’s just one set of tools and commands to work with.

Unit tests should verify that an individual, isolated part works as expected, such as an individual method or a small component. Integration tests verify that several parts work together, such as a larger component or an entire view.

Unit and integration tests should be located close to the code they are testing. For folders with a single module or component files, place the test file directly adjacent to the module or component file - this would include any new React SPAs. Older SPAs and areas of the codebase like `/apps/common` typically have a `__tests__`folder to contain all tests, but these apps and modules are less structured than the current guidelines for SPAs and might benefit from reducing noise. Moving forward, we’d like to keep our directory structures as flat as possible.

To run the entire test suite, execute: `npm test`

To run tests for a single app, such a common, execute: `npm test -- --app common`

It’s often useful to use the Jest test runner directly so that you can test only what you are working on. For example, if you are working on the `common app`, you can run the following command to execute just the tests for that app and get a coverage report for only that app.

_Example of running tests for one folder with coverage report_

```
npm run jest -- js/apps/common --coverage \
--collectCoverageFrom="site/js/apps/common/**/{!(*.stories|server|init),}.js"
```

To run all tests with a coverage report, execute:`npm run jest -- --coverage`

**_Test Coverage_**

On the topic of test coverage, we do not aim for 100% test coverage as at a certain point there are diminishing returns. We should strive to cover critical paths and to have as high of coverage as possible, but use your best judgment in determining how much is enough.

# Analytics

## MixPanel

We use [MixPanel](https://www.mixpanel.com) to collect data about how our customers use the product. This data can be funnelled into Looker and combined with other data sources, including the Help Scout app database. Keep this in mind because it can be useful to collect related data, such as member and company IDs, since it can be correlated with our database.

Generally, as a JavaScript Engineer you won&#39;t be involved in bringing this data into Looker or analyzing it in Looker, but you may be asked to setup MixPanel experiments in one of our products to facilitate collecting user data in order to better understand our products and how people are using them.

**WARNING**: When testing MixPanel analytics make sure that you are not in icognito/private browsing mode and not using any blockers otherwise your events may not be sent to MixPanel and will not appear in the data. This may leave you wondering if your analytics are working.

**Include MixPanel in the product**

MixPanel has a tracking script that needs to be including in the product. You can find it in the hs-app repo, at [site/includes/templates/core/tracking-mixpanel.tpl](https://github.com/helpscout/hs-app/blob/develop/site/includes/templates/core/tracking-mixpanel.tpl).

In the rest of this discussion on MixPanel we will focus on its use in hs-app, but the general principles could be applied to any other product.

By default, we do not include the tracking script in hs-app. We need to include the `tracking-mixpanel.tpl` partial in the template that is responsible for loading the SPA we want to add analytics to. To get started, find the entry point for your SPA. You can do that by searching for `webpack bundle="{spa}"` where `{spa}` is the name of the SPA. For example, `webpack bundle="docs"` includes the scripts for the Mailbox SPA located in `site/js/apps/docs`.

![](_media/creating-spas/mixPanel-tracking-1.png)

Let&#39;s say that we wanted to add analytics to the Docs SPA. We would need to update the `site/includes/templates/helpscout/docs.tpl` template to include the partial with mixpanel tracking code. We can load the partial after the SPA scripts. The order does not matter. The MixPanel utility functions that we will cover later in this guide are async-friendly. Of note, the MixPanel utility functions are also protected with try/catch blocks so, should something go awry, they will not cause a run time error in the SPA they are used in.

Add `{{include file="core/tracking-mixpanel.tpl"}}` to the end of the file, below `{{webpack bundle="docs"}}`.

![](_media/creating-spas/mixPanel-tracking-2.png)

**Send Events to MixPanel**

We have already created some reusable functionality for tracking events in MixPanel. The `track` function sends an event with a name and any number of properties to MixPanel. This function is NOT anonymous as it captures the member ID and company ID and sends these values to MixPanel along with the other properties. These values can then be used to cross-reference events with specific companies and specific users. To use `track` , you must `import { track } from '@common/utils/mixpanel`.

The track function accept two arguments:

1. `name`. Required. The name of the event. This is what will be looked up in MixPanel or Looker. Good event names consist of a verb in past tense and a noun, such as `Viewed Page` or `Clicked Link`. Capitalize words and use spaces.
1. `props`. Optional. An object of key-value pairs, where the key is a property, such as `planName` and the value is the value of that property, such as `Standard`. The `memberId` and `companyId` properties will be automatically set.

Here are a couple examples:

```
track('Viewed App', { slug })
track('Searched Apps', { query })
```

Note that you are generally going to want to invoke these functions on user events. For example:

```
<button onClick={() => track('Clicked Button')} />
```

Sometimes, for events that happen frequently, you will want to debounce it so that the MixPanel event is only sent once after some period of time after the event completed. For example:

```
const trackSearch = _.debounce(
  value => track('Searched', { query: value },
  500
)
```

We could use the above debounced function on a search input with live search, like so:

```
<Input onChange={value => {
  search(value)
  trackSearch(value)
}} />
```

This will capture the `Searched` event 500ms after the user stops typing.

**Verifying Events in MixPanel**

You can verify that Events were sent to MixPanel during testing by checking the LiveView stream.

First, log in to MixPanel at [https://mixpanel.com/](https://mixpanel.com/). **Check the Engineering Vault in 1Password for the username and password.**

From the main menu, select &quot;Data Management &gt; Live View&quot;.

![](_media/creating-spas/mixPanel-setup-1.png)

In the Live View, click on the Filter button in the top left corner of the page and add a filter for your event. Use the event name, which is the first argument sent to `track`.

![](_media/creating-spas/mixPanel-setup-2.png)

With that setup, use the app and monitor the live stream for your events. Note that there may be a delay, sometimes up to several minutes.

Click on a row in the Live Stream to see all of the details that were captured.

![](_media/creating-spas/mixPanel-setup-3.png)

You&#39;ll notice that MixPanel captures a lot of information about the user. Within the expanded row, click on the &quot;Your Properties&quot; tab to see the custom properties submitted with the event.

![](_media/creating-spas/mixPanel-setup-4.png)

The properties shown above were captured by calling `track({ query })`, where the value of `query` was `basecamp`. (If we were not interested in the company ID or member ID, we could have called `track({ query })` instead.

**Examples**

To see examples of when MixPanel tracking was added to hs-app, see: [https://github.com/helpscout/hs-app/pulls?q=is%3Apr+is%3Aclosed+add+mixpanel++in%3Atitle+](https://github.com/helpscout/hs-app/pulls?q=is%3Apr+is%3Aclosed+add+mixpanel++in%3Atitle+).
