declare module 'hsds/*'

declare module 'hsds/components/drop-list'

declare module 'hsds/components/accordion' {
  const Accordion: React.ComponentType<any> & {
    Section: React.ComponentType<any>
    Title: React.ComponentType<any>
    Body: React.ComponentType<any>
  }

  export default Accordion
}

declare module 'hsds/components/tab-bar' {
  const TabBar: React.ComponentType<any> & {
    Item: React.ComponentType<any>
  }

  export default TabBar
}

declare module 'hsds/components/message-card' {
  const MessageCard: React.ComponentType<any> & {
    NPS: React.ComponentType<any>
    Button: React.ComponentType<any>
    Survey: React.ComponentType<any>
    InlineArticleCard: React.ComponentType<any>
    UrlAttachmentImage: React.ComponentType<any>
  }

  export default MessageCard
  export const MessageModal: React.ComponentType<any>
  export const NPSSurvey: React.ComponentType<any>
  export const EmojiSurvey: React.ComponentType<any>
  export const FacesSurvey: React.ComponentType<any>
  export const MultipleChoiceSurvey: React.ComponentType<any>
  export const ThumbsSurvey: React.ComponentType<any>
  export const MessageBanner: React.ComponentType<any> & {
    Button: React.ComponentType<any>
  }
}

declare module 'hsds/components/checkmark-card' {
  const CheckmarkCard: React.ComponentType<any> & {
    Grid: React.ComponentType<any>
  }

  export default CheckmarkCard
}

declare module 'hsds/components/choice' {
  const Choice: React.ComponentType<any>
  export default Choice
  export const ChoiceGroup: React.ComponentType<any>
}

declare module 'hsds/components/radio-card' {
  const RadioCard: React.ComponentType<any>
  export default RadioCard
}

declare module 'hsds/components/switch' {
  const Switch: React.ComponentType<any>
  export default Switch
}
