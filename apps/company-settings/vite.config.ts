import {
  copyViteBuild,
  createLoaderFile,
  getBaseUrl,
} from '@hs-app-ui/build-scripts'
import react from '@vitejs/plugin-react'
import { defineConfig } from 'vite'

const appName = 'company-settings'
const defaultPath = '/settings/company'
const baseUrl = getBaseUrl(appName, defaultPath)
let config

if (process.env.BUILD_ENV === 'preview') {
  // This simplified config is used by the preview environment (Cloudflare) only
  config = defineConfig({
    define: {
      'process.env': {},
    },
    plugins: [react()],
    base: defaultPath,
    build: {
      outDir: 'dist-preview',
      sourcemap: true,
    },
  })
} else {
  // This config is used by other environments (prod, dev, stack, and docker)
  config = defineConfig({
    define: {
      'process.env': {},
    },
    plugins: [
      react(),
      createLoaderFile(baseUrl),
      copyViteBuild(`../../dist/${appName}`),
    ],
    base: baseUrl,
    build: {
      manifest: true,
      sourcemap: true,
      rollupOptions: {
        input: 'src/main.tsx',
        output: {
          format: 'iife',
        },
      },
    },
  })
}

export default config
