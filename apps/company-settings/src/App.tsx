import './App.css'
import hsLogo from '/help-scout-logo-circle-blue.svg'
import { Provider as HSD<PERSON><PERSON>ider } from 'hsds/components/hsds'
import { Switch, Route, Link } from 'react-router-dom'

export default function App() {
  return (
    <HSDSProvider>
      <div>
        <img src={hsLogo} className="logo" alt="Help Scout Logo" />
        <h1>Hello World! 👋</h1>
        <p>
          This is a basic app example using Vite, TypeScript, React, and React
          Router.
        </p>
        <Layout>
          <Switch>
            <Route path="/" exact component={Home} />
            <Route path="/about" component={About} />
            <Route path="/dashboard" component={Dashboard} />
            <Route component={NoMatch} />
          </Switch>
        </Layout>
      </div>
    </HSDSProvider>
  )
}

function Layout({ children }: { children: React.ReactNode }) {
  return (
    <div>
      <nav>
        <ul>
          <li>
            <Link to="/">Home</Link>
          </li>
          <li>
            <Link to="/about">About</Link>
          </li>
          <li>
            <Link to="/dashboard">Dashboard</Link>
          </li>
          <li>
            <Link to="/nothing-here">Nothing Here</Link>
          </li>
        </ul>
      </nav>

      <hr />

      {children}
    </div>
  )
}

function Home() {
  return (
    <div>
      <h2>Home</h2>
    </div>
  )
}

function About() {
  return (
    <div>
      <h2>About</h2>
    </div>
  )
}

function Dashboard() {
  return (
    <div>
      <h2>Dashboard</h2>
    </div>
  )
}

function NoMatch() {
  return (
    <div>
      <h2>Nothing to see here!</h2>
      <p>
        <Link to="/">Go to the home page</Link>
      </p>
    </div>
  )
}
