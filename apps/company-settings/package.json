{"name": "company-settings", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "dev:stack": "BUILD_ENV=stack npm run build -- --watch", "build": "tsc && vite build", "build:dev": "BUILD_ENV=dev npm run build", "build:artifact": "BUILD_ENV=assets npm run build", "build:prod": "BUILD_ENV=prod npm run build", "build:preview": "concurrently npm:build:proxypal npm:build:preview-assets", "build:preview-assets": "BUILD_ENV=preview npm run build", "build:proxypal": "BUILD_ENV=proxypal npm run build", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "lint:fix": "eslint . --fix --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview"}, "dependencies": {"react-router-dom": "~5.3.1"}, "optionalDependencies": {"@rollup/rollup-linux-x64-gnu": "^4.12.0", "concurrently": "^8.2.2"}, "devDependencies": {"@helpscout/tsconfig": "*", "@hs-app-ui/build-scripts": "*", "@types/react-router-dom": "~5.1.7", "@vitejs/plugin-react": "^4.2.1", "eslint": "~7.32.0", "eslint-config-custom": "*", "eslint-plugin-react-refresh": "^0.4.5", "prettier-config-custom": "*", "typescript": "^5.2.2", "vite": "^5.1.0"}}