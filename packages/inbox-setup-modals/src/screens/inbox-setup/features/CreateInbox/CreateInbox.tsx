import {
  AlertUI,
  InputUI,
  LabelUI,
  ModalBodyUI,
  ModalFooterUI,
} from '../../../../common/components/BaseModal/BaseModal.css'
import { STRINGS } from '../../../../common/constants'
import {
  useSaveInbox,
  useVerifyAddressUser,
} from '../../../../common/hooks/hooks'
import { useGetInboxCreationData } from '../../../../common/hooks/hooks'
import useGetString from '../../../../common/hooks/useGetString'
import type { Inbox } from '../../../../common/types/Inbox'
import {
  CreateEmailHelpIconUI,
  CreateEmailInputLabelUI,
  CreateEmailInputSectionUI,
  CreateEmailInputUI,
  CreateInputSectionUI,
  CreateModalHintUI,
  CreateSuffixUI,
  SparklingEnvelopeUI,
  SubdomainBlankSlateUI,
  SubdomainButtonUI,
} from './CreateInbox.css'
import { useQueryClient } from '@tanstack/react-query'
import Heading from 'hsds/components/heading'
import Icon from 'hsds/components/icon'
import Popover from 'hsds/components/popover'
import Info from 'hsds/icons/info-circle-tiny'
import { debounce } from 'lodash'
import { useCallback, useEffect, useState } from 'react'
import { useGlobals } from 'shared/components/HsApp/HsApp.utils'
import { useBeacon, useHsAppContext, useNoty } from 'shared/hooks'

const DEBOUNCE_TIMEOUT = 500
const ALLOWED_CHARS = /[0-9A-Za-z- ]+/
const EMAIL_CHAR_LIMIT = 63
const SUBDOMAIN_HELP_ARTICLE_ID = '52b5b007e4b0a3b4e5ec64da'
const INBOXES_LIMIT_ERROR = 'Inbox limit reached'

const CreateInbox = ({
  onClose,
  onDidSave = () => {},
}: {
  onClose: () => void
  onDidSave: (inbox: Inbox) => void
}) => {
  const queryClient = useQueryClient()

  // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment, @typescript-eslint/no-unsafe-call
  const { imagePath } = useGlobals()
  const { hsGlobal } = useHsAppContext()
  // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment
  const { showErrorNoty } = useNoty()
  const { openArticle } = useBeacon()
  const { getString } = useGetString()
  const { isLoading, data: creationData } = useGetInboxCreationData()

  const [inboxName, setInboxName] = useState<string>()
  const [inboxAddressUser, setInboxAddressUser] = useState<string>()

  const [shouldDisableSave, setShouldDisableSave] = useState(false)

  const [givenUser, setGivenUser] = useState<string>()
  const [errorMessage, setErrorMessage] = useState<string>()
  const [showInputError, setShowInputError] = useState(false)

  const [saveErrors, setSaveErrors] = useState<{ [key: string]: string }>()

  const { mutateAsync: validate, isLoading: isValidating } =
    useVerifyAddressUser()

  const { mutateAsync: saveInbox, isLoading: isSaving } = useSaveInbox()

  useEffect(() => {
    if (!isSaving && !isValidating) {
      setShouldDisableSave(false)
    }
  }, [isSaving, isValidating])

  const verifyAddressUser = useCallback(
    debounce((user: string) => {
      setShowInputError(false)
      setErrorMessage('')
      setShouldDisableSave(false)

      if (!user) {
        return
      }

      return validate(user)
        .then(verifyAddressUserResponse => {
          const {
            newUser,
            isNewUserValid,
            isNewUserAvailable,
            suggestedNewUser,
          } = verifyAddressUserResponse

          if (isNewUserValid && isNewUserAvailable) {
            return
          }

          setGivenUser(newUser)
          if (!isNewUserValid) {
            if (suggestedNewUser) {
              setErrorMessage(
                "includes invalid characters, but we've suggested a new one above."
              )
            } else {
              setErrorMessage(
                'does not contain any valid characters (letters or numbers).'
              )
              setShowInputError(true)
              setShouldDisableSave(true)
            }
          } else if (!isNewUserAvailable) {
            if (suggestedNewUser) {
              setErrorMessage(
                "is already taken but we've suggested a new one above."
              )
            }
          }

          // if we have received a new suggestion back
          // from verification, override the text input
          // with the suggested value
          if (suggestedNewUser) {
            setInboxAddressUser(suggestedNewUser)
          }
        })
        .catch(() => {
          setShouldDisableSave(true)
        })
    }, DEBOUNCE_TIMEOUT),
    [validate]
  )

  const autogenerateMailboxAddress = (name: string) => {
    let result = ''

    if (name) {
      result = name
        .toLowerCase()
        .replace(/[^0-9a-zA-Z-\s]/g, '')
        .replace(/[\s]/g, '-')
        .trim()
    }

    return result
  }

  const getCanSave = useCallback(() => {
    return (
      !isSaving &&
      !isValidating &&
      !shouldDisableSave &&
      inboxAddressUser &&
      inboxAddressUser.length > 0 &&
      inboxName &&
      inboxName.trim().length > 0
    )
  }, [isSaving, isValidating, shouldDisableSave, inboxAddressUser, inboxName])

  if (isLoading || !creationData) {
    return null
  }

  const {
    shouldWarnForCost,
    subdomainAutoGenerated,
    mailboxAddressSubdomain,
    mailboxAddressDomain,
  } = creationData

  const renderMailboxAddressUserError = () => {
    if (!errorMessage) {
      return null
    }

    return (
      <CreateModalHintUI>
        <strong>{givenUser}</strong>&nbsp;{errorMessage}
      </CreateModalHintUI>
    )
  }

  const handleClose = (event: Event) => {
    event && event.preventDefault()
    onClose()
  }

  const handleSave = (event: Event) => {
    event && event.preventDefault()
    const canSave = getCanSave()
    if (canSave) {
      const data = {
        id: 0,
        name: inboxName,
        addressUser: inboxAddressUser,
      }

      saveInbox(data)
        .then(saveInboxResponse => {
          onDidSave(saveInboxResponse.data as Inbox)
          void queryClient.invalidateQueries(['mailboxes'])
        })
        .catch(saveInboxError => {
          const response = (
            saveInboxError as {
              response: { data: { errors: { email: string } } }
            }
          )?.response

          if (
            Array.isArray(response.data?.errors) &&
            response.data.errors.find(error => error === INBOXES_LIMIT_ERROR)
          ) {
            return showErrorNoty(
              "You've reached the maximum number of Inboxes available",
              {
                primaryButtonProps: {
                  label: 'Upgrade',
                  href: `/members/available-plans/`,
                },
              }
            )
          }

          setSaveErrors(response.data?.errors)
          showErrorNoty(
            `An error occurred while saving the ${hsGlobal.features.inboxNameNoun}. Please try again later.`
          )
        })
    }
  }

  const handleEmailAddressChange = (value: string) => {
    const newUser = value
      ? value.split('@')[0].toLowerCase().replace(' ', '-')
      : ''
    const shouldDisableSave = inboxAddressUser !== newUser

    setInboxAddressUser(newUser)
    setShouldDisableSave(shouldDisableSave)
    verifyAddressUser(newUser)
  }

  const handleEmailAddressKeydown = (event: React.KeyboardEvent) => {
    if (event && event.key) {
      if (!ALLOWED_CHARS.test(event.key)) {
        event.preventDefault()
        return false
      }
    }
    return true
  }

  const handleMailboxNameChange = (value: string) => {
    let autoGeneratedMailboxAddress = autogenerateMailboxAddress(value)

    // if attempting to auto generate a valid mbx address results in
    // no valid characters, we pass through the original input instead,
    // and let the verifyUser callback provide a valid suggestion to user
    if (autoGeneratedMailboxAddress.length === 0 && value.length > 0) {
      autoGeneratedMailboxAddress = value
    }

    setInboxName(value)
    setInboxAddressUser(autoGeneratedMailboxAddress)
    verifyAddressUser(autoGeneratedMailboxAddress)
  }

  const openSubdomainArticle = (event: React.MouseEvent) => {
    event && event.preventDefault()
    openArticle(SUBDOMAIN_HELP_ARTICLE_ID)
  }

  const canSave = getCanSave()
  const addressSuffix = `@${mailboxAddressSubdomain}.${mailboxAddressDomain}`

  let primaryButtonText = STRINGS.CREATE_BUTTON_LABEL
  if (isSaving) {
    primaryButtonText = STRINGS.CREATE_IN_PROGRESS_LABEL
  } else if (isValidating) {
    primaryButtonText = STRINGS.CREATE_VALIDATING_BUTTON_LABEL
  }

  return (
    <>
      <ModalBodyUI version={2} scrollable={false}>
        {subdomainAutoGenerated ? (
          <SubdomainBlankSlateUI data-cy={'CreateMailbox.Modal.Alert'}>
            <SparklingEnvelopeUI imagePath={imagePath as string} />
            <div>
              <Heading size={'h5'}>
                {STRINGS.CREATE_SUBDOMAIN_ALERT_TITLE}
              </Heading>
              <div>
                <p>{getString('CREATE_SUBDOMAIN_ALERT_MESSAGE')}</p>
                <SubdomainButtonUI
                  size="sm"
                  linked
                  data-cy={'CreateMailbox.Modal.Alert.ArticleLink'}
                  onClick={openSubdomainArticle}
                >
                  Learn more
                </SubdomainButtonUI>
              </div>
            </div>
          </SubdomainBlankSlateUI>
        ) : null}
        {shouldWarnForCost ? (
          <AlertUI
            data-cy={'CreateMailbox.Modal.CostWarning'}
            data-testid={'CreateMailbox.Modal.CostWarning'}
          >
            <span>
              {/* This value is related to FF isRenameMailboxToInboxEnabled. Remove after the FF is removed. */}
              Additional {hsGlobal.features.inboxNamePlural} above your plan
              limit cost <strong>$10/month</strong>. The charge will be added to
              your next invoice.
            </span>
          </AlertUI>
        ) : null}
        <CreateInputSectionUI>
          <LabelUI data-cy={'CreateMailbox.Modal.MailboxNameLabel'}>
            {getString('MAILBOX_NAME_LABEL')}
          </LabelUI>
          <InputUI
            // Note: Ignoring this warning in a few places, we should avoid adding more.
            // eslint-disable-next-line jsx-a11y/no-autofocus
            autoFocus={true}
            forceAutoFocusTimeout={100}
            data-cy={'CreateMailbox.Modal.MailboxNameInput'}
            data-testid={'mailbox-name-input'}
            disabled={isSaving}
            onChange={handleMailboxNameChange}
            value={inboxName}
          />
        </CreateInputSectionUI>
        <CreateEmailInputSectionUI>
          <LabelUI data-cy={'CreateMailbox.Modal.AddressUserLabel'}>
            <CreateEmailInputLabelUI>
              <span>{STRINGS.EMAIL_ADDRESS_LABEL}</span>
              <Popover
                placement={'right'}
                triggerOn={'mouseover focus'}
                content={
                  <div>
                    You can connect a custom email address (e.g.{' '}
                    <strong><EMAIL></strong>) in the next step.
                  </div>
                }
              >
                <CreateEmailHelpIconUI>
                  {/* eslint-disable-next-line @typescript-eslint/no-unsafe-assignment */}
                  <Icon icon={Info} size="24" />
                </CreateEmailHelpIconUI>
              </Popover>
            </CreateEmailInputLabelUI>
          </LabelUI>
          <CreateEmailInputUI
            data-cy={'CreateMailbox.Modal.AddressUserInput'}
            data-testid={'address-user-input'}
            disabled={isSaving}
            charValidatorLimit={EMAIL_CHAR_LIMIT}
            suffix={
              <CreateSuffixUI isError={showInputError || !!saveErrors?.email}>
                <span
                  title={addressSuffix}
                  data-cy={'CreateMailbox.Modal.AddressUserSuffix'}
                >
                  {addressSuffix}
                </span>
              </CreateSuffixUI>
            }
            state={showInputError || saveErrors?.email ? 'error' : 'default'}
            onChange={handleEmailAddressChange}
            onKeyDown={handleEmailAddressKeydown}
            hintText={renderMailboxAddressUserError()}
            value={inboxAddressUser}
          />
        </CreateEmailInputSectionUI>
      </ModalBodyUI>
      <ModalFooterUI
        data-cy={'CreateMailbox.Modal.CreateFooter'}
        cancelText={STRINGS.CANCEL_BUTTON_LABEL}
        onCancel={handleClose}
        onPrimaryClick={handleSave}
        primaryButtonDisabled={!canSave}
        primaryButtonText={primaryButtonText}
      />
    </>
  )
}

export default CreateInbox
