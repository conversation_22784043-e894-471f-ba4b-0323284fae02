import { getSendCodeFailureMessage } from '../../../../../common/api/api.utils'
import {
  EmailInputUI,
  ModalFooterUI,
} from '../../../../../common/components/BaseModal/BaseModal.css'
import { MX_TYPES, STRINGS } from '../../../../../common/constants'
import {
  useDoMXLookup,
  useSendConfirmation,
} from '../../../../../common/hooks/hooks'
import useGetString from '../../../../../common/hooks/useGetString'
import type { ErrorResponse } from '../../../../../common/types/ErrorResponse'
import type { Inbox } from '../../../../../common/types/Inbox'
import {
  ForwardEmailModalBodyUI,
  HeadingUI,
  InfoContentUI,
  InfoUI,
  TextUI,
} from './ForwardEmail.css'
import MiscMailbox from 'hsds/illos/misc-mailbox'
import React, { useEffect, useState } from 'react'
import { useNoty } from 'shared/hooks'

const ForwardEmail = ({
  onCancel = () => {},
  onDidSendCode = () => {},
  setIsGmailAlias = () => {},
  setAlias = () => {},
  inbox,
}: {
  onCancel: (isCancelLinkClick: boolean) => void
  onDidSendCode: () => void
  setIsGmailAlias: (isGmailAlias: boolean) => void
  setAlias: (alias: string) => void
  inbox: Inbox
}) => {
  // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment
  const { showSuccessNoty } = useNoty()
  const { getString } = useGetString()
  const [aliasEmail, setAliasEmail] = useState('')
  const [invalid, setInvalid] = useState(false)
  const {
    mutate: sendConfirmation,
    isLoading: isSendingCode,
    isSuccess: didSendCode,
    error: sendCodeError,
  } = useSendConfirmation()
  const { mutate: doMXLookup, data: mxLookupResponse } = useDoMXLookup()

  useEffect(() => {
    if (mxLookupResponse) {
      const {
        data: { mxType },
      } = mxLookupResponse
      const isGmailAlias = mxType === MX_TYPES.MX_TYPE_GOOGLE
      setIsGmailAlias(isGmailAlias)
    }
  }, [mxLookupResponse, setIsGmailAlias])

  useEffect(() => {
    const handleDidSendCode = () => {
      // eslint-disable-next-line @typescript-eslint/no-unsafe-call
      showSuccessNoty(STRINGS.FORWARDER_SEND_CODE_SUCCESS)
      onDidSendCode()
    }

    if (didSendCode) {
      handleDidSendCode()
    }
  }, [didSendCode, onDidSendCode, showSuccessNoty])

  useEffect(() => {
    if (sendCodeError) {
      handleSendCodeFailure()
    }
  }, [sendCodeError])

  const handleAliasChange = (email: string) => {
    const aliasEmail = email ? email.trim() : ''

    setAliasEmail(aliasEmail)
    setInvalid(false)
  }

  const handleKeyDown = (event: React.KeyboardEvent) => {
    if (event.key === 'Enter' && !event.shiftKey) {
      event.preventDefault()
      event.stopPropagation()

      sendCode()
    }
  }

  const handleCancel = (event: Event) => {
    event && event.preventDefault()
    onCancel(true)
  }

  const handleSendCodeFailure = () => {
    setInvalid(true)
  }

  const sendCode = () => {
    if (!invalid && aliasEmail) {
      const domain = aliasEmail.split('@')[1]
      doMXLookup(domain)

      const data = {
        slug: inbox.slug,
        email: aliasEmail,
      }
      sendConfirmation(data)
      setAlias(aliasEmail)
    }
  }

  const handleSendCode = (event: Event) => {
    event && event.preventDefault()

    sendCode()
  }

  const renderInfo = () => {
    return (
      <InfoUI data-cy={'Forwarder.Modal.ForwardEmail.Info'}>
        <MiscMailbox size={100} />
        <InfoContentUI>
          <HeadingUI
            size="h5"
            data-cy={'Forwarder.Modal.ForwardEmail.Info.Heading'}
          >
            {getString('FORWARDER_CONNECT_INFO_TITLE')}
          </HeadingUI>
          <TextUI>{STRINGS.FORWARDER_CONNECT_INFO_DESCRIPTION}</TextUI>
        </InfoContentUI>
      </InfoUI>
    )
  }

  const shouldDisabledButton =
    !inbox || isSendingCode || !aliasEmail.length || invalid
  let errorMessage: string = ''

  if (invalid) {
    const response = (sendCodeError as { response: ErrorResponse })?.response
    errorMessage =
      getSendCodeFailureMessage(response) ||
      STRINGS.FORWARDER_INVALID_EMAIL_ERROR
  }

  const tooltipProps = {
    renderTitleAsHtml: true,
    useSafePolygon: true,
  }

  return (
    <>
      <ForwardEmailModalBodyUI version={2} scrollable={false}>
        <EmailInputUI
          data-cy={'Forwarder.Modal.ForwardEmail.EmailInput'}
          data-testid={'Forwarder.Modal.ForwardEmail.EmailInput'}
          // Note: Ignoring this warning in a few places, we should avoid adding more.
          // eslint-disable-next-line jsx-a11y/no-autofocus
          autoFocus={true}
          forceAutoFocusTimeout={100}
          placeholder={STRINGS.FORWARDER_EMAIL_PLACEHOLDER}
          value={aliasEmail}
          onChange={handleAliasChange}
          onKeyDown={handleKeyDown}
          state={invalid ? 'error' : 'default'}
          errorMessage={errorMessage}
          required={true}
          tooltipProps={tooltipProps}
        />
        {renderInfo()}
      </ForwardEmailModalBodyUI>
      <ModalFooterUI
        data-cy={'Forwarder.Modal.ForwardEmail.Footer'}
        cancelText={STRINGS.CANCEL_SETUP_BUTTON_LABEL}
        onCancel={handleCancel}
        onPrimaryClick={handleSendCode}
        primaryButtonDisabled={shouldDisabledButton}
        primaryButtonText={
          isSendingCode
            ? STRINGS.SENDING_CODE_BUTTON_LABEL
            : STRINGS.SEND_CODE_BUTTON_LABEL
        }
      />
    </>
  )
}

export default ForwardEmail
