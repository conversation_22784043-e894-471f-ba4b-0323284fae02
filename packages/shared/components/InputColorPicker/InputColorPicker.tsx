import classNames from 'classnames'
import { noop } from 'lodash'

import {
  ColorWrapperUI,
  InputColorPickerWrapperUI,
} from 'shared/components/InputColorPicker/InputColorPicker.css'

import ColorPicker from '../ColorPicker'
import Input, { InputProps } from 'hsds/components/input'
import { TooltipProp } from 'hsds/components/tooltip'

export type InputColorPickerProps = {
  className?: string
  color?: string
  originalColor?: string
  errorMessage?: string
  onChange?(value: string): void
  onColorChange?(color: string): void
  value?: string
  isDisabled?: boolean
  isNotEditable?: boolean
  placement?: TooltipProp['placement']
}

export function InputColorPicker(props: InputProps & InputColorPickerProps) {
  const {
    className,
    color,
    originalColor,
    errorMessage,
    onChange = noop,
    onColorChange = noop,
    value = '',
    isDisabled,
    isNotEditable,
    placement = 'top',
    // eslint-disable-next-line @typescript-eslint/no-unused-vars,unused-imports/no-unused-vars
    ref,
    ...rest
  } = props

  const state = errorMessage ? 'error' : 'default'
  const isError = state === 'error'
  const componentClassName = classNames(
    'c-InputColorPicker',
    isError && 'is-error',
    isNotEditable && 'is-not-editable',
    className
  )

  return (
    <InputColorPickerWrapperUI className={componentClassName}>
      <Input
        disabled={isDisabled || isNotEditable}
        data-cy="inputColorPicker"
        errorMessage={errorMessage}
        onChange={onChange}
        state={state}
        type="text"
        value={value}
        aria-label="Color Picker input"
        suffix={
          !isDisabled ? (
            <ColorWrapperUI>
              <ColorPicker
                color={color}
                isError={isError}
                isInControlGroup
                onChange={onColorChange}
                placement={placement}
                originalColor={originalColor}
              />
            </ColorWrapperUI>
          ) : null
        }
        {...rest}
      />
    </InputColorPickerWrapperUI>
  )
}
