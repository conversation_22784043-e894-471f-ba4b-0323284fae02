import React from 'react'

import {
  BorderlessInputUI,
  BorderlessTextareaUI,
  EditFormUI,
  PrefixUI,
} from '../EditImprovementComposer.css'

export interface EditFormProps {
  title: string
  body: string
  onTitleChange: (value: string) => void
  onBodyChange: (value: string) => void
  titleRef: React.MutableRefObject<
    HTMLInputElement | HTMLTextAreaElement | null
  >
  isDisabled?: boolean
}

export const EditForm = ({
  title,
  body,
  onTitleChange,
  onBodyChange,
  titleRef,
  isDisabled = false,
}: EditFormProps) => {
  return (
    <EditFormUI>
      <BorderlessInputUI
        value={title}
        onChange={onTitleChange}
        placeholder="Title"
        disabled={isDisabled}
        inputRef={(node: HTMLInputElement | HTMLTextAreaElement | null) => {
          titleRef.current = node
        }}
        prefix={<PrefixUI>Title</PrefixUI>}
        size="lg"
        data-testid="improvement-title-input"
        aria-label="Improvement title"
        autoFocus
      />
      <BorderlessTextareaUI
        value={body}
        onChange={onBodyChange}
        placeholder="Describe the improvement..."
        disabled={isDisabled}
        multiline
        maxHeight="155px"
        size="lg"
        data-testid="improvement-body-input"
        aria-label="Improvement description"
      />
    </EditFormUI>
  )
}
