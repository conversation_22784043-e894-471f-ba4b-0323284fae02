import React from 'react'

import { COMPOSER_STATES, type ComposerState } from '../constants'

import Button from 'hsds/components/button'
import ButtonIcon from 'hsds/components/button-icon'
import Input from 'hsds/components/input'
import ArrowUp from 'hsds/icons/arrow-up'

interface ComposerInputProps {
  state: ComposerState
  userInput: string
  onInputChange: (value: string) => void
  onSubmit: (e: React.FormEvent) => void
  isDisabled: boolean
  inputRef: React.RefObject<HTMLInputElement>
}

export const ComposerInput = ({
  state,
  userInput,
  onInputChange,
  onSubmit,
  isDisabled,
  inputRef,
}: ComposerInputProps) => {
  const isGenerateDisabled = !userInput.trim() || isDisabled

  return (
    <form onSubmit={onSubmit}>
      <Input
        value={userInput}
        onChange={onInputChange}
        placeholder={
          state === COMPOSER_STATES.INITIAL
            ? 'Provide additional context'
            : 'Share additional context'
        }
        disabled={isDisabled}
        inputRef={(ref: HTMLInputElement | HTMLTextAreaElement | null) => {
          if (inputRef && 'current' in inputRef) {
            ;(
              inputRef as React.MutableRefObject<
                HTMLInputElement | HTMLTextAreaElement | null
              >
            ).current = ref
          }
        }}
        size="lg"
        action={
          state === COMPOSER_STATES.INITIAL ? (
            <ButtonIcon
              submit
              icon={ArrowUp}
              color="blue"
              size="sm"
              disabled={isGenerateDisabled}
              aria-label="Generate improvement"
            />
          ) : (
            <Button
              submit
              color="blue"
              size="sm"
              rounded
              disabled={isDisabled || !userInput.trim()}
            >
              Retry
            </Button>
          )
        }
      />
    </form>
  )
}
