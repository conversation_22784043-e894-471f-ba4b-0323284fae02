import { format, isValid, parse } from 'date-fns'
import { debounce } from 'lodash'
import {
  FocusEventHandler,
  KeyboardEventHandler,
  useCallback,
  useEffect,
  useRef,
  useState,
} from 'react'

import useManagePopover from './hooks/useManagePopover'

import { DateInputUI, DatePickerUI, IconButtonUI } from './InputDatePicker.css'

import Datepicker from 'hsds/components/datepicker'
import { InputProps } from 'hsds/components/input'
import Label from 'hsds/components/label'
import Popover from 'hsds/components/popover'
import { TooltipProp } from 'hsds/components/tooltip'
import Calendar from 'hsds/icons/calendar'
import CrossSmall from 'hsds/icons/cross-tiny'

const STANDARD_FORMAT = 'yyyy-MM-dd'

const InputDatePicker = ({
  inputName = 'date',
  label = 'Date',
  value = '',
  onChange = () => {},
  state,
  errorMessage,
  onError = () => {},
  placement,
  placeholder,
  // Used only for display. Internally, the date picker always uses yyyy-MM-dd.
  dateFormat = STANDARD_FORMAT,
  withCalendarIcon = true,
  withClearButton = false,
  enableRangeSelection = false,
  openDatePickerOnRender = false,
  withArrow = false,
  allowFutureDates = false,
}: {
  inputName?: string
  label?: string
  value?: string
  onChange?: (date: string) => void
  state?: string
  errorMessage?: string
  onError?: (error: string) => void
  placement?: TooltipProp['placement']
  appendToBody?: boolean
  placeholder?: string
  dateFormat?: string
  withCalendarIcon?: boolean
  withClearButton?: boolean
  enableRangeSelection?: boolean
  openDatePickerOnRender?: boolean
  withArrow?: boolean
  allowFutureDates?: boolean
}) => {
  const popoverReference = useRef<HTMLDivElement>(null)
  const [inputValue, setInputValue] = useState<string | undefined>()
  const [inputError, setInputError] = useState('')
  const [startDate, setStartDate] = useState<Date | undefined>()
  const [endDate, setEndDate] = useState<Date | undefined>()
  const [selectingDateRange, setSelectingDateRange] = useState(false)

  const getDatesForDatePicker = useCallback(
    (value: string) => {
      let startDate: Date | undefined
      let endDate: Date | undefined
      const [startDateString, endDateString] =
        value.length > 0 ? value.split(' - ') : []
      if (startDateString) {
        const startDateParsed = parse(startDateString, dateFormat, new Date())
        if (
          isValid(startDateParsed) &&
          startDateString.length === dateFormat.length
        ) {
          startDate = startDateParsed
        }
      }
      if (endDateString) {
        const endDateParsed = parse(endDateString, dateFormat, new Date())
        if (
          isValid(endDateParsed) &&
          endDateString.length === dateFormat.length &&
          startDate &&
          endDateParsed.getTime() >= startDate.getTime()
        ) {
          endDate = endDateParsed
        }
      }
      return {
        startDate,
        endDate,
      }
    },
    [dateFormat]
  )

  const resetValue = useCallback(() => {
    setInputValue('')
    setInputError('')
    setStartDate(undefined)
    setEndDate(undefined)
    onChange('')
  }, [onChange])

  // This is needed so we can set/reset the value externally by changing the prop.
  useEffect(() => {
    if (value.length === 0) {
      if (!inputError) {
        setInputValue(value)
      }
      setStartDate(undefined)
      setEndDate(undefined)
      return
    }

    setInputValue(value || '')
    setInputError('')

    const { startDate, endDate } = getDatesForDatePicker(value)
    setStartDate(startDate)
    if (enableRangeSelection && endDate) {
      setEndDate(endDate)
    }

    // We don't want to include inputError in the deps because then it clears the error when it shouldn't.
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [enableRangeSelection, getDatesForDatePicker, value])

  const {
    dateInputRef,
    datePickerRef,
    hideDatePicker,
    toggleDatePicker,
    isPopoverVisible,
    handleKeyDownOnInput,
    handleKeyDownOnDatePicker,
  } = useManagePopover(openDatePickerOnRender)

  const handleDateSelected = useCallback(
    ({
      startDate: newStartDate,
      endDate: newEndDate,
      focusedInput,
    }: {
      startDate: Date
      endDate?: Date
      focusedInput?: string
    }) => {
      // Because we are setting start and end date using the state var, sometimes the date picker
      // passes start date instead of end date and vice-versa (basically it doesn't know if the
      // user is selecting the start or end date).
      if (enableRangeSelection) {
        if (!selectingDateRange) {
          // If selecting the start date but it came as end date, swap them.
          if (newEndDate || focusedInput === 'endDate') {
            if (newEndDate) {
              newStartDate = newEndDate
            }
            newEndDate = undefined
            setEndDate(undefined)
            setStartDate(newStartDate)
          }
          setSelectingDateRange(true)
        } else {
          // If selecting end date but it came as start date, swap them.
          if (selectingDateRange && startDate && !newEndDate) {
            newEndDate = newStartDate
            newStartDate = startDate
          }
          setSelectingDateRange(false)
        }
      }

      const startDateString = format(newStartDate, dateFormat)
      const endDateString = newEndDate
        ? format(newEndDate, dateFormat)
        : undefined
      const dateString = enableRangeSelection
        ? newEndDate
          ? `${startDateString} - ${endDateString}`
          : `${startDateString} - `
        : startDateString

      setInputValue(dateString)
      setInputError('')

      if (!enableRangeSelection || newEndDate) {
        onChange(dateString)
        hideDatePicker()
      }
    },
    [
      selectingDateRange,
      enableRangeSelection,
      dateFormat,
      startDate,
      onChange,
      hideDatePicker,
    ]
  )

  /**
   * Validates the date and returns an error message if there is an error.
   */
  const validateDateString = useCallback(
    (dateString: string) => {
      const humanReadableFormat = dateFormat.toUpperCase()

      if (enableRangeSelection) {
        const dates = dateString.split(' - ')
        if (dates.length !== 2) {
          return `Date range must be formatted as ${humanReadableFormat} - ${humanReadableFormat}.`
        } else {
          const startDate = parse(dates[0], dateFormat, new Date())
          const endDate = parse(dates[1], dateFormat, new Date())
          if (
            !isValid(startDate) ||
            dates[0].length !== dateFormat.length ||
            !isValid(endDate) ||
            dates[1].length !== dateFormat.length
          ) {
            return `Date range must be formatted as ${humanReadableFormat} - ${humanReadableFormat}.`
          }

          if (startDate > endDate) {
            return 'Start date must be before end date.'
          }

          if (!allowFutureDates && startDate.getTime() > new Date().getTime()) {
            return 'Start date cannot be in the future.'
          }

          if (!allowFutureDates && endDate.getTime() > new Date().getTime()) {
            return 'End date cannot be in the future.'
          }
        }
      } else {
        const date = parse(dateString, dateFormat, new Date())
        if (!isValid(date) || dateString.length !== dateFormat.length) {
          return `Date must be formatted as ${humanReadableFormat}.`
        }

        if (!allowFutureDates && date.getTime() > new Date().getTime()) {
          return 'Date cannot be in the future.'
        }
      }

      return ''
    },
    [allowFutureDates, dateFormat, enableRangeSelection]
  )

  const handleClickOutside: FocusEventHandler<HTMLElement> = useCallback(
    event => {
      if (event?.relatedTarget && event.relatedTarget instanceof Node) {
        // Check if click is on the input, calendar icon, or any element in the popoverReference
        const isOnInputArea = popoverReference.current?.contains(
          event.relatedTarget
        )
        const isInsidePopover = datePickerRef.current?.contains(
          event.relatedTarget
        )
        if (isOnInputArea || isInsidePopover) return
      }

      if (isPopoverVisible && endDate) {
        hideDatePicker()
      }
    },
    [isPopoverVisible, endDate, hideDatePicker, datePickerRef, popoverReference]
  )

  const handleChange = debounce((dateString: string) => {
    setInputValue(dateString)

    if (dateString.length === 0) {
      setStartDate(undefined)
      setEndDate(undefined)
      setInputError('')
      onChange('')
      return
    }

    const errorMessage = validateDateString(dateString)
    if (errorMessage) {
      setInputError(errorMessage)
      onError(errorMessage)
      return
    }

    const { startDate, endDate } = getDatesForDatePicker(dateString)
    setStartDate(startDate)
    setEndDate(endDate)
    setInputError('')
    onChange(dateString)
    hideDatePicker()
  }, 300)

  const shouldDisplayClearButton =
    withClearButton && inputValue && inputValue?.length > 0

  return (
    <>
      {label && <Label for={inputName}>{label}</Label>}
      <div ref={popoverReference}>
        <DateInputUI
          autoComplete="off"
          placeholder={placeholder}
          value={inputValue || ''}
          id={inputName}
          name={inputName}
          onChange={handleChange}
          state={
            (inputError ? 'error' : state || 'default') as InputProps['state']
          }
          errorMessage={inputError || errorMessage}
          inputRef={(ref: HTMLInputElement | HTMLTextAreaElement | null) =>
            (dateInputRef.current = ref as HTMLInputElement)
          }
          onClick={() => toggleDatePicker()}
          onKeyDown={handleKeyDownOnInput}
          {...(withCalendarIcon &&
            !shouldDisplayClearButton && {
              inlineSuffix: (
                <IconButtonUI
                  icon={Calendar}
                  size="lg"
                  onClick={() => toggleDatePicker()}
                  seamless
                  title="Open date picker"
                />
              ),
            })}
          {...(shouldDisplayClearButton && {
            inlineSuffix: (
              <IconButtonUI
                icon={CrossSmall}
                size="sm"
                onClick={() => resetValue()}
                seamless
                title="Clear date"
              />
            ),
          })}
        />
      </div>
      <Popover
        maxWidth="auto"
        triggerOn="off"
        isOpen={isPopoverVisible}
        onBlur={handleClickOutside}
        withArrow={withArrow}
        placement={placement || 'bottom-start'}
        ref={datePickerRef}
        reference={popoverReference.current as HTMLElement}
        alwaysMounted
        renderContent={() => {
          return (
            <DatePickerUI
              onKeyDown={handleKeyDownOnDatePicker as KeyboardEventHandler}
            >
              <Datepicker
                firstDayOfWeek={1}
                startDate={startDate}
                endDate={endDate}
                onDateChange={handleDateSelected}
                allowFutureDatePick={allowFutureDates}
                {...(enableRangeSelection ? { enableRangeSelection } : {})}
              />
            </DatePickerUI>
          )
        }}
      />
    </>
  )
}

export default InputDatePicker
