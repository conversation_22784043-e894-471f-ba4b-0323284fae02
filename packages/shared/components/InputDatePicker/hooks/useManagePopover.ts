import {
  MutableRefObject,
  useCallback,
  useEffect,
  useRef,
  useState,
} from 'react'

import {
  findFirstFocusableNode,
  findLastFocusableNode,
  findPreviousFocusableNode,
} from 'hsds/utils/focus'

const useManagePopover = (openDatePickerOnRender: boolean) => {
  const [isPopoverVisible, setIsPopoverVisible] = useState(
    openDatePickerOnRender
  )

  const dateInputRef = useRef<HTMLInputElement | HTMLTextAreaElement | null>()
  const datePickerRef: MutableRefObject<HTMLElement | null> = useRef(null)

  // If open automatically, focus the date picker.
  useEffect(() => {
    if (openDatePickerOnRender) {
      focusDatePicker()
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [])

  /**
   * Open the popover and move the focus to it.
   */
  const showDatePicker = useCallback(() => {
    setIsPopoverVisible(true)

    focusDatePicker()
  }, [])

  /**
   * Close the popover and focus the input.
   */
  const hideDatePicker = useCallback(() => {
    setIsPopoverVisible(false)

    setTimeout(() => {
      dateInputRef.current?.focus()
    }, 50)
  }, [])

  /**
   * Toggle the popover.
   */
  const toggleDatePicker = () => {
    if (isPopoverVisible) {
      hideDatePicker()
    } else {
      showDatePicker()
    }
  }

  const focusDatePicker = () => {
    // Wait for a bit then focus the first focusable element in the date picker.
    setTimeout(() => {
      // eslint-disable-next-line @typescript-eslint/no-unsafe-call
      const firstFocusableNode = findFirstFocusableNode(
        datePickerRef.current
      ) as HTMLElement
      firstFocusableNode?.focus()
    }, 50)
  }

  /**
   * Toggle the datepicker on Enter.
   */
  const handleKeyDownOnInput = useCallback(
    (e: React.KeyboardEvent<HTMLInputElement | HTMLTextAreaElement>) => {
      if (e.key === 'Enter' && !e.shiftKey && datePickerRef?.current) {
        e.preventDefault()
        if (isPopoverVisible) {
          hideDatePicker()
          return
        }

        showDatePicker()
      }
    },
    [hideDatePicker, isPopoverVisible, showDatePicker]
  )

  /**
   * Manage tab navigation (forward and backward) within the date picker.
   * This is only needed because we have to append the date picker to the body to prevent z-index issues.
   *
   * Close the date picker if pressing the Escape key, tabbing on the last button in the date picker,
   * or tabbing back on the first button in the date picker.
   */
  const handleKeyDownOnDatePicker = useCallback(
    (e: React.KeyboardEvent<HTMLInputElement | HTMLTextAreaElement>) => {
      if (e.key === 'Escape') {
        e.preventDefault()
        hideDatePicker()
      } else if (e.key === 'Tab') {
        if (!e.shiftKey) {
          // Navigating forward.
          // eslint-disable-next-line @typescript-eslint/no-unsafe-call
          let lastFocusableNode = findLastFocusableNode(
            datePickerRef.current
          ) as HTMLButtonElement
          // This is needed because for some bizarre reason the last focusable node is disabled.
          while (lastFocusableNode.disabled) {
            // eslint-disable-next-line @typescript-eslint/no-unsafe-call
            lastFocusableNode = findPreviousFocusableNode(
              lastFocusableNode
            ) as HTMLButtonElement
          }
          if (lastFocusableNode === document.activeElement) {
            e.preventDefault()
            hideDatePicker()
          }
        } else {
          // Navigating backwards.
          // eslint-disable-next-line @typescript-eslint/no-unsafe-call
          const firstFocusableNode = findFirstFocusableNode(
            datePickerRef.current
          ) as HTMLElement
          if (firstFocusableNode === document.activeElement) {
            e.preventDefault()
            hideDatePicker()
          }
        }
      }
    },
    [hideDatePicker]
  )

  return {
    dateInputRef,
    datePickerRef,
    showDatePicker,
    hideDatePicker,
    toggleDatePicker,
    isPopoverVisible,
    handleKeyDownOnInput,
    handleKeyDownOnDatePicker,
  }
}

export default useManagePopover
