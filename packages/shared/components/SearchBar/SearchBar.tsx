import React, { useCallback, useEffect, useState } from 'react'

import { useComponentDidUpdate } from 'hsds/hooks'
import { useDebounce } from 'shared/hooks'

import { InputUI, SuffixUI } from './SearchBar.css'

import ClearIcon from './components/ClearIcon/ClearIcon'
import SearchIcon from './components/SearchIcon/SearchIcon'

export const SearchBar = ({
  isLoading,
  onSearch,
  onInputChange,
  ariaLabel,
  name,
  autoFocus = true,
  debounceDelay = 500,
  dataCy = undefined,
  placeholder = 'Search...',
  clearSearchInput,
  onClearSearch,
}: {
  isLoading: boolean
  autoFocus?: boolean
  onSearch: (searchQuery: string) => void
  ariaLabel: string
  placeholder?: string
  name?: string
  debounceDelay?: number
  dataCy?: string
  onInputChange?: (
    previousValue: string,
    newValue: string,
    e: React.ChangeEvent<HTMLInputElement>
  ) => void
  onClearSearch?: () => void
  clearSearchInput?: boolean
}) => {
  const [searchValue, setSearchValue] = useState('')

  const debouncedSearchValue = useDebounce(searchValue, debounceDelay) as string

  const dispatchSearchValue = useCallback(
    (value: string) => {
      onSearch(value)
    },
    [onSearch]
  )

  useEffect(() => {
    dispatchSearchValue(debouncedSearchValue)
  }, [debouncedSearchValue, dispatchSearchValue])

  useComponentDidUpdate(() => {
    if (clearSearchInput && searchValue !== '') {
      setSearchValue('')
    }
  }, [clearSearchInput, searchValue])

  const handleOnInputChange = useCallback(
    (
      value: string,
      event?: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
    ) => {
      setSearchValue(value)
      onInputChange &&
        onInputChange(
          searchValue,
          value,
          event as React.ChangeEvent<HTMLInputElement>
        )
    },
    [onInputChange, searchValue]
  )

  const clearSearch = useCallback(() => {
    setSearchValue('')
    dispatchSearchValue('')
    onClearSearch && onClearSearch()
  }, [dispatchSearchValue, onClearSearch])

  const renderCurrentIcon = () => {
    return searchValue ? (
      <SuffixUI>
        <ClearIcon clearSearch={clearSearch} />
      </SuffixUI>
    ) : (
      <SuffixUI>
        <SearchIcon />
      </SuffixUI>
    )
  }

  return (
    <InputUI
      aria-label={ariaLabel}
      data-testid="Input"
      autocomplete="off"
      placeholder={placeholder}
      name={name}
      inlineSuffix={renderCurrentIcon()}
      tabindex="0"
      onChange={handleOnInputChange}
      value={searchValue}
      autoFocus={autoFocus}
      disabled={isLoading}
      {...(dataCy && { 'data-cy': dataCy })}
    />
  )
}
