import { createCallerFactory, router } from '../trpc'
import { aiTranslationRouter } from './ai-translate/router'
import { authenticatedRouter } from './authenticated'
import { automationsRouter } from './automations/router'
import { beaconsRouter } from './beacons/router'
import { companiesRouter } from './companies/router'
import { customFieldsRouter } from './custom-fields/router'
import { externalSourcesRouter } from './external-sources/router'
import { publicRouter } from './public'
import { threadReactionsRouter } from './thread-reactions/router'

export const appRouter = router({
  public: publicRouter,
  authenticated: authenticatedRouter,
  automations: automationsRouter,
  customFields: customFieldsRouter,
  aiTranslation: aiTranslationRouter,
  threadReactions: threadReactionsRouter,
  companies: companiesRouter,
  externalSources: externalSourcesRouter,
  beacons: beaconsRouter,
})

export type AppRouter = typeof appRouter

export const createCaller = createCallerFactory(appRouter)
