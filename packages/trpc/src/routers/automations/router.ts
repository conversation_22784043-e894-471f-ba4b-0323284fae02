import { ensureUserHasAccessToInbox } from '../../middleware/ensure-user-has-access-to-inbox'
import { router, authenticatedProcedure } from '../../trpc'
import { RoutingSettings, routingSettingsSchema } from './types'
import { z } from 'zod'

const routingMocksByInbox = new Map<number, RoutingSettings>()

const getDefaultRoutingSettings = (): RoutingSettings => ({
  enabled: true,
  assignmentMethod: 'balanced',
  assignmentLimit: 10,
  userIds: [],
})

export const automationsRouter = router({
  getRoutingSettings: authenticatedProcedure
    .input(z.object({ inboxId: z.number() }))
    .use(ensureUserHasAccessToInbox)
    .query(({ input }): RoutingSettings => {
      if (!routingMocksByInbox.has(input.inboxId)) {
        routingMocksByInbox.set(input.inboxId, getDefaultRoutingSettings())
      }
      return routingMocksByInbox.get(input.inboxId)!
    }),
  updateRoutingSettings: authenticatedProcedure
    .input(
      z.object({
        inboxId: z.number(),
        routingSettings: routingSettingsSchema,
      })
    )
    .use(ensureUserHasAccessToInbox)
    .mutation(({ input }) => {
      const currentSettings =
        routingMocksByInbox.get(input.inboxId) ?? getDefaultRoutingSettings()
      const updatedSettings = {
        ...currentSettings,
        ...input.routingSettings,
      }

      routingMocksByInbox.set(input.inboxId, updatedSettings)
      return updatedSettings
    }),
})
