import { AiServicesApi } from '../../clients/ai-services/ai-services'
import { AiServiceError } from '../../clients/ai-services/errors'
import { CoreApi } from '../../clients/core-api/core-api'
import { createCaller } from '../_app'
import { PrismaClient } from '@prisma/client'
import { TRPCError } from '@trpc/server'
import { vi, beforeEach, describe, expect, it } from 'vitest'

const mockGetExternalSources = vi.fn()
const mockCreateExternalSource = vi.fn()
const mockResyncExternalSource = vi.fn()
const mockDeleteExternalSource = vi.fn()
const mockUpdateExternalSource = vi.fn()
const mockGetWebsiteMap = vi.fn()
const mockGenerateImprovement = vi.fn()
const mockGetExternalSourcesByConversation = vi.fn()
const mockGetUsersByIds = vi.fn()

const mockGetUserPermissions = vi.fn().mockResolvedValue({
  permissions: [{ key: 'manage_beacons', enabled: true }],
  mailboxes: [],
})

const mockUser = { memberId: 1, companyId: 1 }

vi.mock('../../clients/ai-services/ai-services', () => ({
  AiServicesApi: vi.fn().mockImplementation(() => ({
    getExternalSources: mockGetExternalSources,
    createExternalSource: mockCreateExternalSource,
    resyncExternalSource: mockResyncExternalSource,
    deleteExternalSource: mockDeleteExternalSource,
    updateExternalSource: mockUpdateExternalSource,
    getWebsiteMap: mockGetWebsiteMap,
    generateImprovement: mockGenerateImprovement,
    getExternalSourcesByConversation: mockGetExternalSourcesByConversation,
  })),
}))

vi.mock('../../clients/core-api/core-api', () => ({
  CoreApi: vi.fn().mockImplementation(() => ({
    getUserPermissions: mockGetUserPermissions,
    getUsersByIds: mockGetUsersByIds,
  })),
}))

const mockCoreApi = new CoreApi(mockUser)
const mockAiServicesApi = new AiServicesApi(mockUser)

describe('externalSourcesRouter', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  function getCaller() {
    return createCaller({
      user: mockUser,
      organizationService: null,
      coreApi: mockCoreApi,
      aiServicesApi: mockAiServicesApi,
      prisma: null as unknown as PrismaClient,
      notificationsClient: null,
    })
  }

  describe('getExternalSources', () => {
    it('returns external sources', async () => {
      mockGetExternalSources.mockResolvedValueOnce({ sources: [{ id: 1 }] })
      const caller = getCaller()
      const result = await caller.externalSources.getExternalSources()
      expect(result).toEqual({ sources: [{ id: 1 }] })
    })

    it('enriches snippet sources with user data', async () => {
      const mockSources = [
        {
          id: 1,
          type: 'snippet',
          name: 'Test Snippet',
          text: 'Test text',
          createdBy: 789,
          updatedBy: 456,
        },
        {
          id: 2,
          type: 'public_site',
          url: 'https://example.com',
        },
      ]

      const mockUsers = {
        items: [
          {
            id: 789,
            first: 'John',
            last: 'Doe',
            email: '<EMAIL>',
          },
          {
            id: 456,
            first: 'Jane',
            last: 'Smith',
            email: '<EMAIL>',
          },
        ],
      }

      mockGetExternalSources.mockResolvedValueOnce({ sources: mockSources })
      mockGetUsersByIds.mockResolvedValueOnce(mockUsers)

      const caller = getCaller()
      const result = await caller.externalSources.getExternalSources()

      expect(mockGetUsersByIds).toHaveBeenCalledWith([789, 456])
      expect(result.sources).toEqual([
        {
          ...mockSources[0],
          createdByUser: mockUsers.items[0],
          updatedByUser: mockUsers.items[1],
        },
        mockSources[1], // public_site source should remain unchanged
      ])
    })

    it('handles sources with undefined user IDs', async () => {
      const mockSources = [
        {
          id: 1,
          type: 'snippet',
          name: 'Test Snippet',
          text: 'Test text',
          createdBy: undefined,
          updatedBy: null,
        },
      ]

      mockGetExternalSources.mockResolvedValueOnce({ sources: mockSources })
      const caller = getCaller()
      const result = await caller.externalSources.getExternalSources()

      expect(mockGetUsersByIds).not.toHaveBeenCalled()
      expect(result.sources).toEqual([mockSources[0]])
    })

    it('handles more than 50 user IDs by making multiple parallel requests', async () => {
      const mockSources = Array.from({ length: 100 }, (_, index) => ({
        id: index + 1,
        type: 'snippet',
        name: `Test ${index + 1}`,
        text: `Test text ${index + 1}`,
        createdBy: index + 1,
        updatedBy: index + 1000,
      }))

      const firstChunkUsers = {
        items: Array.from({ length: 50 }, (_, index) => ({
          id: index + 1,
          first: `User${index + 1}`,
          last: 'Creator',
        })),
      }

      const secondChunkUsers = {
        items: Array.from({ length: 50 }, (_, index) => ({
          id: index + 51,
          first: `User${index + 51}`,
          last: 'Creator',
        })),
      }

      const thirdChunkUsers = {
        items: Array.from({ length: 50 }, (_, index) => ({
          id: index + 1001,
          first: `User${index + 1001}`,
          last: 'Updater',
        })),
      }

      const fourthChunkUsers = {
        items: Array.from({ length: 50 }, (_, index) => ({
          id: index + 1051,
          first: `User${index + 1051}`,
          last: 'Updater',
        })),
      }

      mockGetExternalSources.mockResolvedValueOnce({ sources: mockSources })

      // Mock the getUsersByIds to be called 4 times (2 chunks for creators, 2 chunks for updaters)
      mockGetUsersByIds
        .mockResolvedValueOnce(firstChunkUsers)
        .mockResolvedValueOnce(secondChunkUsers)
        .mockResolvedValueOnce(thirdChunkUsers)
        .mockResolvedValueOnce(fourthChunkUsers)

      const caller = getCaller()
      const result = await caller.externalSources.getExternalSources()

      expect(mockGetUsersByIds).toHaveBeenCalledTimes(4)
      expect(result.sources).toHaveLength(100)
    })

    it('throws INTERNAL_SERVER_ERROR on failure', async () => {
      mockGetExternalSources.mockRejectedValueOnce(new Error('fail'))
      const caller = getCaller()
      try {
        await caller.externalSources.getExternalSources()
      } catch (error) {
        const err = error as TRPCError
        expect(err).toBeInstanceOf(TRPCError)
        expect(err.code).toBe('INTERNAL_SERVER_ERROR')
        expect(err.message).toBe('Failed to fetch external sources')
      }
    })
  })

  describe('createExternalSource', () => {
    it('creates snippet source', async () => {
      mockCreateExternalSource.mockResolvedValueOnce({ id: 42 })
      const caller = getCaller()
      const input = {
        type: 'snippet' as const,
        text: 'abc',
        name: 'Test Snippet',
        convoId: 123,
        createdBy: 1,
      }
      const result = await caller.externalSources.createExternalSource(input)
      expect(result).toEqual({ id: 42 })
      expect(mockCreateExternalSource).toHaveBeenCalledWith(input)
    })

    it('creates public_site source', async () => {
      mockCreateExternalSource.mockResolvedValueOnce({ id: 99 })
      const caller = getCaller()
      const input = {
        type: 'public_site' as const,
        url: 'https://a.com',
        pageLimit: 2,
        excludedUrlPaths: ['/a'],
      }
      const result = await caller.externalSources.createExternalSource(input)
      expect(result).toEqual({ id: 99 })
      expect(mockCreateExternalSource).toHaveBeenCalledWith(input)
    })

    it('throws BAD_REQUEST on AiServiceError', async () => {
      const error = new AiServiceError('BAD_INPUT', 'Test')
      mockCreateExternalSource.mockRejectedValueOnce(error)
      const caller = getCaller()
      try {
        await caller.externalSources.createExternalSource({
          type: 'snippet',
          text: 'fail',
          name: 'Test',
          convoId: 123,
        })
      } catch (error) {
        const err = error as TRPCError
        expect(err).toBeInstanceOf(TRPCError)
        expect(err.code).toBe('BAD_REQUEST')
        expect(err.message).toBe('BAD_INPUT')
      }
    })

    it('throws INTERNAL_SERVER_ERROR on generic error', async () => {
      mockCreateExternalSource.mockRejectedValueOnce(new Error('fail'))
      const caller = getCaller()
      try {
        await caller.externalSources.createExternalSource({
          type: 'snippet',
          text: 'fail',
          name: 'Test',
          convoId: 123,
        })
      } catch (error) {
        const err = error as TRPCError
        expect(err).toBeInstanceOf(TRPCError)
        expect(err.code).toBe('INTERNAL_SERVER_ERROR')
        expect(err.message).toBe(
          'An unexpected error occurred while creating the external source.'
        )
      }
    })
  })

  describe('resyncExternalSource', () => {
    it('calls resync', async () => {
      mockResyncExternalSource.mockResolvedValueOnce(undefined)
      const caller = getCaller()
      await caller.externalSources.resyncExternalSource(123)
      expect(mockResyncExternalSource).toHaveBeenCalledWith(123)
    })

    it('throws INTERNAL_SERVER_ERROR on failure', async () => {
      mockResyncExternalSource.mockRejectedValueOnce(new Error('fail'))
      const caller = getCaller()
      await expect(
        caller.externalSources.resyncExternalSource(123)
      ).rejects.toThrow(
        new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: 'Failed to resync external source(s)',
        })
      )
    })
  })

  describe('deleteExternalSource', () => {
    it('calls delete', async () => {
      mockDeleteExternalSource.mockResolvedValueOnce(undefined)
      const caller = getCaller()
      await caller.externalSources.deleteExternalSource({
        externalSourceId: 123,
      })
      expect(mockDeleteExternalSource).toHaveBeenCalledWith(123)
    })

    it('throws INTERNAL_SERVER_ERROR on failure', async () => {
      mockDeleteExternalSource.mockRejectedValueOnce(new Error('fail'))
      const caller = getCaller()
      await expect(
        caller.externalSources.deleteExternalSource({ externalSourceId: 123 })
      ).rejects.toThrow(
        new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: 'Failed to delete external source',
        })
      )
    })
  })

  describe('updateExternalSource', () => {
    describe('snippet type', () => {
      it('updates snippet with all fields', async () => {
        mockUpdateExternalSource.mockResolvedValueOnce(undefined)
        const caller = getCaller()
        const input = {
          id: 1,
          type: 'snippet' as const,
          text: 'Updated text',
          name: 'Updated name',
          convoId: 123,
        }
        await caller.externalSources.updateExternalSource(input)
        expect(mockUpdateExternalSource).toHaveBeenCalledWith(1, {
          type: 'snippet',
          text: 'Updated text',
          name: 'Updated name',
          convoId: 123,
          updatedBy: 1,
        })
      })

      it('updates snippet with optional fields', async () => {
        mockUpdateExternalSource.mockResolvedValueOnce(undefined)
        const caller = getCaller()
        const input = {
          id: 1,
          type: 'snippet' as const,
          text: 'Updated text',
          name: 'Updated name',
        }
        await caller.externalSources.updateExternalSource(input)
        expect(mockUpdateExternalSource).toHaveBeenCalledWith(1, {
          type: 'snippet',
          text: 'Updated text',
          name: 'Updated name',
          updatedBy: 1,
        })
      })

      it('validates required fields for snippet', async () => {
        const caller = getCaller()
        try {
          await caller.externalSources.updateExternalSource({
            id: 1,
            type: 'snippet' as const,
            text: '', // Invalid - empty string
            name: 'Test',
          })
        } catch (error) {
          expect(error).toBeDefined()
        }
      })
    })

    describe('public_site type', () => {
      it('updates public site with valid input', async () => {
        mockUpdateExternalSource.mockResolvedValueOnce(undefined)
        const caller = getCaller()
        const input = {
          id: 2,
          type: 'public_site' as const,
          pageLimit: 50,
          excludedUrlPaths: ['/private', '/admin'],
        }
        await caller.externalSources.updateExternalSource(input)
        expect(mockUpdateExternalSource).toHaveBeenCalledWith(2, {
          type: 'public_site',
          pageLimit: 50,
          excludedUrlPaths: ['/private', '/admin'],
        })
      })

      it('updates public site with minimal fields', async () => {
        mockUpdateExternalSource.mockResolvedValueOnce(undefined)
        const caller = getCaller()
        const input = {
          id: 2,
          type: 'public_site' as const,
        }
        await caller.externalSources.updateExternalSource(input)
        expect(mockUpdateExternalSource).toHaveBeenCalledWith(2, {
          type: 'public_site',
        })
      })
    })

    it('throws BAD_REQUEST on AiServiceError', async () => {
      const error = new AiServiceError('BAD_INPUT', 'Test')
      mockUpdateExternalSource.mockRejectedValueOnce(error)
      const caller = getCaller()
      try {
        await caller.externalSources.updateExternalSource({
          id: 1,
          type: 'snippet' as const,
          text: 'fail',
          name: 'Test',
        })
      } catch (error) {
        const err = error as TRPCError
        expect(err).toBeInstanceOf(TRPCError)
        expect(err.code).toBe('BAD_REQUEST')
        expect(err.message).toBe('BAD_INPUT')
      }
    })

    it('throws INTERNAL_SERVER_ERROR on generic error', async () => {
      mockUpdateExternalSource.mockRejectedValueOnce(new Error('fail'))
      const caller = getCaller()
      try {
        await caller.externalSources.updateExternalSource({
          id: 1,
          type: 'snippet' as const,
          text: 'fail',
          name: 'Test',
        })
      } catch (error) {
        const err = error as TRPCError
        expect(err).toBeInstanceOf(TRPCError)
        expect(err.code).toBe('INTERNAL_SERVER_ERROR')
        expect(err.message).toBe(
          'An unexpected error occurred while updating the external source.'
        )
      }
    })
  })

  describe('getWebsiteMap', () => {
    it('returns website map', async () => {
      mockGetWebsiteMap.mockResolvedValueOnce({ map: ['a'] })
      const caller = getCaller()
      const result = await caller.externalSources.getWebsiteMap({
        url: 'https://a.com',
      })
      expect(result).toEqual({ map: ['a'] })
    })

    it('throws BAD_REQUEST on AiServiceError', async () => {
      const error = new AiServiceError('BAD_INPUT', 'Test')
      mockGetWebsiteMap.mockRejectedValueOnce(error)
      const caller = getCaller()
      try {
        await caller.externalSources.getWebsiteMap({ url: 'fail' })
      } catch (error) {
        const err = error as TRPCError
        expect(err).toBeInstanceOf(TRPCError)
        expect(err.code).toBe('BAD_REQUEST')
        expect(err.message).toBe('BAD_INPUT')
      }
    })

    it('throws INTERNAL_SERVER_ERROR on generic error', async () => {
      mockGetWebsiteMap.mockRejectedValueOnce(new Error('fail'))
      const caller = getCaller()
      try {
        await caller.externalSources.getWebsiteMap({ url: 'fail' })
      } catch (error) {
        const err = error as TRPCError
        expect(err).toBeInstanceOf(TRPCError)
        expect(err.code).toBe('INTERNAL_SERVER_ERROR')
        expect(err.message).toBe(
          'An unexpected error occurred while getting the website map.'
        )
      }
    })
  })

  describe('generateImprovement', () => {
    it('generates improvement successfully', async () => {
      const mockResponse = {
        title: 'Generated Title',
        text: 'Generated improvement text',
      }
      mockGenerateImprovement.mockResolvedValueOnce(mockResponse)

      const caller = getCaller()

      const input = {
        userImprovement: 'User suggestion',
        conversationId: 123,
      }

      const result = await caller.externalSources.generateImprovement(input)

      expect(result).toEqual(mockResponse)
      expect(mockGenerateImprovement).toHaveBeenCalledWith(input)
    })

    it('does not require permission check', async () => {
      // Reset permissions to empty
      mockGetUserPermissions.mockResolvedValueOnce({
        permissions: [],
        mailboxes: [],
      })

      const mockResponse = {
        title: 'Generated Title',
        text: 'Generated improvement text',
      }
      mockGenerateImprovement.mockResolvedValueOnce(mockResponse)

      const caller = getCaller()

      const input = {
        userImprovement: 'User suggestion',
        conversationId: 123,
      }

      // Should succeed even without permissions
      const result = await caller.externalSources.generateImprovement(input)
      expect(result).toEqual(mockResponse)
    })

    it('throws INTERNAL_SERVER_ERROR on failure', async () => {
      mockGenerateImprovement.mockRejectedValueOnce(
        new Error('Generation failed')
      )

      const caller = getCaller()

      try {
        await caller.externalSources.generateImprovement({
          userImprovement: 'User suggestion',
          conversationId: 123,
        })
      } catch (error) {
        const err = error as TRPCError
        expect(err).toBeInstanceOf(TRPCError)
        expect(err.code).toBe('INTERNAL_SERVER_ERROR')
        expect(err.message).toBe('Failed to generate improvement')
      }
    })

    it('validates input parameters', async () => {
      const caller = getCaller()

      try {
        await caller.externalSources.generateImprovement({
          userImprovement: '',
          conversationId: 123,
        })
      } catch (error) {
        expect(error).toBeDefined()
      }
    })
  })

  describe('getExternalSourcesByConversation', () => {
    it('successfully fetches external sources with user data for a conversation', async () => {
      const mockSources = [
        {
          id: 1,
          name: 'Test Improvement',
          text: 'Test improvement text',
          type: 'snippet',
          convoId: 123,
          createdBy: 789,
          updatedBy: 456,
          createdAt: '2025-06-09T11:50:57.991Z',
          syncedAt: '2025-06-09T11:50:57.991Z',
        },
        {
          id: 2,
          name: 'Another Improvement',
          text: 'Another improvement text',
          type: 'snippet',
          convoId: 123,
          createdBy: 456,
          updatedBy: null,
          createdAt: '2025-06-09T11:50:57.991Z',
          syncedAt: '2025-06-09T11:50:57.991Z',
        },
      ]

      const mockUsers = {
        items: [
          {
            id: 789,
            first: 'John',
            last: 'Doe',
            email: '<EMAIL>',
          },
          {
            id: 456,
            first: 'Jane',
            last: 'Smith',
            email: '<EMAIL>',
          },
        ],
      }

      mockGetExternalSourcesByConversation.mockResolvedValueOnce({
        sources: mockSources,
      })
      mockGetUsersByIds.mockResolvedValueOnce(mockUsers)

      const caller = getCaller()

      const result =
        await caller.externalSources.getExternalSourcesByConversation({
          conversationId: 123,
        })

      expect(mockGetExternalSourcesByConversation).toHaveBeenCalledWith(123)
      expect(mockGetUsersByIds).toHaveBeenCalledWith([789, 456])

      expect(result).toEqual([
        {
          ...mockSources[0],
          createdByUser: mockUsers.items[0],
          updatedByUser: mockUsers.items[1],
        },
        {
          ...mockSources[1],
          createdByUser: mockUsers.items[1],
        },
      ])
    })

    it('throws error when AI service fails', async () => {
      mockGetExternalSourcesByConversation.mockRejectedValueOnce(
        new Error('Service unavailable')
      )

      const caller = getCaller()

      try {
        await caller.externalSources.getExternalSourcesByConversation({
          conversationId: 123,
        })
      } catch (error) {
        const err = error as TRPCError
        expect(err).toBeInstanceOf(TRPCError)
        expect(err.code).toBe('INTERNAL_SERVER_ERROR')
        expect(err.message).toBe(
          'Failed to fetch external sources for conversation'
        )
      }
    })

    it('validates input parameters', async () => {
      const caller = getCaller()

      // Test with invalid input
      try {
        await caller.externalSources.getExternalSourcesByConversation({
          conversationId: 'not-a-number' as any,
        })
      } catch (error) {
        expect(error).toBeDefined()
      }

      // Test with missing input
      try {
        await caller.externalSources.getExternalSourcesByConversation({} as any)
      } catch (error) {
        expect(error).toBeDefined()
      }
    })

    it('returns empty array when no sources exist', async () => {
      mockGetExternalSourcesByConversation.mockResolvedValueOnce({
        sources: [],
      })

      const caller = getCaller()

      const result =
        await caller.externalSources.getExternalSourcesByConversation({
          conversationId: 123,
        })

      expect(result).toEqual([])
      expect(mockGetUsersByIds).not.toHaveBeenCalled()
    })

    it('handles null response from API', async () => {
      mockGetExternalSourcesByConversation.mockResolvedValueOnce(null)
      mockGetUsersByIds.mockResolvedValueOnce({ items: [] })

      const caller = getCaller()

      const result =
        await caller.externalSources.getExternalSourcesByConversation({
          conversationId: 123,
        })

      expect(result).toEqual([])
      expect(mockGetUsersByIds).not.toHaveBeenCalled()
    })

    it('handles sources with undefined user IDs', async () => {
      const mockSources = [
        {
          id: 1,
          name: 'Test',
          text: 'Test text',
          type: 'snippet',
          convoId: 123,
          createdBy: undefined,
          updatedBy: null,
          createdAt: '2025-06-09T11:50:57.991Z',
          syncedAt: '2025-06-09T11:50:57.991Z',
        },
      ]

      mockGetExternalSourcesByConversation.mockResolvedValueOnce({
        sources: mockSources,
      })
      mockGetUsersByIds.mockResolvedValueOnce({ items: [] })

      const caller = getCaller()

      const result =
        await caller.externalSources.getExternalSourcesByConversation({
          conversationId: 123,
        })

      expect(mockGetUsersByIds).not.toHaveBeenCalled()
      expect(result).toEqual([mockSources[0]])
    })

    it('handles duplicate user IDs correctly', async () => {
      const mockSources = [
        {
          id: 1,
          name: 'Test 1',
          text: 'Test text 1',
          type: 'snippet',
          convoId: 123,
          createdBy: 789,
          updatedBy: 789,
          createdAt: '2025-06-09T11:50:57.991Z',
          syncedAt: '2025-06-09T11:50:57.991Z',
        },
        {
          id: 2,
          name: 'Test 2',
          text: 'Test text 2',
          type: 'snippet',
          convoId: 123,
          createdBy: 789,
          updatedBy: 456,
          createdAt: '2025-06-09T11:50:57.991Z',
          syncedAt: '2025-06-09T11:50:57.991Z',
        },
      ]

      const mockUsers = {
        items: [
          {
            id: 789,
            first: 'John',
            last: 'Doe',
          },
          {
            id: 456,
            first: 'Jane',
            last: 'Smith',
          },
        ],
      }

      mockGetExternalSourcesByConversation.mockResolvedValueOnce({
        sources: mockSources,
      })
      mockGetUsersByIds.mockResolvedValueOnce(mockUsers)
      const caller = getCaller()

      await caller.externalSources.getExternalSourcesByConversation({
        conversationId: 123,
      })

      // Should only call getUsersByIds with unique user IDs
      expect(mockGetUsersByIds).toHaveBeenCalledWith([789, 456])
    })

    it('handles more than 50 user IDs by making multiple parallel requests', async () => {
      // Generate 100 unique user IDs (more than the 50 limit)
      const mockSources = Array.from({ length: 100 }, (_, index) => ({
        id: index + 1,
        name: `Test ${index + 1}`,
        text: `Test text ${index + 1}`,
        type: 'snippet',
        convoId: 123,
        createdBy: index + 1,
        updatedBy: index + 1000, // Different user for updatedBy
        createdAt: '2025-06-09T11:50:57.991Z',
        syncedAt: '2025-06-09T11:50:57.991Z',
      }))

      const firstChunkUsers = {
        items: Array.from({ length: 50 }, (_, index) => ({
          id: index + 1,
          first: `User${index + 1}`,
          last: 'Creator',
        })),
      }

      const secondChunkUsers = {
        items: Array.from({ length: 50 }, (_, index) => ({
          id: index + 51,
          first: `User${index + 51}`,
          last: 'Creator',
        })),
      }

      const thirdChunkUsers = {
        items: Array.from({ length: 50 }, (_, index) => ({
          id: index + 1001,
          first: `User${index + 1001}`,
          last: 'Updater',
        })),
      }

      const fourthChunkUsers = {
        items: Array.from({ length: 50 }, (_, index) => ({
          id: index + 1051,
          first: `User${index + 1051}`,
          last: 'Updater',
        })),
      }

      mockGetExternalSourcesByConversation.mockResolvedValueOnce({
        sources: mockSources,
      })

      // Mock the getUsersByIds to be called 4 times (2 chunks for creators, 2 chunks for updaters)
      mockGetUsersByIds
        .mockResolvedValueOnce(firstChunkUsers)
        .mockResolvedValueOnce(secondChunkUsers)
        .mockResolvedValueOnce(thirdChunkUsers)
        .mockResolvedValueOnce(fourthChunkUsers)

      const caller = getCaller()

      const result =
        await caller.externalSources.getExternalSourcesByConversation({
          conversationId: 123,
        })

      expect(mockGetUsersByIds).toHaveBeenCalledTimes(4)

      expect(result).toHaveLength(100)
    })
  })
})
