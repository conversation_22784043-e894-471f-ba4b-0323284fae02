import { AiServiceError } from '../../clients/ai-services/errors'
import { ExternalSource, SnippetSource } from '../../clients/ai-services/types'
import { CoreApi } from '../../clients/core-api/core-api'
import { ensureUserHasPermission } from '../../middleware/ensure-user-has-permission'
import { router, authenticatedProcedure } from '../../trpc'
import { TRPCError } from '@trpc/server'
import { Improvement, ImprovementWithUser } from 'shared/types/Improvement'
import { z } from 'zod'

export const externalSourcesRouter = router({
  getExternalSources: authenticatedProcedure
    .use(ensureUserHasPermission('manage_beacons'))
    .query(async ({ ctx }) => {
      const { aiServicesApi, coreApi } = ctx

      try {
        const response = await aiServicesApi.getExternalSources()
        const enrichedSources = await enrichExternalSourcesWithUsers(
          response.sources,
          coreApi
        )
        return {
          ...response,
          sources: enrichedSources,
        }
      } catch (error) {
        console.error('Error fetching external sources:', error)
        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: 'Failed to fetch external sources',
          cause: error instanceof Error ? error : undefined,
        })
      }
    }),

  createExternalSource: authenticatedProcedure
    .input(
      z.discriminatedUnion('type', [
        z.object({
          type: z.literal('snippet'),
          text: z.string().min(1),
          name: z.string().min(1),
          convoId: z.number().optional(),
        }),
        z.object({
          type: z.literal('public_site'),
          url: z.string(),
          pageLimit: z.number().optional(),
          excludedUrlPaths: z.array(z.string()).optional(),
        }),
      ])
    )
    .mutation(async ({ ctx, input }) => {
      const { aiServicesApi, user } = ctx

      try {
        const data =
          input.type === 'snippet'
            ? { ...input, createdBy: user.memberId }
            : input
        const newSource = await aiServicesApi.createExternalSource(data)
        return { id: newSource.id }
      } catch (error) {
        console.error('Error creating external source:', error)
        if (error instanceof AiServiceError) {
          throw new TRPCError({
            code: 'BAD_REQUEST',
            message: error.code,
            cause: error,
          })
        }
        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message:
            'An unexpected error occurred while creating the external source.',
          cause: error instanceof Error ? error : undefined,
        })
      }
    }),

  resyncExternalSource: authenticatedProcedure
    .input(z.number())
    .use(ensureUserHasPermission('manage_beacons'))
    .mutation(async ({ ctx, input }) => {
      const { aiServicesApi } = ctx
      try {
        await aiServicesApi.resyncExternalSource(input)
      } catch (error) {
        console.error('Error resyncing external source(s):', error)
        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: 'Failed to resync external source(s)',
          cause: error instanceof Error ? error : undefined,
        })
      }
    }),

  deleteExternalSource: authenticatedProcedure
    .input(
      z.object({
        externalSourceId: z.number(),
      })
    )
    .mutation(async ({ ctx, input }) => {
      const { aiServicesApi } = ctx
      try {
        await aiServicesApi.deleteExternalSource(input.externalSourceId)
      } catch (error) {
        console.error('Error deleting external source:', error)
        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: 'Failed to delete external source',
          cause: error instanceof Error ? error : undefined,
        })
      }
    }),

  updateExternalSource: authenticatedProcedure
    .input(
      z.discriminatedUnion('type', [
        z.object({
          id: z.number(),
          type: z.literal('snippet'),
          text: z.string().min(1),
          name: z.string().min(1),
          convoId: z.number().optional(),
        }),
        z.object({
          id: z.number(),
          type: z.literal('public_site'),
          pageLimit: z.number().optional(),
          excludedUrlPaths: z.array(z.string()).optional(),
        }),
      ])
    )
    .mutation(async ({ ctx, input }) => {
      const { aiServicesApi, user } = ctx
      const { id, ...updateData } = input
      const data =
        updateData.type === 'snippet'
          ? { ...updateData, updatedBy: user.memberId }
          : updateData
      try {
        await aiServicesApi.updateExternalSource(id, data)
      } catch (error) {
        console.error('Error updating external source:', error)
        if (error instanceof AiServiceError) {
          throw new TRPCError({
            code: 'BAD_REQUEST',
            message: error.code,
            cause: error,
          })
        }
        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message:
            'An unexpected error occurred while updating the external source.',
          cause: error instanceof Error ? error : undefined,
        })
      }
    }),

  getWebsiteMap: authenticatedProcedure
    .input(z.object({ url: z.string() }))
    .use(ensureUserHasPermission('manage_beacons'))
    .query(async ({ ctx, input }) => {
      const { aiServicesApi } = ctx
      try {
        return await aiServicesApi.getWebsiteMap(input.url)
      } catch (error) {
        console.error('Error getting website map:', error)
        if (error instanceof AiServiceError) {
          throw new TRPCError({
            code: 'BAD_REQUEST',
            message: error.code,
            cause: error,
          })
        }
        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message:
            'An unexpected error occurred while getting the website map.',
          cause: error instanceof Error ? error : undefined,
        })
      }
    }),

  generateImprovement: authenticatedProcedure
    .input(
      z.object({
        userImprovement: z.string().min(1),
        conversationId: z.number().optional(),
      })
    )
    .mutation(async ({ ctx, input }) => {
      const { aiServicesApi } = ctx

      try {
        return await aiServicesApi.generateImprovement(input)
      } catch (error) {
        console.error('Error generating improvement:', error)
        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: 'Failed to generate improvement',
          cause: error instanceof Error ? error : undefined,
        })
      }
    }),

  getExternalSourcesByConversation: authenticatedProcedure
    .input(
      z.object({
        conversationId: z.number(),
      })
    )
    .query(async ({ ctx, input }) => {
      const { aiServicesApi, coreApi } = ctx

      try {
        const sources =
          (
            await aiServicesApi.getExternalSourcesByConversation(
              input.conversationId
            )
          )?.sources ?? []
        return await enrichImprovementsWithUsers(sources, coreApi)
      } catch (error) {
        console.error(
          'Error fetching external sources for conversation:',
          error
        )
        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: 'Failed to fetch external sources for conversation',
          cause: error instanceof Error ? error : undefined,
        })
      }
    }),
})

async function enrichImprovementsWithUsers(
  improvements: Improvement[],
  coreApi: CoreApi
) {
  const userIds = improvements
    .flatMap(source => [source.createdBy, source.updatedBy])
    .filter((item): item is number => item !== undefined && item !== null)

  // Split user IDs into chunks of 50 to respect API limit
  const allUsers = await getUsersInChunks(userIds, coreApi)

  const usersMap = new Map(allUsers.map(user => [user.id, user]))

  return improvements.map((source: ImprovementWithUser) => ({
    ...source,
    createdByUser: usersMap.get(source.createdBy),
    ...(source.updatedBy
      ? { updatedByUser: usersMap.get(source.updatedBy) }
      : {}),
  }))
}

async function getUsersInChunks(userIds: number[], coreApi: CoreApi) {
  if (userIds.length > 0) {
    const uniqueUserIds = Array.from(new Set(userIds))

    const chunks: number[][] = []
    for (let i = 0; i < uniqueUserIds.length; i += 50) {
      chunks.push(uniqueUserIds.slice(i, i + 50))
    }

    const userResponses = await Promise.all(
      chunks.map(chunk => coreApi.getUsersByIds(chunk))
    )

    return userResponses.flatMap(response => response.items)
  }
  return []
}

async function enrichExternalSourcesWithUsers(
  sources: ExternalSource[],
  coreApi: CoreApi
) {
  const userIds = sources
    .filter((source): source is SnippetSource => source.type === 'snippet')
    .flatMap(source => [source.createdBy, source.updatedBy])
    .filter((item): item is number => item !== undefined && item !== null)

  const allUsers = await getUsersInChunks(userIds, coreApi)

  const usersMap = new Map(allUsers.map(user => [user.id, user]))

  return sources.map(source => {
    if (source.type === 'snippet' && source.createdBy !== undefined) {
      return {
        ...source,
        createdByUser: usersMap.get(source.createdBy),
        ...(source.updatedBy !== undefined
          ? { updatedByUser: usersMap.get(source.updatedBy) }
          : {}),
      }
    }
    return source
  })
}
