export type SyncStatus = 'completed' | 'in_progress' | 'failed'

interface BaseSource {
  id: number
  syncedAt: string
}

export interface PublicSiteSource extends BaseSource {
  type: 'public_site'
  name: string
  url: string
  pageCount: number
  pageLimit?: number
  excludedUrlPaths: string[]
  syncStatus: SyncStatus
  syncError?: string
}

export interface SnippetSource extends BaseSource {
  type: 'snippet'
  text: string
  createdBy: number
  updatedBy?: number
}

export type ExternalSource = PublicSiteSource | SnippetSource

export interface ExternalSourcesResponse {
  companyPageCount: number
  companyPageLimit: number
  pageLimitExceeded: boolean
  sources: ExternalSource[]
}

interface SiteMap {
  path: string
  totalPageCount: number
  siteMaps: SiteMap[]
}

export interface WebsiteMap {
  rootUrl: string
  siteMap: SiteMap
}

export interface AiServiceErrorResponse {
  code: string
  error: string
}
