import React, { useContext } from 'react'

import classNames from 'classnames'
import DOMPurify from 'dompurify'
import PropTypes from 'prop-types'

import Icon from 'hsds/components/icon'
import Text from 'hsds/components/text'
import TypingDots from 'hsds/components/typing-dots'
import { getValidProps, isNativeSpanType } from 'hsds/utils/react'
import { convertLinksToHTML, newlineToHTML } from 'hsds/utils/strings'

import { compose, isWord, textIncludesOnlyEmoji } from './Message.utils'

import {
  MessageBubbleBodyUI,
  MessageBubbleFromUI,
  MessageBubbleIconWrapperUI,
  MessageBubbleTitleUI,
  MessageBubbleTypingUI,
  MessageBubbleUI,
} from './Message.Bubble.styles'

import { MessageThemeContext } from './Message.Provider'

// convertLinksToHTML will escape for output as HTML
const enhanceBody = compose(newlineToHTML, convertLinksToHTML)

export const MessageBubble = ({
  'data-cy': dataCy = 'MessageBubble',
  body,
  children,
  className,
  from,
  icon,
  isNote,
  ltr,
  rtl,
  size,
  timestamp,
  title,
  to,
  typing,
  ...rest
}) => {
  const messageContext = useContext(MessageThemeContext) || {}
  const { themeName } = messageContext
  const isThemeNotifications = themeName === 'notifications'
  const isThemeEmbed = themeName === 'embed'
  const fromName = from && typeof from === 'string' ? from : null
  const hasOnlyOneChild = React.Children.count(children) === 1
  let showEmojiOnlyStyles = false
  const childrenMarkup = React.Children.map(children, child => {
    showEmojiOnlyStyles =
      !isThemeEmbed && hasOnlyOneChild && textIncludesOnlyEmoji(child)
    const fontSize = isThemeEmbed ? '13' : showEmojiOnlyStyles ? 48 : '14'

    return isWord(child) || isNativeSpanType(child) ? (
      <MessageBubbleBodyUI
        className={classNames(
          'c-MessageBubble__body',
          isThemeEmbed && 'is-theme-embed',
          showEmojiOnlyStyles && 'with-emoji-only-styles'
        )}
      >
        <Text wordWrap lineHeightInherit size={fontSize}>
          {child}
        </Text>
      </MessageBubbleBodyUI>
    ) : (
      child
    )
  })

  const renderBody = () => {
    if (!body) {
      return childrenMarkup
    }

    showEmojiOnlyStyles = !isThemeEmbed && textIncludesOnlyEmoji(body)

    if (showEmojiOnlyStyles) {
      return (
        <MessageBubbleBodyUI className="c-MessageBubble__body">
          <Text wordWrap lineHeightInherit size={48}>
            {body}
          </Text>
        </MessageBubbleBodyUI>
      )
    }
    return (
      <MessageBubbleBodyUI
        className="c-MessageBubble__body"
        dangerouslySetInnerHTML={{
          __html: enhanceBody(DOMPurify.sanitize(body)),
        }}
      />
    )
  }

  return (
    <MessageBubbleUI
      {...getValidProps(rest)}
      data-cy={dataCy}
      className={classNames(
        'c-MessageBubble',
        from && 'is-from',
        icon && 'withIcon',
        isNote && 'is-note',
        size && `is-${size}`,
        ltr && !rtl && 'is-ltr',
        !ltr && rtl && 'is-rtl',
        themeName && `is-theme-${themeName}`,
        to && 'is-to',
        typing && 'is-typing',
        showEmojiOnlyStyles && 'emoji-only',
        showEmojiOnlyStyles && !isNote && 'with-emoji-only-styles',
        className
      )}
    >
      {isThemeNotifications && fromName ? (
        <MessageBubbleFromUI
          className={classNames(
            'c-MessageBubble__from',
            isThemeEmbed && 'is-theme-embed'
          )}
        >
          <Text className="c-MessageBubble__fromText" lineHeightReset size="11">
            {fromName}
          </Text>
        </MessageBubbleFromUI>
      ) : null}
      {title ? (
        <MessageBubbleTitleUI className="c-MessageBubble__title" size="small">
          {title}
        </MessageBubbleTitleUI>
      ) : null}
      <div className="c-MessageBubble__content">
        {icon ? (
          <MessageBubbleIconWrapperUI className="c-MessageBubble__iconWrapper">
            <Icon
              className="c-MessageBubble__icon"
              icon={icon}
              size="20"
              shade="extraMuted"
            />
          </MessageBubbleIconWrapperUI>
        ) : null}
        <div className="c-MessageBubble__bodyWrapper">
          {typing ? (
            <MessageBubbleTypingUI className="c-MessageBubble__typing">
              <TypingDots />
            </MessageBubbleTypingUI>
          ) : (
            renderBody()
          )}
        </div>
      </div>
    </MessageBubbleUI>
  )
}

MessageBubble.propTypes = {
  body: PropTypes.string,
  /** Custom class names to be added to the component. */
  className: PropTypes.string,
  /** Provides author information and applies "From" styles. */
  from: PropTypes.any,
  /** Applies "note" styles. */
  isNote: PropTypes.bool,
  /** Applies left-to-right text styles. */
  ltr: PropTypes.bool,
  /** Applies "primary" styles. */
  primary: PropTypes.bool,
  /** Determines if the Message is read. */
  read: PropTypes.bool,
  /** Applies right-to-left text styles. */
  rtl: PropTypes.bool,
  /** Determines the size of the component. */
  size: PropTypes.oneOf(['md', 'sm', '']),
  /** Timestamp for the Message. */
  timestamp: PropTypes.string,
  /** Renders a `Heading` title in the component. */
  title: PropTypes.string,
  /** Provides author information and applies "To" styles. */
  to: PropTypes.any,
  /** Renders `TypingDots` within the component. */
  typing: PropTypes.bool,
  /** Callback when clicked. */
  onClick: PropTypes.func,
  /** The icon to render. */
  icon: PropTypes.oneOfType([PropTypes.func, PropTypes.element]),
  /** Data attr for Cypress tests. */
  'data-cy': PropTypes.string,
}

export default MessageBubble
