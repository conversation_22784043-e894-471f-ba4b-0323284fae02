import { render } from '@testing-library/react'

import Attachment from 'hsds/icons/paperclip'

import Message from '../'
import MessageBubble from '../Message.Bubble'

const cx = 'c-MessageBubble'
const ui = {
  body: `.${cx}__body`,
  bodyWrapper: `.${cx}__bodyWrapper`,
  from: `.${cx}__from`,
  iconWrapper: `.${cx}__iconWrapper`,
  icon: `.${cx}__icon`,
  title: `.${cx}__title`,
}

describe('ClassName', () => {
  test('Has default className', () => {
    const { container } = render(<MessageBubble />)

    expect(container.querySelector('.c-MessageBubble')).toBeInTheDocument()
  })

  test('Applies custom className if specified', () => {
    const customClass = 'piano-key-neck-tie'
    const { container } = render(<MessageBubble className={customClass} />)

    expect(container.querySelector('.c-MessageBubble')).toHaveClass(customClass)
  })
})

describe('Content', () => {
  test('Can render children', () => {
    const { getByText } = render(
      <MessageBubble>
        <span className="child">Hello</span>
      </MessageBubble>
    )

    expect(getByText('Hello')).toBeInTheDocument()
  })
})

describe('Title', () => {
  test('Does not render a Title by default', () => {
    const { container } = render(<MessageBubble />)
    const o = container.querySelector(ui.title)

    expect(o).not.toBeInTheDocument()
  })

  test('Renders a Title if defined', () => {
    const { container } = render(<MessageBubble title="Mugatu" />)
    const o = container.querySelector(ui.title)

    expect(o).toBeInTheDocument()
    expect(o).toHaveClass('c-MessageBubble__title')
    expect(o).toHaveTextContent('Mugatu')
  })
})

describe('Typing', () => {
  test('Does not render a TypingDots by default', () => {
    const { container } = render(<MessageBubble />)
    const o = container.querySelector('.c-TypingDots')

    expect(o).not.toBeInTheDocument()
  })

  test('Renders TypingDots if typing', () => {
    const { container } = render(<MessageBubble typing />)
    const o = container.querySelector('.c-TypingDots')

    expect(o).toBeInTheDocument()
  })

  test('Renders TypingDots instead of children if typing', () => {
    const { container } = render(<MessageBubble typing>Mugatu</MessageBubble>)
    const o = container.querySelector('.c-TypingDots')

    expect(o).toBeInTheDocument()
    expect(o.innerHTML).not.toContain('Mugatu')
  })
})

describe('Content', () => {
  test('Text-based content is contained with a wordWrapped Text component', () => {
    const { container } = render(<MessageBubble>Mugatu</MessageBubble>)
    const o = container.querySelector('.c-Text')

    expect(o).toBeInTheDocument()
    expect(o).toHaveClass('is-wordWrap')
  })

  test('Span-based content is contained with a wordWrapped Text component', () => {
    const { container } = render(
      <MessageBubble>
        <span>Mugatu</span>
      </MessageBubble>
    )
    const o = container.querySelector('.c-Text')

    expect(o).toBeInTheDocument()
    expect(o.innerHTML).toContain('Mugatu')
  })

  test('Block-based content is not contained with a Text component', () => {
    const { container } = render(
      <MessageBubble>
        <div>Mugatu</div>
      </MessageBubble>
    )
    const o = container.querySelector('.c-Text')

    expect(o).not.toBeInTheDocument()
    expect(container.innerHTML).toContain('Mugatu')
  })

  test('Renders body if defined', () => {
    const { container } = render(<MessageBubble body="Mugatu" />)
    const o = container.querySelector(ui.body)

    expect(o).toBeInTheDocument()
    expect(container.innerHTML).toContain('Mugatu')
  })

  test('Renders body instead of children, if defined', () => {
    const { container } = render(
      <MessageBubble body="Mugatu">Zoolander</MessageBubble>
    )
    const o = container.querySelector(ui.body)

    expect(o).toBeInTheDocument()
    expect(container.innerHTML).toContain('Mugatu')
    expect(container.innerHTML).not.toContain('Zoolander')
  })

  test('Renders body with autolinked URLs', () => {
    const body = 'www.helpscout.com'
    const { container } = render(<MessageBubble body={body} />)
    const o = container.querySelector(ui.body)

    expect(o).toBeInTheDocument()
    expect(container.innerHTML).toContain('<a href="http://www.helpscout.com"')
  })

  describe('when body includes only emoji content', () => {
    test('Renders body with Text component', () => {
      const { container } = render(<MessageBubble body="🙄" />)
      const o = container.querySelector(ui.body)
      const includesTextElement = container.querySelector('.c-Text')

      expect(o).toBeInTheDocument()
      expect(includesTextElement).toBeInTheDocument()
      expect(container.innerHTML).toContain('🙄')
    })
  })

  test('Converts newlines to line break elements', () => {
    const body = 'Hello\n\nGoodbye'
    const { container } = render(<MessageBubble body={body} />)
    const o = container.querySelector(ui.body)

    expect(o).toBeInTheDocument()
    expect(container.innerHTML).toContain('Hello<br><br>Goodbye')
  })

  test('Escapes HTML in the body', () => {
    const html = '<div>Mugatu</div>'
    const { container } = render(<MessageBubble body={html} />)
    const o = container.querySelector(ui.body)
    const parsedHTML = '&lt;div&gt;Mugatu&lt;/div&gt;'

    expect(o).toBeInTheDocument()
    expect(container.innerHTML).toContain(parsedHTML)
  })

  test('font size should be 14', () => {
    const { container } = render(<MessageBubble to>Bye TomO</MessageBubble>)
    const o = container.querySelector('.c-Text')

    expect(o).toHaveClass('is-14')
  })

  describe('text that includes only emoji', () => {
    describe('when themeName is not embed', () => {
      test('font size should be 48', () => {
        const { container } = render(<MessageBubble from>🐐🦄</MessageBubble>)
        const o = container.querySelector('.c-Text')

        expect(o).toHaveClass('is-48')
      })
    })
    describe('when themeName is embed', () => {
      test('font size should be 13', () => {
        const { container } = render(
          <Message.Provider themeName="embed">
            <MessageBubble from>🐐🦄</MessageBubble>
          </Message.Provider>
        )
        const o = container.querySelector('.c-Text')

        expect(o).toHaveClass('is-13')
      })
    })
  })
})

describe('Styles', () => {
  test('Applies "from" styles, if defined', () => {
    const { container } = render(<MessageBubble from />)

    expect(container.querySelector('.c-MessageBubble')).toHaveClass('is-from')
  })

  test('Applies "to" styles, if defined', () => {
    const { container } = render(<MessageBubble to />)

    expect(container.querySelector('.c-MessageBubble')).toHaveClass('is-to')
  })

  test('Applies "note" styles, if defined', () => {
    const { container } = render(<MessageBubble isNote />)

    expect(container.querySelector('.c-MessageBubble')).toHaveClass('is-note')
  })

  test('Applies "size" styles, if defined', () => {
    const { container } = render(<MessageBubble size="sm" />)

    expect(container.querySelector('.c-MessageBubble')).toHaveClass('is-sm')
  })

  test('Applies "ltr" styles, if defined', () => {
    const { container } = render(<MessageBubble ltr />)

    expect(container.querySelector('.c-MessageBubble')).toHaveClass('is-ltr')
  })

  test('Applies "rtl" styles, if defined', () => {
    const { container } = render(<MessageBubble rtl />)

    expect(container.querySelector('.c-MessageBubble')).toHaveClass('is-rtl')
  })

  test('Applies "typing" styles, if defined', () => {
    const { container } = render(<MessageBubble typing />)

    expect(container.querySelector('.c-MessageBubble')).toHaveClass('is-typing')
  })
})

describe('Context', () => {
  test('Adds className based on context.themeName', () => {
    const { container } = render(
      <Message.Provider themeName="embed">
        <MessageBubble />
      </Message.Provider>
    )

    expect(container.querySelector('.c-MessageBubble')).toHaveClass(
      'is-theme-embed'
    )
  })
})

describe('From', () => {
  test('It does not render a from name, by default', () => {
    const { container } = render(<MessageBubble from="Mugatu" />)
    const o = container.querySelector(ui.from)

    expect(o).not.toBeInTheDocument()
  })

  test('Does not render from name, if themeName is notifications, but from is not provided', () => {
    const { container } = render(
      <Message.Provider themeName="notifications">
        <MessageBubble />
      </Message.Provider>
    )
    const o = container.querySelector(ui.from)

    expect(o).not.toBeInTheDocument()
  })

  test('Does not renders from name, if themeName is notifications and from is not a string', () => {
    const { container } = render(
      <Message.Provider themeName="notifications">
        <MessageBubble from />
      </Message.Provider>
    )
    const o = container.querySelector(ui.from)

    expect(o).not.toBeInTheDocument()
  })

  test('Renders from name, if themeName is notifications and from is a string', () => {
    const { container } = render(
      <Message.Provider themeName="notifications">
        <MessageBubble from="Mugatu" />
      </Message.Provider>
    )
    const o = container.querySelector(ui.from)

    expect(o).toBeInTheDocument()
    expect(o.innerHTML).toContain('Mugatu')
  })
})

describe('Icon', () => {
  test('Does not render an icon by default', () => {
    const { container } = render(<MessageBubble body="derek" />)
    const o = container.querySelector(ui.icon)
    const w = container.querySelector(ui.iconWrapper)

    expect(o).not.toBeInTheDocument()
    expect(w).not.toBeInTheDocument()
  })

  test('Renders an icon, if specified', () => {
    const { container } = render(
      <MessageBubble body="derek" icon={Attachment} />
    )
    const o = container.querySelector(ui.icon)
    const w = container.querySelector(ui.iconWrapper)

    expect(o).toBeInTheDocument()
    expect(w).toBeInTheDocument()
    expect(o).toHaveClass('is-iconName-paperclip')
  })
})
