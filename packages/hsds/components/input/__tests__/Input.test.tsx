import { fireEvent, render, waitFor } from '@testing-library/react'
import user from '@testing-library/user-event'

import Chat from 'hsds/icons/chat-oval'

import Input from '../Input'

const ui = {
  field: '.c-InputField',
  errorIcon: '.c-Input__errorIcon',
  helpText: '.c-Input__helpText',
  hintText: '.c-Input__hintText',
  input: 'div.c-Input',
  label: '.c-Input__label',
  suffix: 'div.c-Input__item.is-suffix',
  tooltip: '.c-Tooltip',
}

jest.useFakeTimers()

describe('ClassName', () => {
  test('Has default className', () => {
    const { container } = render(<Input />)
    const input = container.querySelector(ui.input)
    const field = container.querySelector(ui.field)
    const backdrop = container.querySelector('.c-Input__backdrop')

    expect(input).toBeInTheDocument()
    expect(field).toBeInTheDocument()
    expect(backdrop).toBeInTheDocument()
  })

  test('Accepts custom className', () => {
    const className = 'milk-was-a-bad-choice'
    const { container } = render(<Input className={className} />)

    expect(container.querySelector(`.${className}`)).toBeInTheDocument()
  })
})

describe('Input', () => {
  test('Can generate an input component', () => {
    const { container } = render(<Input />)

    expect(container.querySelector('input')).toBeInTheDocument()
  })
})

describe('Autofocus', () => {
  test('Does not autoFocus by default', () => {
    const { container } = render(<Input />)
    const input = container.querySelector('input')!

    expect(input).not.toHaveFocus()
  })

  test('Autofocuses if specified', () => {
    jest.useFakeTimers()
    const { container } = render(<Input autoFocus />)

    jest.runAllTimers()
    const input = container.querySelector('input')!
    expect(input).toHaveFocus()
  })
})

describe('Events', () => {
  test('Can trigger onBlur callback', () => {
    const spy = jest.fn()
    const { container } = render(<Input onBlur={spy} />)
    const input = container.querySelector('input')!

    input.focus()
    input.blur()

    expect(spy).toHaveBeenCalled()
  })

  test('Can trigger onClick callback', () => {
    const spy = jest.fn()
    const { container } = render(<Input onClick={spy} />)
    const input = container.querySelector('input')!

    user.click(input)

    expect(spy).toHaveBeenCalled()
  })

  test('Can trigger onFocus callback', () => {
    const spy = jest.fn()
    const { container } = render(<Input onFocus={spy} />)
    const input = container.querySelector('input')!

    input.focus()

    expect(spy).toHaveBeenCalled()
  })

  test('onChange callback passes selected value', () => {
    const spy = jest.fn()
    const { container } = render(<Input onChange={spy} />)
    const input = container.querySelector('input')!
    const value = 'Champ Kind'

    user.type(input, value)

    expect(spy).toHaveBeenCalledWith(
      value,
      expect.objectContaining({ type: 'change' })
    )
  })

  test('onWheel callback does not trigger for non-multiline inputs', () => {
    const spy = jest.fn()
    const { container } = render(<Input onWheel={spy} multiline={false} />)
    const input = container.querySelector('input')

    fireEvent.wheel(input!)

    expect(spy).not.toHaveBeenCalled()
  })

  test('onWheel callback only triggers for multiline + scrollLock enabled inputs', () => {
    const spy = jest.fn()
    const { container } = render(<Input onWheel={spy} multiline scrollLock />)
    const input = container.querySelector('textarea')

    fireEvent.wheel(input!)

    expect(spy).toHaveBeenCalled()
  })

  test('onKeydown callback fires when input keyDown occurs', () => {
    const spy = jest.fn()
    const { container } = render(<Input multiline onKeyDown={spy} />)

    const input = container.querySelector('textarea')

    fireEvent.keyDown(input!)
    expect(spy).toHaveBeenCalled()
  })

  test('onResize callback is called when Input resizes', () => {
    const spy = jest.fn()

    render(<Input multiline onResize={spy} />)

    global.dispatchEvent(new Event('resize'))

    expect(spy).toHaveBeenCalled()
  })
})

describe('value', () => {
  test('Does not update the state if new value is the same as previous value', () => {
    const { container, rerender } = render(<Input value="initial value" />)

    expect(container.querySelector('input')).toHaveValue('initial value')

    rerender(<Input value="initial value" />)

    expect(container.querySelector('input')).toHaveValue('initial value')
  })

  test('Does update the state if new value is different than previous value', () => {
    const { container, rerender } = render(<Input value="initial value" />)

    expect(container.querySelector('input')).toHaveValue('initial value')

    rerender(<Input value="second value" />)

    expect(container.querySelector('input')).toHaveValue('second value')
  })
})

describe('ID', () => {
  test('Automatically generates an ID if not defined', () => {
    const { container } = render(<Input label="Input" />)
    const label = container.querySelector('label')!
    const input = container.querySelector('input')!
    const id = input.id

    expect(id).toBeTruthy()
    expect(id).toContain('Input')
    expect(label).toHaveAttribute('for', id)
  })

  test('Can set custom ID on Input', () => {
    const { container } = render(
      <Input label="Input" id="sixty-percent-of-the-time" />
    )
    const label = container.querySelector('label')!
    const input = container.querySelector('input')!
    const id = input.id

    expect(id).toBeTruthy()
    expect(id).toContain('sixty-percent-of-the-time')
    expect(label).toHaveAttribute('for', id)
  })
})

describe('Multiline', () => {
  test('Default selector is an input', () => {
    const { container } = render(<Input />)

    expect(container.querySelector('input')).toBeInTheDocument()
  })

  test('Selector becomes a textarea if multiline is defined', () => {
    const { container } = render(<Input multiline />)

    expect(container.querySelector('textarea')).toBeInTheDocument()
  })

  test('Accepts number argument', () => {
    const { container } = render(<Input multiline={5} />)

    expect(container.querySelector('textarea')).toBeInTheDocument()
  })

  test('Adds Resizer component if multiline is defined', () => {
    const { container } = render(<Input multiline />)

    expect(container.querySelector('.c-InputResizer')).toBeInTheDocument()
  })

  test('Applies resizable styles if specified', () => {
    const { container } = render(<Input multiline resizable />)
    expect(container.querySelector('textarea')).toHaveClass('is-resizable')
  })

  test('Has regular height without multiline', () => {
    const { container } = render(<Input />)

    expect(container.querySelector('input')).not.toHaveAttribute('style')
  })

  test('Sets height and max-height on textarea with multiline', () => {
    const { container } = render(<Input multiline={3} />)
    // This is very difficult (basically impossible) to test with Enzyme/JSDOM.
    // This method involves height calculation, which is absent from JSDOM's api.
    // JSDOM always returns 0 for height.

    // The only thing we can check is if the height is not null (because null is default)
    // The height should be 0, which is what JSDOM returns.
    expect(container.querySelector('textarea')).toHaveStyle(
      'height: 0px; max-height: 320px;'
    )
  })

  test('Sets maxHeight on multiline, if specified', () => {
    const { container } = render(<Input multiline={3} maxHeight={50} />)

    expect(container.querySelector('textarea')).toHaveStyle('max-height: 50px;')
  })

  test('Adds maxHeight styles, if specified', () => {
    const { container } = render(<Input multiline={3} maxHeight={50} />)

    expect(container.querySelector('textarea')).toHaveClass('has-maxHeight')
  })

  test('maxHeight Accepts string values', () => {
    const { container } = render(<Input multiline={3} maxHeight="50vh" />)
    expect(container.querySelector('textarea')).toHaveStyle('max-height: 50vh;')
  })

  test('Does not focus input on resize', () => {
    const spy = jest.fn()

    render(<Input multiline={3} maxHeight="50vh" onFocus={spy} />)

    global.dispatchEvent(new Event('resize'))

    expect(spy).not.toHaveBeenCalled()
  })
})

describe('HelpText', () => {
  test('Does not render by default', () => {
    const { container } = render(<Input />)

    expect(container.querySelector(ui.helpText)).not.toBeInTheDocument()
  })

  test('Adds helpText if specified', () => {
    const { container } = render(<Input helpText="Help text" />)

    expect(container.querySelector(ui.helpText)).toBeInTheDocument()
    expect(container.querySelector(ui.helpText)).toHaveTextContent('Help text')
  })

  test('Can render children', () => {
    const { getByText } = render(
      <Input helpText={<div className="custom">Hello</div>} />
    )

    expect(getByText('Hello')).toBeInTheDocument()
  })
})

describe('HintText', () => {
  test('Does not render by default', () => {
    const { container } = render(<Input />)

    expect(container.querySelector(ui.hintText)).not.toBeInTheDocument()
  })

  test('Adds hintText if specified', () => {
    const { container } = render(<Input hintText="Hint text" />)
    expect(container.querySelector(ui.hintText)).toBeInTheDocument()
    expect(container.querySelector(ui.hintText)).toHaveTextContent('Hint text')
  })

  test('Can render children', () => {
    const { getByText } = render(
      <Input hintText={<div className="custom">Hello</div>} />
    )

    expect(getByText('Hello')).toBeInTheDocument()
  })
})

describe('Label', () => {
  test('Adds label if specified', () => {
    const { container } = render(<Input label="Channel" />)

    expect(container.querySelector(ui.label)).toBeInTheDocument()
    expect(container.querySelector(ui.label)).toHaveTextContent('Channel')
  })

  test('Can render children', () => {
    const { getByText } = render(
      <Input label={<div className="custom">Hello</div>} />
    )

    expect(getByText('Hello')).toBeInTheDocument()
  })
})

describe('Styles', () => {
  test('Applies sizing styles if specified', () => {
    const { container } = render(<Input size="lg" />)

    expect(container.querySelector('.c-Input__inputField')).toHaveClass('is-lg')
  })

  test('Passes style prop to wrapper', () => {
    const { container } = render(<Input style={{ background: 'red' }} />)

    expect(container.querySelector('.c-InputWrapper')).toHaveStyle(
      'background: red;'
    )
  })
})

describe('States', () => {
  test('Applies disabled styles if specified', () => {
    const { container } = render(<Input disabled />)
    const o = container.querySelector(ui.input)!
    const input = container.querySelector('input')!

    expect(o).toHaveClass('is-disabled')
    expect(input).toHaveAttribute('disabled')
  })

  test('Applies readOnly styles if specified', () => {
    const { container } = render(<Input readOnly />)
    const o = container.querySelector(ui.input)!
    const input = container.querySelector('input')!

    expect(o).toHaveClass('is-readonly')
    expect(input).toHaveAttribute('readOnly')
  })

  test('Applies error styles if specified', () => {
    const { container } = render(<Input state="error" />)
    const o = container.querySelector(ui.input)!

    expect(o).toHaveClass('is-error')
  })

  test('Applies success styles if specified', () => {
    const { container } = render(<Input state="success" />)
    const o = container.querySelector(ui.input)!

    expect(o).toHaveClass('is-success')
  })

  test('Applies warning styles if specified', () => {
    const { container } = render(<Input state="warning" />)
    const o = container.querySelector(ui.input)!

    expect(o).toHaveClass('is-warning')
  })

  test('Updates states on prop change', () => {
    const { container, rerender } = render(<Input state="warning" />)

    expect(container.querySelector(ui.input)).toHaveClass('is-warning')

    rerender(<Input state="success" />)

    expect(container.querySelector(ui.input)).toHaveClass('is-success')

    rerender(<Input state={undefined} />)

    expect(container.querySelector(ui.input)).not.toHaveClass('is-success')
  })
})

describe('Stateful helper label', () => {
  test('Renders stateful helper label if error is a string', () => {
    const { container } = render(<Input state="error" helpText="Error" />)

    expect(container.querySelector('.c-Input__helpText')).toBeInTheDocument()
    expect(container.querySelector('.c-Input__helpText')).toHaveTextContent(
      'Error'
    )
  })
})

describe('removeStateStylesOnFocus', () => {
  test('Does not remove state style on focus, by default', () => {
    const { container } = render(<Input state="error" />)

    expect(container.querySelector(ui.input)).toHaveClass('is-error')

    container.querySelector('input')!.focus()

    expect(container.querySelector(ui.input)).toHaveClass('is-error')
  })

  test('Removes state style on focus, by specified', () => {
    const { container } = render(
      <Input state="error" removeStateStylesOnFocus />
    )
    expect(container.querySelector(ui.input)).toHaveClass('is-error')

    container.querySelector('input')!.focus()

    expect(container.querySelector(ui.input)).not.toHaveClass('is-error')
  })
})

describe('inputNode', () => {
  test('Sets innerRef on mount', () => {
    const spy = jest.fn()
    const { container } = render(<Input innerRef={spy} />)

    expect(spy).toHaveBeenCalledWith(container.querySelector('input'))
  })

  test('Sets inputRef on mount', () => {
    const spy = jest.fn()
    const { container } = render(<Input inputRef={spy} />)

    expect(spy).toHaveBeenCalledWith(container.querySelector('input'))
  })
})

describe('isFocused', () => {
  test('Can focus input using isFocused prop', () => {
    const spy = jest.fn()
    const { container } = render(<Input isFocused onFocus={spy} />)

    jest.runAllTimers()

    expect(spy).toHaveBeenCalled()
    expect(container.querySelector('input')).toHaveFocus()
    expect(container.querySelector('.is-focused')).toBeInTheDocument()
  })

  test('Can focus input using custom timeout', () => {
    const spy = jest.fn()
    const { container } = render(
      <Input isFocused forceAutoFocusTimeout={20} onFocus={spy} />
    )

    jest.runAllTimers()

    expect(spy).toHaveBeenCalled()
    expect(container.querySelector('input')).toHaveFocus()
    expect(container.querySelector('.is-focused')).toBeInTheDocument()
  })

  test('Can toggle isFocused', () => {
    const spy = jest.fn()
    const { container, rerender } = render(
      <Input onFocus={spy} isFocused={false} forceAutoFocusTimeout={20} />
    )
    const input = container.querySelector('.c-Input__inputField') as HTMLInputElement
    input.onfocus = spy

    rerender(<Input isFocused />)

    jest.runAllTimers()

    expect(spy).toHaveBeenCalled()
  })
})

describe('Typing events', () => {
  let refs: any, spies: any, container: HTMLElement, unmount: () => void

  beforeEach(() => {
    jest.useFakeTimers()
    refs = {
      applySubmit: () => {},
    }

    spies = {
      callStartTyping: jest.spyOn(Input.prototype, 'callStartTyping'),
      callStopTyping: jest.spyOn(Input.prototype, 'callStopTyping'),
      clearThrottler: jest.spyOn(Input.prototype, 'clearThrottler'),
      clearTypingTimeout: jest.spyOn(Input.prototype, 'clearTypingTimeout'),
      onStartTyping: jest.fn(),
      onStopTyping: jest.fn(),
      setThrottler: jest.spyOn(Input.prototype, 'setThrottler'),
      setTypingTimeout: jest.spyOn(Input.prototype, 'setTypingTimeout'),
      typingEvent: jest.spyOn(Input.prototype, 'typingEvent'),
    }

    const rt = render(
      <Input
        onStartTyping={spies.onStartTyping}
        onStopTyping={spies.onStopTyping}
        refApplyCallStopTyping={fn => (refs.applySubmit = fn)}
        typingTimeoutDelay={3000}
        withTypingEvent
      />
    )
    container = rt.container
    unmount = rt.unmount
  })

  afterEach(() => {
    jest.clearAllMocks()
    jest.clearAllTimers()
  })

  afterAll(() => {
    jest.useRealTimers()
  })

  test('On start typing should call start typing events and make a timeout', () => {
    user.type(container.querySelector('input')!, 'a')
    expect(spies.clearTypingTimeout).toHaveBeenCalledTimes(1)
    expect(spies.typingEvent).toHaveBeenCalledTimes(1)
    expect(spies.callStartTyping).toHaveBeenCalledTimes(1)
    expect(spies.setTypingTimeout).toHaveBeenCalledTimes(1)
    expect(spies.onStartTyping).toHaveBeenCalledTimes(1)

    // This no longer is valid assertion in newer Jest versions
    // expect(setTimeout.mock.calls[1][1]).toEqual(3000)
  })

  test('After a delay of 3000ms and no more typing events, should call stop typing events and clear timeout', () => {
    jest.spyOn(global, 'clearTimeout')
    jest.spyOn(global, 'clearInterval')

    user.type(container.querySelector('input')!, 'a')

    expect(spies.callStartTyping).toHaveBeenCalledTimes(1)

    user.type(container.querySelector('input')!, 'a')

    expect(spies.callStartTyping).toHaveBeenCalledTimes(1)

    jest.advanceTimersByTime(5000)

    expect(spies.callStopTyping).toHaveBeenCalledTimes(1)
    expect(spies.onStopTyping).toHaveBeenCalledTimes(1)
    expect(spies.clearTypingTimeout).toHaveBeenCalledTimes(4)
    expect(global.clearTimeout).toHaveBeenCalledTimes(2)
    expect(global.clearInterval).toHaveBeenCalledTimes(1)
  })

  test('If the delay is less than 3000ms reset the timeout than fire it if time advances past 3000ms', () => {
    jest.spyOn(global, 'clearTimeout')
    user.type(container.querySelector('input')!, 'a')

    expect(spies.callStartTyping).toHaveBeenCalledTimes(1)

    jest.advanceTimersByTime(2100)

    expect(spies.onStartTyping).toHaveBeenCalledTimes(5)

    user.type(container.querySelector('input')!, 'a')

    expect(spies.callStartTyping).toHaveBeenCalledTimes(1)
    expect(spies.setThrottler).toHaveBeenCalledTimes(1)
    expect(spies.callStopTyping).not.toHaveBeenCalled()
    expect(spies.onStopTyping).not.toHaveBeenCalled()
    expect(spies.clearTypingTimeout).toHaveBeenCalledTimes(3)
    // only going to clear timeout if there is one
    expect(global.clearTimeout).toHaveBeenCalledTimes(1)

    user.type(container.querySelector('input')!, 'a')

    expect(spies.clearTypingTimeout).toHaveBeenCalledTimes(4)
    expect(global.clearTimeout).toHaveBeenCalledTimes(2)

    jest.advanceTimersByTime(4999)

    expect(spies.onStartTyping).toHaveBeenCalledTimes(11)
    expect(spies.callStopTyping).toHaveBeenCalledTimes(1)
    expect(spies.onStopTyping).toHaveBeenCalledTimes(1)
    expect(spies.clearTypingTimeout).toHaveBeenCalledTimes(5)
    expect(global.clearTimeout).toHaveBeenCalledTimes(3)
    expect(spies.callStartTyping).toHaveBeenCalledTimes(1)
    expect(spies.setThrottler).toHaveBeenCalledTimes(1)
  })

  test('Should clear timeout on componentWillUnMount', () => {
    user.type(container.querySelector('input')!, 'a')
    unmount()
    expect(spies.clearTypingTimeout).toHaveBeenCalledTimes(3)
  })

  test('Should call callStopTyping on refApplyCallStopTyping', () => {
    user.type(container.querySelector('input')!, 'a')
    expect(spies.callStartTyping).toHaveBeenCalledTimes(1)
    expect(spies.clearTypingTimeout).toHaveBeenCalledTimes(1)
    refs.applySubmit()
    expect(spies.onStopTyping).toHaveBeenCalledTimes(1)
    expect(spies.clearTypingTimeout).toHaveBeenCalledTimes(2)
  })
})

describe('Unmount', () => {
  test('should call clearTimeout once when the component unmounts', () => {
    jest.useFakeTimers()
    jest.spyOn(global, 'clearTimeout')

    const { container, unmount } = render(<Input />)

    user.type(container.querySelector('input')!, 'a')

    unmount()

    expect(global.clearTimeout).toHaveBeenCalled()
  })
})

describe('ErrorMessage', () => {
  test('Does not render an error Icon if suffix is defined', () => {
    const { container } = render(<Input suffix="Derek" />)

    expect(container.querySelector(ui.errorIcon)).not.toBeInTheDocument()
  })

  test('Can render an error Icon and suffix', () => {
    const { container } = render(<Input suffix="Derek" state="error" />)

    expect(container.querySelector(ui.errorIcon)).toBeInTheDocument()
    expect(container.querySelector(ui.suffix)).toBeInTheDocument()
  })

  test('Renders a Tooltip, if error', async () => {
    const { container } = render(
      <Input suffix="Derek" state="error" errorMessage="Nope!" />
    )

    user.hover(container.querySelector('.TooltipTrigger')!)

    await waitFor(() => {
      expect(document.querySelector('.c-Tooltip__title')).toHaveTextContent(
        'Nope!'
      )
    })
  })

  test('Can customize error Icon', () => {
    const { container } = render(
      <Input suffix="Derek" state="error" errorIcon={Chat} tabIndex={3} />
    )

    expect(
      container.querySelector('.is-iconName-chat-oval')
    ).toBeInTheDocument()
  })
})

describe('inlinePrefix/inlineSuffix', () => {
  test('Can render an inline prefix', () => {
    const { container } = render(<Input inlinePrefix="Words" />)

    expect(
      container.querySelector('div.c-Input__item.is-prefix')
    ).toHaveTextContent('Words')
  })

  test('Can render an inline suffix', () => {
    const { container } = render(<Input inlineSuffix="Words" />)

    expect(
      container.querySelector('div.c-Input__item.is-suffix')
    ).toHaveTextContent('Words')
  })

  test('Can render both inline prefix and suffix', () => {
    const { container } = render(
      <Input inlinePrefix="A lota" inlineSuffix="Words" />
    )
    expect(
      container.querySelector('div.c-Input__item.is-prefix')
    ).toHaveTextContent('A lota')
    expect(
      container.querySelector('div.c-Input__item.is-suffix')
    ).toHaveTextContent('Words')
  })
})

describe('Prefix', () => {
  test('Does not render a Prefix by default', () => {
    const { container } = render(<Input />)

    expect(container.querySelector('.c-InputPrefix')).not.toBeInTheDocument()
  })

  test('Renders a prefix if specified', () => {
    const Compo = () => <div className="Compo">Hello</div>
    const { container } = render(<Input prefix={<Compo />} />)

    expect(container.querySelector('.c-InputPrefix')).toBeInTheDocument()
  })
})

describe('Suffix', () => {
  test('Does not render a Suffix by default', () => {
    const { container } = render(<Input />)

    expect(container.querySelector('.c-InputSuffix')).not.toBeInTheDocument()
  })

  test('Renders a suffix if specified', () => {
    const Compo = () => <div className="Compo">Hello</div>
    const { container } = render(<Input suffix={<Compo />} />)

    expect(container.querySelector('.c-InputSuffix')).toBeInTheDocument()
  })

  test('Can render a Prefix and a Suffix', () => {
    const Compo = () => <div className="Compo">Hello</div>
    const { container } = render(
      <Input prefix={<Compo />} suffix={<Compo />} />
    )

    expect(container.querySelector('.c-InputPrefix')).toBeInTheDocument()
    expect(container.querySelector('.c-InputPrefix .Compo')).toBeInTheDocument()
    expect(container.querySelector('.c-InputSuffix')).toBeInTheDocument()
    expect(container.querySelector('.c-InputSuffix .Compo')).toBeInTheDocument()
  })
})

describe('Action', () => {
  test('Can render action UI', () => {
    const { container } = render(
      <Input action={<button className="action">Go</button>} />
    )

    expect(container.querySelector('button.action')).toBeInTheDocument()
  })

  test('Can render multiple action UI', () => {
    const { container } = render(
      <Input
        action={
          <div>
            <button className="go">Go</button>
            <button className="cancel">Go</button>
          </div>
        }
      />
    )

    expect(container.querySelector('button.go')).toBeInTheDocument()
    expect(container.querySelector('button.cancel')).toBeInTheDocument()
  })
})

describe('onEnterDown', () => {
  test('Fires onEnterDown when key enter is pressed (down)', () => {
    const spy = jest.fn()
    const { container } = render(<Input onEnterDown={spy} />)
    const el = container.querySelector('input')!

    fireEvent.keyDown(el, {
      keyCode: 13,
    })

    expect(spy).toHaveBeenCalled()
  })

  test('Fires onEnterDown when key enter + shift is pressed (down)', () => {
    const spy = jest.fn()
    const { container } = render(<Input onEnterDown={spy} />)
    const el = container.querySelector('input')!

    fireEvent.keyDown(el, {
      keyCode: 13,
      shiftKey: true,
    })

    expect(spy).toHaveBeenCalled()
  })

  test('Fires onEnterDown when key enter + shift is pressed (down)', () => {
    const spy = jest.fn()
    const { container } = render(<Input onEnterDown={spy} />)
    const el = container.querySelector('input')!

    fireEvent.keyDown(el, {
      keyCode: 13,
      ctrlKey: true,
    })

    expect(spy).toHaveBeenCalled()
  })

  test('onKeyDown still fires when Enter key is pressed', () => {
    const enterSpy = jest.fn()
    const keyDownSpy = jest.fn()
    const { container } = render(
      <Input onEnterDown={enterSpy} onKeyDown={keyDownSpy} />
    )
    const el = container.querySelector('input')!

    fireEvent.keyDown(el, {
      keyCode: 13,
    })

    expect(enterSpy).toHaveBeenCalled()
    expect(keyDownSpy).toHaveBeenCalled()
  })

  test('onEnterDown does not fire when a non-Enter key is pressed (down)', () => {
    const enterSpy = jest.fn()
    const keyDownSpy = jest.fn()
    const { container } = render(
      <Input onEnterDown={enterSpy} onKeyDown={keyDownSpy} />
    )
    const el = container.querySelector('input')!

    fireEvent.keyDown(el, {
      keyCode: 40,
    })

    expect(enterSpy).not.toHaveBeenCalled()
    expect(keyDownSpy).toHaveBeenCalled()
  })
})

describe('onEnterUp', () => {
  test('Fires onEnterUp when key enter is pressed (up)', () => {
    const spy = jest.fn()
    const { container } = render(<Input onEnterUp={spy} />)
    const el = container.querySelector('input')!

    fireEvent.keyUp(el, {
      keyCode: 13,
    })

    expect(spy).toHaveBeenCalled()
  })

  test('Fires onEnterUp when key enter + shift is pressed (up)', () => {
    const spy = jest.fn()
    const { container } = render(<Input onEnterUp={spy} />)
    const el = container.querySelector('input')!

    fireEvent.keyUp(el, {
      shiftKey: true,
      keyCode: 13,
    })

    expect(spy).toHaveBeenCalled()
  })

  test('Fires onEnterUp when key enter + shift is pressed (up)', () => {
    const spy = jest.fn()
    const { container } = render(<Input onEnterUp={spy} />)
    const el = container.querySelector('input')!

    fireEvent.keyUp(el, {
      keyCode: 13,
      ctrlKey: true,
    })

    expect(spy).toHaveBeenCalled()
  })

  test('onKeyUp still fires when Enter key is pressed', () => {
    const enterSpy = jest.fn()
    const keyUpSpy = jest.fn()
    const { container } = render(
      <Input onEnterUp={enterSpy} onKeyUp={keyUpSpy} />
    )
    const el = container.querySelector('input')!

    fireEvent.keyUp(el, {
      keyCode: 13,
    })

    expect(enterSpy).toHaveBeenCalled()
    expect(keyUpSpy).toHaveBeenCalled()
  })

  test('onEnterUp does not fire when a non-Enter key is pressed (up)', () => {
    const enterSpy = jest.fn()
    const keyUpSpy = jest.fn()
    const { container } = render(
      <Input onEnterUp={enterSpy} onKeyUp={keyUpSpy} />
    )
    const el = container.querySelector('input')!

    fireEvent.keyUp(el, {
      keyCode: 40,
    })

    expect(enterSpy).not.toHaveBeenCalled()
    expect(keyUpSpy).toHaveBeenCalled()
  })
})

describe('inputType', () => {
  test('Does not remove characters for number inputType', () => {
    jest.useFakeTimers()
    const spy = jest.fn()
    const { container } = render(<Input inputType="number" onChange={spy} />)
    const input = container.querySelector('input')!

    user.type(input, 'abc123def ~!@#$%^&*()-=')

    expect(input.value).toBe('abc123def ~!@#$%^&*()-=')
    expect(spy).toHaveBeenCalledWith(
      'abc123def ~!@#$%^&*()-=',
      expect.objectContaining({ type: 'change' })
    )
  })
})

describe('charValidator', () => {
  test('should not show the charValidator if no value in the input', () => {
    const { container } = render(
      <Input charValidatorLimit="9" withCharValidator />
    )
    expect(
      container.querySelector('.c-Input__CharValidator')
    ).not.toBeInTheDocument()
  })

  test('should not show the charValidator if the input value length is less than half the limit', async () => {
    const { container } = render(
      <Input charValidatorLimit="10" withCharValidator />
    )
    const input = container.querySelector('input')!
    const charValidator = container.querySelector('.c-Input__CharValidator')

    user.type(input, '1234')

    await waitFor(() => {
      expect(charValidator).not.toBeInTheDocument()
    })
  })

  test('should show the green charValidator if the input value length is equal or more than half the limit', () => {
    const { container } = render(
      <Input charValidatorLimit="10" withCharValidator value="12345" />
    )
    const input = container.querySelector('input')!

    input.focus()
    expect(
      container.querySelector('.c-Input__CharValidator')
    ).toBeInTheDocument()
    expect(container.querySelector('.c-Badge')).toHaveClass('is-color-green')
  })

  test('should show the yellow charValidator if the input value length is equal or more than half the limit', () => {
    const { container } = render(
      <Input charValidatorLimit="10" withCharValidator value="12345678" />
    )
    const input = container.querySelector('input')!

    input.focus()
    expect(
      container.querySelector('.c-Input__CharValidator')
    ).toBeInTheDocument()
    expect(container.querySelector('.c-Badge')).toHaveClass('is-color-yellow')
  })

  test('should show the red charValidator if the input value length has reached the limit', () => {
    const { container } = render(
      <Input charValidatorLimit="10" withCharValidator value="1234567890" />
    )
    const input = container.querySelector('input')!

    input.focus()
    expect(
      container.querySelector('.c-Input__CharValidator')
    ).toBeInTheDocument()
    expect(container.querySelector('.c-Badge')).toHaveClass('is-color-red')
  })

  test('should clear the charValidator if the input value is cleared', () => {
    const { container } = render(
      <Input charValidatorLimit="10" withCharValidator value="1234567890" />
    )
    const input = container.querySelector('input')!

    user.type(input, '{selectall}{backspace}')

    expect(
      container.querySelector('.c-Input__CharValidator')
    ).not.toBeInTheDocument()
  })

  // This test was incorrect before and doesn't seem trivially fixable
  test.skip('should clear the charValidator if the input value is completely replaced with a value under the green limit', async () => {
    const { container } = render(
      <Input charValidatorLimit="10" withCharValidator value="1234567890" />
    )
    const input = container.querySelector('input')!

    user.type(input, '{selectall}a')

    await waitFor(() => {
      expect(
        container.querySelector('.c-Input__CharValidator')
      ).not.toBeInTheDocument()
    })
  })

  test('should update the charValidator as you type', () => {
    const { container } = render(
      <Input charValidatorLimit="10" withCharValidator />
    )
    const input = container.querySelector('input')!

    // no show
    user.type(input, '1234')

    expect(
      container.querySelector('.c-Input__CharValidator')
    ).not.toBeInTheDocument()

    // show green
    user.type(input, '5')

    expect(
      container.querySelector('.c-Input__CharValidator')
    ).toBeInTheDocument()
    expect(container.querySelector('.c-Badge')).toHaveClass('is-color-green')

    // show yellow
    user.type(input, '678')

    expect(
      container.querySelector('.c-Input__CharValidator')
    ).toBeInTheDocument()
    expect(container.querySelector('.c-Badge')).toHaveClass('is-color-yellow')

    // show red
    user.type(input, '67890')

    expect(
      container.querySelector('.c-Input__CharValidator')
    ).toBeInTheDocument()
    expect(container.querySelector('.c-Badge')).toHaveClass('is-color-red')

    // back to yellow

    user.type(input, '{backspace}')

    expect(
      container.querySelector('.c-Input__CharValidator')
    ).toBeInTheDocument()
    expect(container.querySelector('.c-Badge')).toHaveClass('is-color-yellow')

    // back to green

    user.type(input, '{backspace}{backspace}')

    expect(
      container.querySelector('.c-Input__CharValidator')
    ).toBeInTheDocument()
    expect(container.querySelector('.c-Badge')).toHaveClass('is-color-green')

    // back to hide

    user.type(input, '{backspace}{backspace}{backspace}')

    expect(
      container.querySelector('.c-Input__CharValidator')
    ).not.toBeInTheDocument()
  })
})
