import { render } from '@testing-library/react'

import InputResizer from '../Input.Resizer'

describe('onResize', () => {
  test('Is called when mounted', () => {
    const spy = jest.fn()
    render(<InputResizer onResize={spy} />)

    expect(spy).toHaveBeenCalled()
  })
})

describe('ReplaceEntity', () => {
  test('Converts greater/less than characters', () => {
    const { container } = render(
      <InputResizer contents="<strong>News team!</strong>" />
    )
    const html = container.querySelector('.c-InputGhost')!.innerHTML

    expect(html).not.toContain('<strong>')
    expect(html).toContain('&lt;')
    expect(html).toContain('&gt;')
    expect(html).toContain('&lt;strong&gt;')
  })

  test('Converts \\n characters to <br>', () => {
    const { container } = render(<InputResizer contents={`\nSan Diego\n`} />)
    const html = container.querySelector('.c-InputGhost')!.innerHTML

    expect(html).not.toContain('\n')
    expect(html).toContain('<br>San Diego<br>')
  })

  test('Converts & characters to &amp;', () => {
    const { container } = render(<InputResizer contents="San & Diego" />)
    const html = container.querySelector('.c-InputGhost')!.innerHTML

    expect(html).toContain('San &amp; Diego')
  })

  test('Does not convert if content does not contain special characters', () => {
    const { container } = render(<InputResizer contents="San Diego" />)
    const html = container.querySelector('.c-InputGhost')!.innerHTML

    expect(html).toContain('San Diego')
  })
})

describe('InputGhost', () => {
  test('Renders an InputGhost', () => {
    const { container } = render(<InputResizer />)

    expect(container.querySelector('.c-InputGhost')).toBeInTheDocument()
    expect(container.querySelector('.c-InputGhost br')).toBeInTheDocument()
  })

  test('Adds a <br> for every line', () => {
    const { container } = render(<InputResizer minimumLines={5} />)

    expect(
      container.querySelectorAll('.c-InputGhost:last-child br').length
    ).toBe(5)
  })

  test('Does not render content InputGhost if minimumLines is falsey', () => {
    const { container } = render(<InputResizer minimumLines={0} />)
    expect(container.querySelectorAll('.c-InputGhost').length).toBe(1)
  })
})

describe('Styles', () => {
  test('Applies seamless styles to child components, if specified', () => {
    const { container } = render(<InputResizer seamless />)

    expect(container.querySelectorAll('.c-InputGhost')[0]).toHaveClass(
      'is-seamless'
    )
    expect(container.querySelectorAll('.c-InputGhost')[1]).toHaveClass(
      'is-seamless'
    )
  })

  test('Does not apply seamless styles to child components, if not specified', () => {
    const { container } = render(<InputResizer seamless={false} />)

    expect(container.querySelectorAll('.c-InputGhost')[0]).not.toHaveClass(
      'is-seamless'
    )
    expect(container.querySelectorAll('.c-InputGhost')[1]).not.toHaveClass(
      'is-seamless'
    )
  })
})

describe('offsetAmount', () => {
  test('Adds offsetChars, if specified', () => {
    const { container } = render(<InputResizer offsetAmount={5} contents="1" />)

    expect(container.querySelectorAll('.c-InputGhost')[0]).toHaveTextContent(
      '1RRRRR'
    )
  })

  test('Does not add offsetChars, if specified', () => {
    const { container } = render(<InputResizer offsetAmount={0} contents="1" />)

    expect(container.querySelectorAll('.c-InputGhost')[0]).toHaveTextContent(
      '1'
    )
  })
})
