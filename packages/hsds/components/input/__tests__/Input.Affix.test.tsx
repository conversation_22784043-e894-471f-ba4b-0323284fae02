import { render } from '@testing-library/react'

import InputAffix from '../Input.Affix'

describe('Input/Affix', () => {
  describe('ClassName', () => {
    test('Has default className', () => {
      const { container } = render(<InputAffix />)

      expect(container.querySelector('.c-InputAffix')).toBeInTheDocument()
    })

    test('Applies custom className if specified', () => {
      const customClass = 'piano-key-neck-tie'
      const { container } = render(<InputAffix className={customClass} />)

      expect(container.querySelector('.c-InputAffix')).toHaveClass(customClass)
    })

    test('Applies prefix classNames', () => {
      const { container } = render(<InputAffix variant="prefix" />)

      expect(container.querySelector('.c-InputAffix')).toHaveClass(
        'c-InputPrefix'
      )
    })
    test('Applies suffix classNames', () => {
      const { container } = render(<InputAffix variant="suffix" />)

      expect(container.querySelector('.c-InputAffix')).toHaveClass(
        'c-InputSuffix'
      )
    })
  })

  describe('Style', () => {
    test('Can render action styles', () => {
      const { container } = render(<InputAffix isAction />)

      expect(container.querySelector('.c-InputAffix')).toHaveClass('is-action')
    })
  })
})
