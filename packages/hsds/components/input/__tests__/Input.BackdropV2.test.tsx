import { render } from '@testing-library/react'

import InputBackdropV2 from '../Input.BackdropV2'

describe('ClassName', () => {
  test('Has default className', () => {
    const { container } = render(<InputBackdropV2 />)

    expect(container.querySelector('.c-InputBackdropV2')).toBeInTheDocument()
  })

  test('Applies custom className if specified', () => {
    const customClass = 'piano-key-neck-tie'
    const { container } = render(<InputBackdropV2 className={customClass} />)

    expect(container.querySelector('.c-InputBackdropV2')).toHaveClass(
      customClass
    )
  })
})

describe('Styles', () => {
  test('Can render isFirst styles', () => {
    const { container } = render(<InputBackdropV2 isFirst />)

    expect(container.querySelector('.c-InputBackdropV2')).toHaveClass(
      'is-first'
    )
    expect(container.querySelector('.c-InputBackdropV2')).not.toHaveClass(
      'is-notOnly'
    )
    expect(container.querySelector('.c-InputBackdropV2')).not.toHaveClass(
      'is-last'
    )
  })

  test('Can render isNotOnly styles', () => {
    const { container } = render(<InputBackdropV2 isNotOnly />)

    expect(container.querySelector('.c-InputBackdropV2')).not.toHaveClass(
      'is-first'
    )
    expect(container.querySelector('.c-InputBackdropV2')).toHaveClass(
      'is-notOnly'
    )
    expect(container.querySelector('.c-InputBackdropV2')).not.toHaveClass(
      'is-last'
    )
  })

  test('Can render isLast styles', () => {
    const { container } = render(<InputBackdropV2 isLast />)

    expect(container.querySelector('.c-InputBackdropV2')).not.toHaveClass(
      'is-first'
    )
    expect(container.querySelector('.c-InputBackdropV2')).not.toHaveClass(
      'is-notOnly'
    )
    expect(container.querySelector('.c-InputBackdropV2')).toHaveClass('is-last')
  })

  test('Can render choiceKind styles', () => {
    const { container } = render(<InputBackdropV2 choiceKind="checkbox" />)

    expect(container.querySelector('.c-InputBackdropV2')).toHaveClass(
      'is-checkbox'
    )
  })

  test('Can render read-only styles', () => {
    const { container } = render(<InputBackdropV2 readOnly />)

    expect(container.querySelector('.c-InputBackdropV2')).toHaveClass(
      'is-readonly'
    )
  })

  test('Can render is-radio styles', () => {
    const { container } = render(<InputBackdropV2 choiceKind="radio" />)

    expect(container.querySelector('.c-InputBackdropV2')).toHaveClass(
      'is-radio'
    )
  })

  test('Can render disabled styles', () => {
    const { container } = render(<InputBackdropV2 disabled />)

    expect(container.querySelector('.c-InputBackdropV2')).toHaveClass(
      'is-disabled'
    )
  })

  test('Can render filled styles', () => {
    const { container } = render(<InputBackdropV2 isFilled />)

    expect(container.querySelector('.c-InputBackdropV2')).toHaveClass(
      'is-filled'
    )
  })

  test('Can render kind styles', () => {
    const { container } = render(<InputBackdropV2 kind="custom" />)

    expect(container.querySelector('.c-InputBackdropV2')).toHaveClass(
      'is-kind-custom'
    )
  })
})

describe('Focus', () => {
  test('Can render focus styles', () => {
    const { container } = render(<InputBackdropV2 isFocused />)
    expect(container.querySelector('.c-InputBackdropV2')).toHaveClass(
      'is-focused'
    )
  })

  test('Does not render focus styles, if specified', () => {
    const { container } = render(
      <InputBackdropV2 isFocused showFocus={false} />
    )

    expect(container.querySelector('.c-InputBackdropV2')).not.toHaveClass(
      'is-focused'
    )
    expect(
      container.querySelector('.c-InputBackdropV2__focus')
    ).not.toBeInTheDocument()
  })

  test('Can render isFirst styles', () => {
    const { container } = render(<InputBackdropV2 isFocused isFirst />)

    expect(container.querySelector('.c-InputBackdropV2__focus')).toHaveClass(
      'is-first'
    )
    expect(
      container.querySelector('.c-InputBackdropV2__focus')
    ).not.toHaveClass('is-notOnly')
    expect(
      container.querySelector('.c-InputBackdropV2__focus')
    ).not.toHaveClass('is-last')
  })

  test('Can render isNotOnly styles', () => {
    const { container } = render(<InputBackdropV2 isFocused isNotOnly />)

    expect(
      container.querySelector('.c-InputBackdropV2__focus')
    ).not.toHaveClass('is-first')
    expect(container.querySelector('.c-InputBackdropV2__focus')).toHaveClass(
      'is-notOnly'
    )
    expect(
      container.querySelector('.c-InputBackdropV2__focus')
    ).not.toHaveClass('is-last')
  })

  test('Can render isLast styles', () => {
    const { container } = render(<InputBackdropV2 isFocused isLast />)

    expect(
      container.querySelector('.c-InputBackdropV2__focus')
    ).not.toHaveClass('is-first')
    expect(
      container.querySelector('.c-InputBackdropV2__focus')
    ).not.toHaveClass('is-notOnly')
    expect(container.querySelector('.c-InputBackdropV2__focus')).toHaveClass(
      'is-last'
    )
  })

  test('Can render error state styles', () => {
    const { container } = render(<InputBackdropV2 isFocused state="error" />)
    expect(container.querySelector('.c-InputBackdropV2__focus')).toHaveClass(
      'is-state-error'
    )
  })
})
