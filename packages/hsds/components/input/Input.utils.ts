/**
 * Get the total number of lines (rows) of a textarea
 */
export const getTextAreaLineTotal = (
  textarea: HTMLTextAreaElement | null
): number => {
  if (!textarea) return 0
  return textarea.value.split(/\r*\n/).length
}

/**
 * Get the current line (row) of the textarea based on cursor position.
 */
export const getTextAreaLineCurrent = (
  textarea: HTMLTextAreaElement | null
): number => {
  if (!textarea) return 0

  return textarea.value.substr(0, textarea.selectionStart).split('\n').length
}

/**
 * Moves the cursor caret to the end of the Input value
 * Source: https://css-tricks.com/snippets/javascript/move-cursor-to-end-of-input/
 */
export const moveCursorToEnd = (
  input: HTMLInputElement | HTMLTextAreaElement | null
): void => {
  // Extra failsafe guard
  if (!input) return
  if (typeof input.selectionStart === 'number') {
    input.selectionStart = input.selectionEnd = input.value.length
  } else if (
    'createTextRange' in input &&
    typeof input.createTextRange !== 'undefined'
  ) {
    input.focus()

    const range = (input.createTextRange as any)?.()
    range.collapse(false)
    range.select()
  }
}

/**
 * Check to see if Node is a textarea.
 *
 * @param   {HTMLElement} node
 * @returns {boolean}
 */
export function isTextArea(
  node: HTMLElement | null
): node is HTMLTextAreaElement {
  return !!(node && node.tagName === 'TEXTAREA')
}
