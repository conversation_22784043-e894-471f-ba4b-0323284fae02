import React, { PropsWithChildren } from 'react'

import classNames from 'classnames'

import { getValidProps } from 'hsds/utils/react'
import { capitalizeFirstLetter } from 'hsds/utils/strings'

import { AffixUI } from './Input.styles'

interface InputAffixProps {
  /** Custom class names to be added to the component. */
  className?: string
  /** Data attr for Cypress tests. */
  'data-cy'?: string
  /** Gives some spacing in case is action with a margin */
  isAction?: boolean
  /** Whether the affix is a suffix or a prefix */
  variant?: 'prefix' | 'suffix'
}

function InputAffix({
  'data-cy': dataCy,
  className,
  variant = 'prefix',
  isAction = false,
  ...rest
}: PropsWithChildren<InputAffixProps>) {
  return (
    <AffixUI
      {...getValidProps(rest)}
      data-cy={dataCy || `Input${capitalizeFirstLetter(variant)}`}
      className={classNames(
        'c-InputAffix',
        `c-Input${capitalizeFirstLetter(variant)}`,
        'c-Input__item',
        `c-Input__${variant}`,
        isAction && 'is-action',
        className
      )}
    />
  )
}

export default InputAffix
