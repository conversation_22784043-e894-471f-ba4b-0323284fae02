import styled from 'styled-components'

import { getToken } from 'hsds/tokens'

export const config = {
  backgroundColor: getToken('input.color.background.default'),
  backgroundColorDisabled: getToken('input.color.background.disabled'),
  borderColor: getToken('color.border.ui'),
  borderRadius: '4px',
  boxShadow: `0 0 0 0 ${getToken('color.border.default')}`,
  transition:
    'box-shadow 100ms ease, background-color 100ms ease, border-color 100ms ease',
}

export const InputWrapperUI = styled('div')`
  --hsds-input-padding-sides: 16px;
  --hsds-input-height: 36px;
  --hsds-input-font-size: var(--HSDSGlobalFontSize);
  --hsds-input-color: ${getToken('input.color.text')};
  --hsds-input-color-placeholder: ${getToken(
    'input.color.placeholder.default'
  )};

  width: 100%;

  .c-Input__helpText {
    color: ${getToken('color.text.light')};
    padding-bottom: 12px;
    margin-top: -2px;
  }
  .c-Input__hintText {
    color: ${getToken('color.text.light')};
    padding-top: 4px;
  }
`

export const InputUI = styled('div')`
  position: relative;
  display: flex;
  align-items: center;
  padding: 1px var(--hsds-input-padding-sides);
  border: none;
  background-color: transparent;

  &.is-multiline {
    height: auto;
  }

  &.is-focused {
    z-index: 2;
  }

  &.is-disabled {
    --hsds-input-color: ${getToken('color.text.disabled')};
  }
`

export const FieldUI = styled('input')`
  &.c-InputField {
    font-size: var(--hsds-input-font-size);
    font-family: var(--HSDSGlobalFontFamily);
    appearance: none;
    background-color: transparent;
    border: none;
    box-shadow: none;
    color: var(--hsds-input-color);
    display: block;
    height: var(--hsds-input-height);
    margin-bottom: 1px;
    margin-top: 1px;
    padding: 0;
    position: relative;
    top: 0;
    width: 100%;
    z-index: 1;

    &:focus {
      outline: none;
    }

    &::placeholder {
      color: var(--hsds-input-color-placeholder);
    }

    &.is-error::placeholder {
      --hsds-input-color-placeholder: ${getToken(
        'input.color.placeholder.error'
      )};
    }

    &.is-success::placeholder {
      --hsds-input-color-placeholder: ${getToken(
        'input.color.placeholder.success'
      )};
    }

    &.is-warning::placeholder {
      --hsds-input-color-placeholder: ${getToken(
        'input.color.placeholder.warning'
      )};
    }

    &:-webkit-autofill,
    &:-webkit-autofill:hover,
    &:-webkit-autofill:focus &:-webkit-autofill {
      -webkit-box-shadow: 0 0 0px 1000px white inset;
      background-clip: content-box;
      transition: background-color 5000s ease-in-out 0s;
    }

    &.is-lg {
      --hsds-input-height: 46px;
      --hsds-input-font-size: 14px;
    }

    &.is-error {
      width: calc(100% - 24px);
      --hsds-input-color: ${getToken('color.state.error.text')};
    }

    &.is-success {
      --hsds-input-color: ${getToken('color.state.success.text')};
    }

    &.is-warning {
      --hsds-input-color: ${getToken('color.state.warning.text')};
    }

    &.is-resizable {
      resize: vertical !important;
    }

    &.is-multiline {
      line-height: normal;
      overflow: hidden;
      height: auto;
      margin-bottom: 1px;
      margin-top: 1px;
      margin-left: calc(var(--hsds-input-padding-sides) * -1);
      margin-right: calc(var(--hsds-input-padding-sides) * -1);
      padding: 12px var(--hsds-input-padding-sides);
      resize: none;
      top: 1px;
      width: calc(100% + (var(--hsds-input-padding-sides) * 2));
      min-height: 40px;

      &.is-lg {
        min-height: 50px;
      }

      &.has-maxHeight {
        margin-bottom: 2px;
        margin-left: calc((var(--hsds-input-padding-sides) - 2px) * -1);
        margin-right: calc((var(--hsds-input-padding-sides) - 2px) * -1);
        margin-top: 0;
        overflow-y: auto;

        &.is-error {
          margin-right: calc((var(--hsds-input-padding-sides) * 2 + 2px) * -1);
        }
      }
    }
  }
`

export const AffixUI = styled('div')`
  position: relative;
  margin-top: -1px;
  margin-bottom: -1px;

  z-index: 1;

  &.c-Input__prefix {
    margin-right: var(--hsds-input-padding-sides);
    margin-left: calc(var(--hsds-input-padding-sides) * -1);
  }

  &.c-Input__suffix {
    margin-left: var(--hsds-input-padding-sides);
    margin-right: calc(var(--hsds-input-padding-sides) * -1);
  }

  &.is-action {
    margin-right: -11px;
  }
`

export const CharValidatorUI = styled('div')`
  right: 15px;
  bottom: -7px;
  min-height: 18px;
  position: absolute;
  text-align: right;
  z-index: 3;
  transform: translateY(0);

  .c-Input__CharValidator__Text {
    min-width: 18px;
    width: fit-content;
    padding: 0 3px;
  }

  .c-Badge {
    font-weight: 400;
    min-width: 20px;
    padding-left: 4px;
    padding-right: 4px;
  }
`

export const InlinePrefixSuffixUI = styled('div')`
  --hsds-input-inlinePrefixSuffix-color: ${getToken(
    'input.color.placeholder.default'
  )};

  color: var(--hsds-input-inlinePrefixSuffix-color);
  padding-left: 4px;
  padding-right: 4px;
  position: relative;
  top: 0;
  white-space: nowrap;
  z-index: 1;

  &.is-suffix {
    right: -4px;
  }

  &.is-error {
    --hsds-input-inlinePrefixSuffix-color: ${getToken(
      'input.color.placeholder.error'
    )};
  }

  &.is-success {
    --hsds-input-inlinePrefixSuffix-color: ${getToken(
      'input.color.placeholder.success'
    )};
  }

  &.is-warning {
    --hsds-input-inlinePrefixSuffix-color: ${getToken(
      'input.color.placeholder.warning'
    )};
  }

  &.is-prefix {
  }

  &.is-suffix {
  }
`

export const ControlUI = styled('div')`
  --hsds-input-backdrop-border-radius: ${config.borderRadius};

  display: flex;
  align-items: center;
  justify-content: center;
  width: fit-content;
  height: calc(var(--hsds-input-height) + 2px);
  padding: 0 var(--hsds-input-padding-sides);
  background-color: ${getToken('input.color.control.default.bg')};
  color: ${getToken('input.color.control.default.color')};
  text-align: center;
  border-right: 1px solid
    var(
      --hsds-input-backdrop-border,
      ${getToken('input.color.control.default.border')}
    );
  border-left: 1px solid
    var(
      --hsds-input-backdrop-border,
      ${getToken('input.color.control.default.border')}
    );

  .c-InputPrefix & {
    border-top-left-radius: var(--hsds-input-backdrop-border-radius);
    border-bottom-left-radius: var(--hsds-input-backdrop-border-radius);
  }

  .c-InputSuffix & {
    border-top-right-radius: var(--hsds-input-backdrop-border-radius);
    border-bottom-right-radius: var(--hsds-input-backdrop-border-radius);
  }

  .is-success & {
    background-color: ${getToken('input.color.control.success.bg')};
    color: ${getToken('input.color.control.success.color')};
    border-color: ${getToken('input.color.control.success.border')};
  }

  .is-warning & {
    background-color: ${getToken('input.color.control.warning.bg')};
    color: ${getToken('input.color.control.warning.color')};
    border-color: ${getToken('input.color.control.warning.border')};
  }

  .is-error & {
    background-color: ${getToken('input.color.control.error.bg')};
    color: ${getToken('input.color.control.error.color')};
    border-color: ${getToken('input.color.control.error.border')};
  }
`
