import styled from 'styled-components'

import { getToken } from 'hsds/tokens'
import { focusShadow } from 'hsds/utils/mixins'

import { config } from './Input.styles'

export const BackdropUI = styled('div')`
  --hsds-input-backdrop-bg-color: ${config.backgroundColor};
  --hsds-input-backdrop-border: ${config.borderColor};
  --hsds-input-backdrop-border-radius: ${config.borderRadius};
  --hsds-input-backdrop-focus-box-shadow: none;
  position: absolute;
  inset: 0;
  border-radius: var(--hsds-input-backdrop-border-radius);
  border: 1px solid var(--hsds-input-backdrop-border);
  background-color: var(--hsds-input-backdrop-bg-color);
  transition: ${config.transition};

  &:not(.is-filled).is-focused,
  &:not(.is-focused):not(.is-filled).is-state-default {
    --hsds-input-backdrop-border: ${config.borderColor};
  }

  &:not(.is-focused):not(.is-filled).is-state-error {
    --hsds-input-backdrop-border: ${getToken('color.state.error.borderColor')};
  }

  &:not(.is-focused):not(.is-filled).is-state-warning {
    --hsds-input-backdrop-border: ${getToken(
      'color.state.warning.borderColor'
    )};
  }

  &:not(.is-focused):not(.is-filled).is-state-success {
    --hsds-input-backdrop-border: ${getToken(
      'color.state.success.borderColor'
    )};
  }

  &.is-disabled {
    --hsds-input-backdrop-bg-color: ${config.backgroundColorDisabled};
  }

  &.is-notOnly {
    --hsds-input-backdrop-border-radius: 0;
  }

  &.is-first {
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;
  }

  &.is-last {
    border-top-left-radius: 0;
    border-bottom-left-radius: 0;
  }
`

export const FocusUI = styled('div')`
  position: absolute;
  inset: 0px;
  border-radius: 2px;
  box-shadow: var(--hsds-input-backdrop-focus-box-shadow);
  animation: BackdropFocusFadeIn 200ms;
  z-index: 1;
  pointer-events: none;

  &.is-state-default,
  &.is-state-error,
  &.is-state-warning,
  &.is-state-success {
    --hsds-input-backdrop-focus-box-shadow: ${focusShadow};
  }

  &.is-state-error {
    --hsds-focus-ring-color: ${getToken('color.focusRing.error.innerColor')};
    --hsds-focus-ring-outer-color: ${getToken(
      'color.focusRing.error.outerColor'
    )};
  }

  &.is-state-warning {
    --hsds-focus-ring-color: ${getToken('color.focusRing.warning.innerColor')};
    --hsds-focus-ring-outer-color: ${getToken(
      'color.focusRing.warning.outerColor'
    )};
  }

  &.is-state-success {
    --hsds-focus-ring-color: ${getToken('color.focusRing.success.innerColor')};
    --hsds-focus-ring-outer-color: ${getToken(
      'color.focusRing.success.outerColor'
    )};
  }

  &.is-first {
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;
  }

  &.is-notOnly {
    border-radius: 0;
  }

  &.is-last {
    border-top-left-radius: 0;
    border-bottom-left-radius: 0;
  }

  @keyframes BackdropFocusFadeIn {
    from {
      opacity: 0;
    }
    to {
      opacity: 1;
    }
  }
`

export default BackdropUI
