import React from 'react'

import classNames from 'classnames'

import { BackdropUI, FocusUI } from './Input.BackdropV2.styles'

interface InputBackdropV2Props {
  'data-cy'?: string
  choiceKind?: string
  className?: string
  disabled?: boolean
  isFilled?: boolean
  isFirst?: boolean
  isFocused?: boolean
  isLast?: boolean
  isNotOnly?: boolean
  kind?: string
  readOnly?: boolean
  showFocus?: boolean
  state?: string
  validatorCount?: number
}

export function InputBackdropV2({
  choiceKind,
  className,
  'data-cy': dataCy = 'InputBackdropV2',
  disabled = false,
  isFilled = false,
  isFirst,
  isFocused = false,
  isLast,
  isNotOnly,
  kind,
  readOnly = false,
  showFocus = true,
  state = 'default',
  validatorCount,
}: InputBackdropV2Props) {
  const shouldShowFocus = isFocused && showFocus

  return (
    <BackdropUI
      data-cy={dataCy}
      className={classNames(
        'c-InputBackdropV2',
        choiceKind ? `is-${choiceKind}` : 'is-input',
        disabled && 'is-disabled',
        isFilled && 'is-filled',
        shouldShowFocus && 'is-focused',
        isFirst && 'is-first',
        isNotOnly && 'is-notOnly',
        isLast && 'is-last',
        kind && `is-kind-${kind}`,
        readOnly && 'is-readonly',
        state && `is-state-${state}`,
        (validatorCount as number) <= 0 && 'is-state-error',
        className
      )}
      role="presentation"
    >
      {shouldShowFocus && (
        <FocusUI
          className={classNames(
            'c-InputBackdropV2__focus',
            isFirst && 'is-first',
            isNotOnly && 'is-notOnly',
            isLast && 'is-last',
            choiceKind === 'radio' && 'is-radio',
            state && `is-state-${state}`,
            (validatorCount as number) <= 0 && 'is-state-error'
          )}
        />
      )}
    </BackdropUI>
  )
}

export default InputBackdropV2
