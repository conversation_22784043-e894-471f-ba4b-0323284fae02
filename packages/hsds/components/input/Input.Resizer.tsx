import React from 'react'

import classNames from 'classnames'

import EventListener from 'hsds/components/event-listener'
import { getValidProps } from 'hsds/utils/react'

import { GhostUI, ResizerUI } from './Input.Resizer.styles'

// Thanks Stephen <3
export const OFFSET_CHAR = 'R'

const ENTITIES_TO_REPLACE: Record<string, string> = {
  '&': '&amp;',
  '<': '&lt;',
  '>': '&gt;',
  '\n': '<br>',
}
const REPLACE_REGEX = /[\n&<>]/g

interface InputResizerProps {
  /** Custom class names to be added to the component. */
  className?: string
  contents?: string
  currentHeight?: number | null
  minimumLines?: number
  /** Number of characters to offset (bottom-right) for multiline resizing. */
  offsetAmount?: number
  /** Callback when input is resized. */
  onResize?: (height: number) => void
  /** Removes the border around the input. */
  seamless?: boolean
  /** Data attr for Cypress tests. */
  'data-cy'?: string
}

export class InputResizer extends React.PureComponent<InputResizerProps> {
  static className = 'c-InputResizer'

  static defaultProps: InputResizerProps = {
    contents: '',
    currentHeight: null,
    'data-cy': 'InputResizer',
    minimumLines: 1,
    offsetAmount: 0,
    onResize: noop,
    seamless: false,
  }

  contentNode: HTMLDivElement | null = null
  minimumLinesNode: HTMLDivElement | null = null
  _isMounted = false

  componentDidMount() {
    this._isMounted = true
    this.handleOnResize()

    // Re-trigger to help recalculate when used within heavier components/views.

    requestAnimationFrame(() => {
      if (this._isMounted) {
        this.handleOnResize()
      }
    })
  }

  componentWillUnmount() {
    this._isMounted = false
  }

  componentDidUpdate() {
    this.handleOnResize()
  }

  getClassName() {
    const { className } = this.props

    return classNames(InputResizer.className, className)
  }

  getContentClassName() {
    const { seamless } = this.props

    return classNames(
      'c-InputGhost',
      'c-InputGhost--characters',
      seamless && 'is-seamless'
    )
  }

  // Ignoring as height calculation isn't possible with JSDOM

  handleOnResize = () => {
    if (!this.contentNode || !this.minimumLinesNode) return
    const contentHeight = this.contentNode.offsetHeight
    const minimumHeight = this.minimumLinesNode
      ? this.minimumLinesNode.offsetHeight
      : 0
    const newHeight = Math.max(contentHeight, minimumHeight)

    const { currentHeight, onResize } = this.props

    if (newHeight !== currentHeight) {
      onResize?.(newHeight)
    }
  }

  replaceEntity(entity: string) {
    return ENTITIES_TO_REPLACE[entity] || entity
  }

  getContentsForMinimumLines(minimumLines: number) {
    let content = ''
    for (let line = 0; line < minimumLines; line++) {
      content += '<br>'
    }

    return content
  }

  getFinalContents(contents?: string) {
    const charOffset = OFFSET_CHAR.repeat(this.props.offsetAmount as number)
    return contents
      ? `${contents
          .replace(REPLACE_REGEX, this.replaceEntity)
          .concat(charOffset)}<br>`
      : '<br>'
  }

  setMinimumLinesNode = (node: HTMLDivElement | null) =>
    (this.minimumLinesNode = node)
  setContentNodeRef = (node: HTMLDivElement | null) => (this.contentNode = node)

  renderMinimumLines() {
    const { minimumLines } = this.props
    if (!minimumLines) return

    return (
      <GhostUI
        ref={this.setMinimumLinesNode}
        className={this.getContentClassName()}
        dangerouslySetInnerHTML={{
          __html: this.getContentsForMinimumLines(minimumLines),
        }}
      />
    )
  }

  render() {
    const { contents, ...rest } = this.props

    return (
      <ResizerUI
        {...getValidProps(rest)}
        aria-hidden
        className={this.getClassName()}
      >
        <EventListener event="resize" handler={this.handleOnResize} />
        <GhostUI
          ref={this.setContentNodeRef}
          className={this.getContentClassName()}
          dangerouslySetInnerHTML={{ __html: this.getFinalContents(contents) }}
        />
        {this.renderMinimumLines()}
      </ResizerUI>
    )
  }
}

function noop() {}

export default InputResizer
