/* eslint react/no-deprecated: off */
import React, { ChangeEvent, FocusEvent, WheelEvent } from 'react'

import classNames from 'classnames'

import Badge from 'hsds/components/badge'
import { FormLabelContext } from 'hsds/components/form-label'
import Icon, { IconType } from 'hsds/components/icon'
import Label from 'hsds/components/label'
import { scrollLockY } from 'hsds/components/scroll-lock/ScrollLock.utils'
import Tooltip from 'hsds/components/tooltip'
import { createUniqueIDFactory } from 'hsds/utils/id'
import { Keys } from 'hsds/utils/keyboard'
import { isModifierKeyPressed } from 'hsds/utils/keyboard'
import { getValidProps } from 'hsds/utils/react'

import { isTextArea, moveCursorToEnd } from './Input.utils'

import {
  CharValidatorUI,
  FieldUI,
  InlinePrefixSuffixUI,
  InputUI,
  InputWrapperUI,
} from './Input.styles'

import InputAffix from './Input.Affix'
import InputBackdropV2 from './Input.BackdropV2'
import InputResizer from './Input.Resizer'

const uniqueID = createUniqueIDFactory('Input')

function noop() {}

export type InputProps = {
  /** Embedded actions for the Input. */
  action?: React.ReactNode
  /** Automatically focuses the input. */
  autoFocus?: boolean
  autoFocusTimeoutId?: ReturnType<typeof setTimeout>
  /** How many chars are allowed to be input. */
  charValidatorLimit?: number | string
  /** Custom class names to be added to the component. */
  className?: string
  /** Disable the input. */
  disabled?: boolean
  /** Icon that renders when the state is `error`. */
  errorIcon?: IconType | React.ReactElement
  /** Error message that renders into a Tooltip. */
  errorMessage?: React.ReactNode | string
  /** Determines the amount of time (`ms`) for the component to focus on mount. */
  forceAutoFocusTimeout?: number
  /** If `true` and `enter + special` key is pressed, a return will be inserted */
  hasInsertCarriageReturns?: boolean
  /** Displays text underneath input. */
  helpText?: React.ReactNode
  /** Displays text above input. */
  hintText?: React.ReactNode
  /** ID for the input. */
  id?: string
  /** Text or component (usually an Icon) to render before the input. */
  inlinePrefix?: string | React.ReactNode
  /** Text or component (usually an Icon) to render after the input. */
  inlineSuffix?: string | React.ReactNode
  /** Retrieves the `input` DOM node. */
  inputRef?: (node: HTMLInputElement | HTMLTextAreaElement | null) => void
  /** Helps render component without right borders. */
  isFirst?: boolean
  /** Determines if the component is focused. */
  isFocused?: boolean
  /** Helps render component without left borders. */
  isLast?: boolean
  /** Helps render component without left/right borders. */
  isNotOnly?: boolean
  isSubtleReadOnly?: boolean
  /** Label for the input. */
  label?: React.ReactNode
  /** Sets the `max-height` for the input. Used with `multiline`. */
  maxHeight?: number | string
  maxLength?: number
  /** Moves the selection cursor to the end, on focus. Default `false`. */
  moveCursorToEnd?: boolean
  /** Transforms input into an auto-expanding textarea. */
  multiline?: boolean | number
  /** Name for the input. */
  name?: string
  /** Number of characters to offset (bottom-right) for multiline resizing. */
  offsetAmount?: number
  /** Callback when input is blurred. */
  onBlur?: (event: FocusEvent<HTMLInputElement | HTMLTextAreaElement>) => void
  /** Callback when input value is changed. */
  onChange?: (
    value: string,
    event?: ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
  ) => void
  /** Callback when `Enter` is pressed down. */
  onEnterDown?: (
    event: React.KeyboardEvent<HTMLInputElement | HTMLTextAreaElement>
  ) => void
  /** Callback when `Enter` is pressed up. */
  onEnterUp?: (
    event: React.KeyboardEvent<HTMLInputElement | HTMLTextAreaElement>
  ) => void
  onClick?: (event: MouseEvent) => void
  /** Callback when input is focused. */
  onFocus?: (event: FocusEvent) => void
  onKeyDown?: (
    event: React.KeyboardEvent<HTMLInputElement | HTMLTextAreaElement>
  ) => void
  onKeyUp?: (
    event: React.KeyboardEvent<HTMLInputElement | HTMLTextAreaElement>
  ) => void
  /** Callback when input is resized. */
  onResize?: (height: number) => void
  onScroll?: (
    event: React.UIEvent<HTMLInputElement | HTMLTextAreaElement>
  ) => void
  /** Callback when user starts typing, rate limited by `typingThrottleInterval` */
  onStartTyping?: () => void
  /** Callback when user stops typing after delay of `typingTimeoutDelay`. */
  onStopTyping?: () => void
  onWheel?: (event: WheelEvent<HTMLInputElement | HTMLTextAreaElement>) => void
  /** Placeholder text for the input. */
  placeholder?: string
  /** Component to render before the input. */
  prefix?: React.ReactNode
  /** Disable editing of the input. */
  readOnly?: boolean
  /** Exposes `CallStopTyping`, so that it can be called outside itself. */
  refApplyCallStopTyping?: (callback: () => void) => void
  /** Removes the `state` styles on input focus. Default `false`. */
  removeStateStylesOnFocus?: boolean
  required?: boolean
  /** Enables resizing for the textarea (only enabled for `multiline`). */
  resizable?: boolean
  /** Enables scrollLock for component. Default `false`. */
  scrollLock?: boolean
  /** Determines the size of the input. */
  size?: 'sm' | 'md' | 'lg'
  /** Change input to state color. */
  state?: 'error' | 'success' | 'warning' | 'default' | ''
  style?: React.CSSProperties
  /** Component to render after the input. */
  suffix?: React.ReactNode
  tabIndex?: number
  /** Determines the input type. */
  type?: string
  /** Determines the rate limiting interval for firing `onStartTyping`. */
  typingThrottleInterval?: number
  /** Determines the delay of when `onStopTyping` fires after typing stops. */
  typingTimeoutDelay?: number
  /** Initial value of the input. */
  value?: string | number
  /** Adds a char validator UI to the input. */
  withCharValidator?: boolean
  /** Enables typing `onStartTyping` and `onStopTyping` event callbacks. */
  withTypingEvent?: boolean
  innerRef?: (node: HTMLInputElement | HTMLTextAreaElement | null) => void
  tooltipProps?: object
  width?: string | number
  /** HTML autocomplete attribute */
  autoComplete?: string
  /** HTML autocomplete attribute (legacy prop name) */
  autocomplete?: string
  /** Determines the input type (legacy prop name). */
  inputType?: string
  /** tabIndex (legacy prop name) */
  tabindex?: string | number
  /** Data attr for Cypress tests. */
  'data-cy'?: string
} & Omit<
  React.HTMLProps<HTMLInputElement>,
  'onChange' | 'size' | 'action' | 'prefix' | 'label'
>

interface InputState {
  id: string
  isFocused?: boolean
  state?: string
  typingThrottle: ReturnType<typeof setInterval> | undefined
  typingTimeout: ReturnType<typeof setTimeout> | undefined
  value?: string | number
  validatorCount: number
  height?: number
}

export class Input extends React.PureComponent<InputProps, InputState> {
  static Backdrop = InputBackdropV2
  static Resizer = InputResizer

  static defaultProps: InputProps = {
    autoFocus: false,
    charValidatorLimit: 500,
    'data-cy': 'Input',
    disabled: false,
    forceAutoFocusTimeout: 0,
    hasInsertCarriageReturns: false,
    innerRef: noop,
    inputRef: noop,
    isFirst: false,
    isFocused: false,
    isLast: false,
    isNotOnly: false,
    isSubtleReadOnly: false,
    maxHeight: 320,
    maxLength: 524288,
    moveCursorToEnd: false,
    multiline: undefined,
    offsetAmount: 0,
    onBlur: noop,
    onClick: noop,
    onChange: noop,
    onEnterDown: noop,
    onEnterUp: noop,
    onFocus: noop,
    onKeyDown: noop,
    onKeyUp: noop,
    onResize: noop,
    onStartTyping: noop,
    onStopTyping: noop,
    onWheel: noop,
    readOnly: false,
    refApplyCallStopTyping: noop,
    removeStateStylesOnFocus: false,
    required: false,
    resizable: false,
    scrollLock: false,
    size: 'md',
    state: 'default',
    style: {},
    type: 'text',
    typingThrottleInterval: 500,
    typingTimeoutDelay: 5000,
    value: '',
    withCharValidator: false,
    withTypingEvent: false,
  }

  computedStyles: { paddingBottom: number } | undefined
  inputNode: HTMLInputElement | HTMLTextAreaElement | null = null

  constructor(props: InputProps) {
    super(props)

    this.state = {
      id: props.id || uniqueID(),
      isFocused: props.isFocused,
      state: props.state,
      typingThrottle: undefined,
      typingTimeout: undefined,
      value: props.value,
      validatorCount:
        (props.charValidatorLimit as number) - (props.value as string).length,
    }
  }

  autoFocusTimeoutId: ReturnType<typeof setTimeout> = setTimeout(() => '', 0)

  componentDidMount() {
    this.maybeForceAutoFocus()
    this.props.withTypingEvent &&
      this.props.refApplyCallStopTyping?.(this.callStopTyping.bind(this))
  }

  UNSAFE_componentWillReceiveProps(nextProps: InputProps) {
    const { isFocused, value, state } = nextProps
    const prevValue = this.state.value
    const prevState = this.state.state

    if (value !== prevValue) {
      this.setValue(value)
    }

    if (state !== prevState) {
      this.setState({ state: state || 'default' })
    }

    if (isFocused) {
      this.forceAutoFocus()
    }
  }

  componentWillUnmount() {
    this.inputNode = null
    this.props.withTypingEvent && this.clearTypingTimeout()
    this.autoFocusTimeoutId && clearTimeout(this.autoFocusTimeoutId)
  }

  setValue = (value: string | number | undefined) => {
    const { withCharValidator, charValidatorLimit } = this.props
    const nextValue = value

    this.setState({ value: nextValue })

    if (withCharValidator) {
      this.setState({
        validatorCount: (charValidatorLimit as number) - `${value}`.length,
      })
    }

    return nextValue
  }

  maybeForceAutoFocus() {
    const { autoFocus, isFocused } = this.props

    if (autoFocus || isFocused) {
      this.forceAutoFocus()
      this.moveCursorToEnd()
    }
  }

  forceAutoFocus() {
    const { forceAutoFocusTimeout } = this.props

    this.autoFocusTimeoutId = setTimeout(() => {
      if (this.inputNode) {
        this.inputNode.focus()
      }

      this.setState({
        isFocused: true,
      })
    }, forceAutoFocusTimeout)
  }

  callStartTyping() {
    if (this.props.onStartTyping) {
      this.props.onStartTyping()
      this.setThrottler()
    }
  }

  callStopTyping() {
    if (this.state.typingTimeout) {
      this.clearThrottler()
      this.props.onStopTyping?.()
      this.clearTypingTimeout()
    }
  }

  clearTypingTimeout() {
    if (this.state.typingTimeout) {
      clearTimeout(this.state.typingTimeout)
      this.setState({ typingTimeout: undefined })
    }
  }

  setTypingTimeout() {
    this.setState({
      typingTimeout: setTimeout(() => {
        this.clearThrottler()
        this.callStopTyping()
      }, this.props.typingTimeoutDelay),
    })
  }

  clearThrottler() {
    if (this.state.typingThrottle) {
      clearInterval(this.state.typingThrottle)
      this.setState({ typingThrottle: undefined })
    }
  }

  setThrottler() {
    this.setState({
      typingThrottle: setInterval(
        this.props.onStartTyping as () => void,
        this.props.typingThrottleInterval
      ),
    })
  }

  typingEvent() {
    // reset the stop debouncer every time a key is pressed
    this.clearTypingTimeout()
    this.setTypingTimeout()
    if (!this.state.typingThrottle) {
      // if there is no throttler add it
      this.callStartTyping()
    }
  }

  handleOnChange = (
    event: ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
  ) => {
    if (this.props.withTypingEvent) this.typingEvent()

    const value = event.currentTarget.value
    const nextValue = this.setValue(value) as string
    this.props.onChange?.(nextValue, event)
  }

  handleOnInputBlur = (
    event: FocusEvent<HTMLInputElement | HTMLTextAreaElement>
  ) => {
    this.setState({
      isFocused: false,
    })
    this.props.onBlur?.(event)
  }

  handleOnInputFocus = (
    event: FocusEvent<HTMLInputElement | HTMLTextAreaElement>
  ) => {
    const { onFocus, removeStateStylesOnFocus } = this.props
    const { state } = this.state
    if (removeStateStylesOnFocus && state) {
      this.setState({ state: 'default' })
    }
    this.setState({
      isFocused: true,
    })
    this.moveCursorToEnd()
    onFocus?.(event)
  }

  handleOnWheel = (
    event: WheelEvent<HTMLInputElement | HTMLTextAreaElement>
  ) => {
    const { multiline, onWheel, scrollLock } = this.props
    event.stopPropagation()

    if (!multiline || !scrollLock) return

    const stopPropagation = true
    scrollLockY(event as any, stopPropagation)
    onWheel?.(event)
  }

  insertCarriageReturnAtCursorIndex(
    event: React.KeyboardEvent<HTMLInputElement | HTMLTextAreaElement>
  ) {
    const target = event.currentTarget as HTMLInputElement | HTMLTextAreaElement
    const cursorIndex = target.selectionStart as number
    const nextValue = target.value
    const prevValue = this.state.value as string

    // this prevents a return being inserted if the field is completely empty
    // this works on every modifier key, and with standalone returns
    const isEmptyField =
      cursorIndex === 0 && nextValue.length === 0 && prevValue.length === 0

    if (isEmptyField) {
      event.preventDefault() // prevents shift and return from inserting a line break
      return
    }

    if (!isModifierKeyPressed(event as any)) return
    // this inserts a return into the value if a modifier key is also pressed
    event.preventDefault()
    event.stopPropagation()
    const newValue = `${nextValue.substr(0, cursorIndex)}\n${nextValue.substr(
      cursorIndex
    )}`
    this.setState({ value: newValue }, () => {
      this.props.onChange?.(this.state.value as string)

      this.inputNode?.setSelectionRange(cursorIndex + 1, cursorIndex + 1)
    })
  }

  handleOnKeyDown = (
    event: React.KeyboardEvent<HTMLInputElement | HTMLTextAreaElement>
  ) => {
    const { hasInsertCarriageReturns } = this.props

    if (event.keyCode === Keys.ENTER) {
      this.props.onEnterDown?.(event)
    }

    if (hasInsertCarriageReturns && event.keyCode === Keys.ENTER) {
      this.insertCarriageReturnAtCursorIndex(event)
    }

    this.props.onKeyDown?.(event)
  }

  handleOnKeyUp = (
    event: React.KeyboardEvent<HTMLInputElement | HTMLTextAreaElement>
  ) => {
    if (event.keyCode === Keys.ENTER) {
      this.props.onEnterUp?.(event)
    }
    this.props.onKeyUp?.(event)
  }

  handleExpandingResize = (height: number) => {
    this.props.onResize?.(height)
    this.setState({ height })
    this.setComputedStylesFromHeight(height)
  }

  moveCursorToEnd = () => {
    // Not reliably testable in JSDOM
    if (
      !this.props.moveCursorToEnd ||
      !this.inputNode ||
      !isTextArea(this.inputNode)
    )
      return

    requestAnimationFrame(() => {
      moveCursorToEnd(this.inputNode)
    })
  }

  setInputNodeRef = (node: HTMLInputElement | HTMLTextAreaElement | null) => {
    this.inputNode = node
    this.props.inputRef?.(node)
    this.props.innerRef?.(node)
  }

  // Assumption: The padding-bottom does not change after the component is
  // rendered.
  setComputedStylesFromHeight = (height: number) => {
    if (!height) return
    if (this.computedStyles) return
    if (!this.inputNode) return

    const computedStyles = window.getComputedStyle(this.inputNode)

    const { paddingBottom } = computedStyles
    this.computedStyles = {
      paddingBottom: parseInt(paddingBottom, 10),
    }
  }

  getHelpTextMarkup() {
    const { helpText, label } = this.props
    const isCompact = !!label

    return (
      helpText && (
        <div
          className={classNames('c-Input__helpText', isCompact && 'is-compact')}
          data-cy="HelpText"
        >
          {helpText}
        </div>
      )
    )
  }

  getHintTextMarkup() {
    const { hintText } = this.props

    return (
      hintText && (
        <div className="c-Input__hintText" data-cy="HelpText">
          {hintText}
        </div>
      )
    )
  }

  getLabelMarkup() {
    const { label, required } = this.props
    const { id: inputID } = this.state

    return (
      label && (
        <Label className="c-Input__label" for={inputID} required={required}>
          {label}
        </Label>
      )
    )
  }

  getInlinePrefixSuffixClassName({ type }: { type: string }) {
    const { multiline, state } = this.props

    return classNames(
      'c-Input__item',
      type && `is-${type}`,
      multiline && 'is-multiline',
      state && `is-${state}`
    )
  }

  getInlinePrefixMarkup() {
    const { inlinePrefix } = this.props

    return (
      inlinePrefix && (
        <InlinePrefixSuffixUI
          className={this.getInlinePrefixSuffixClassName({ type: 'prefix' })}
        >
          {inlinePrefix}
        </InlinePrefixSuffixUI>
      )
    )
  }

  getPrefixMarkup() {
    const { prefix } = this.props

    return prefix && <InputAffix variant="prefix">{prefix}</InputAffix>
  }

  getInlineSuffixMarkup() {
    const { inlineSuffix } = this.props

    return (
      inlineSuffix && (
        <InlinePrefixSuffixUI
          className={this.getInlinePrefixSuffixClassName({ type: 'suffix' })}
        >
          {inlineSuffix}
        </InlinePrefixSuffixUI>
      )
    )
  }

  getSuffixMarkup() {
    const { suffix } = this.props

    return suffix && <InputAffix variant="suffix">{suffix}</InputAffix>
  }

  getActionMarkup() {
    const { action } = this.props

    return (
      action && (
        <InputAffix variant="suffix" isAction>
          {action}
        </InputAffix>
      )
    )
  }

  getErrorMarkup() {
    const { errorIcon, errorMessage, state, tooltipProps } = this.props
    const shouldRenderError = state === 'error'

    if (!shouldRenderError) return null

    return (
      <InlinePrefixSuffixUI
        className={this.getInlinePrefixSuffixClassName({
          type: 'suffix',
        })}
      >
        <Tooltip
          display="block"
          placement="top-end"
          title={errorMessage}
          withTriggerWrapper
          tabIndex={-1}
          zIndex={999999} // to make sure the tooltip is visible within a modal
          {...tooltipProps}
        >
          <Icon
            className="c-Input__errorIcon"
            icon={errorIcon as IconType}
            size={24}
            state="error"
          />
        </Tooltip>
      </InlinePrefixSuffixUI>
    )
  }

  getMultilineValue() {
    const { multiline } = this.props
    return typeof multiline === 'number' ? multiline : 1
  }

  getCharValidatorMarkup() {
    const { charValidatorLimit } = this.props
    const { value, isFocused, validatorCount } = this.state

    // shows validator green at 50% rounded down to the nearest 10th
    const charValidatorShowAt =
      Math.floor((charValidatorLimit as number) / 2 / 10) * 10 ||
      (charValidatorLimit as number) / 2

    // shows validator yellow at 20% rounded down to the nearest 10th
    const isLessThanAFifth =
      validatorCount <=
      (Math.floor((charValidatorLimit as number) / 5 / 10) * 10 ||
        (charValidatorLimit as number) / 5)

    // shows validator red when reached the limit
    const isTooMuch = validatorCount <= 0

    const isVisible =
      isFocused &&
      validatorCount <= charValidatorShowAt &&
      (value as string).length >= validatorCount

    function getBadgeColor() {
      if (isTooMuch) {
        return 'red'
      } else if (isLessThanAFifth) {
        return 'yellow'
      } else {
        return 'green'
      }
    }

    return isVisible ? (
      <CharValidatorUI className="c-Input__CharValidator">
        <Badge badgeColor={getBadgeColor()}>
          <div className="c-Input__CharValidator__Text">
            {this.state.validatorCount}
          </div>
        </Badge>
      </CharValidatorUI>
    ) : null
  }

  getResizerMarkup() {
    const { multiline, offsetAmount } = this.props
    const { height, value } = this.state

    const resizer =
      multiline != null ? (
        <InputResizer
          contents={value as string}
          currentHeight={height}
          minimumLines={this.getMultilineValue()}
          offsetAmount={offsetAmount}
          onResize={this.handleExpandingResize}
        />
      ) : null

    return resizer
  }

  getInputMarkup = (props: { id?: string }) => {
    const {
      autoFocus,
      charValidatorLimit,
      className,
      disabled,
      errorIcon,
      errorMessage,
      forceAutoFocusTimeout,
      helpText,
      hintText,
      inputRef,
      isFirst,
      isFocused,
      isLast,
      isNotOnly,
      isSubtleReadOnly,
      label,
      maxHeight,
      maxLength,
      moveCursorToEnd,
      multiline,
      name,
      offsetAmount,
      onBlur,
      onEnterDown,
      onEnterUp,
      onFocus,
      onResize,
      onScroll,
      onStartTyping,
      onStopTyping,
      onWheel,
      placeholder,
      prefix,
      readOnly,
      refApplyCallStopTyping,
      removeStateStylesOnFocus,
      resizable,
      scrollLock,
      size,
      state: stateProp,
      style: styleProp,
      suffix,
      tabIndex,
      type,
      typingThrottleInterval,
      typingTimeoutDelay,
      withCharValidator,
      withTypingEvent,
      ...rest
    } = this.props

    const { height, value, state, validatorCount } = this.state

    const style = multiline
      ? {
          height,
          maxHeight,
        }
      : undefined

    return (
      <FieldUI
        as={multiline ? 'textarea' : 'input'}
        {...getValidProps(rest)}
        // eslint-disable-next-line jsx-a11y/no-autofocus
        autoFocus={this.state.isFocused}
        className={classNames(
          'c-Input__inputField',
          'c-InputField',
          maxHeight && 'has-maxHeight',
          multiline && 'is-multiline',
          !isSubtleReadOnly && readOnly && 'is-readonly',
          resizable && 'is-resizable',
          state && `is-${state}`,
          size && `is-${size}`,
          withCharValidator && validatorCount <= 0 && 'is-error'
        )}
        disabled={disabled}
        id={props.id || this.state.id}
        ref={this.setInputNodeRef}
        maxLength={
          withCharValidator ? (charValidatorLimit as number) : maxLength
        }
        name={name}
        onBlur={this.handleOnInputBlur}
        onChange={this.handleOnChange}
        onFocus={this.handleOnInputFocus}
        onKeyDown={this.handleOnKeyDown}
        onKeyUp={this.handleOnKeyUp}
        onWheel={this.handleOnWheel}
        placeholder={placeholder}
        readOnly={readOnly}
        style={style}
        tabIndex={tabIndex}
        type={type}
        value={value}
      />
    )
  }

  render() {
    const {
      className,
      disabled,
      isFirst,
      isNotOnly,
      isLast,
      isSubtleReadOnly,
      maxHeight,
      multiline,
      readOnly,
      resizable,
      style: styleProp,
      width,
      withCharValidator,
    } = this.props
    const { isFocused, value, state, validatorCount } = this.state
    const isReadOnly = !isSubtleReadOnly && readOnly

    return (
      <FormLabelContext.Consumer>
        {props => (
          <InputWrapperUI
            className="c-InputWrapper"
            style={{
              ...styleProp,
              maxWidth: width,
            }}
          >
            {this.getLabelMarkup()}
            {this.getHelpTextMarkup()}
            <InputUI
              className={classNames(
                'c-Input',
                disabled && 'is-disabled',
                isFocused && 'is-focused',
                maxHeight && 'has-maxHeight',
                multiline && 'is-multiline',
                isReadOnly && 'is-readonly',
                resizable && 'is-resizable',
                state && `is-${state}`,
                withCharValidator && validatorCount <= 0 && 'is-error',
                value && 'has-value',
                className
              )}
            >
              {this.getPrefixMarkup()}
              {this.getInlinePrefixMarkup()}
              {this.getInputMarkup(props as { id?: string })}
              {this.getInlineSuffixMarkup()}
              {this.getErrorMarkup()}
              {this.getSuffixMarkup()}
              {this.getActionMarkup()}
              <InputBackdropV2
                className="c-Input__backdrop"
                disabled={disabled}
                isFirst={isFirst}
                isFocused={isFocused}
                isNotOnly={isNotOnly}
                isLast={isLast}
                readOnly={isReadOnly}
                state={state}
                validatorCount={withCharValidator ? validatorCount : 1}
              />
              {this.getResizerMarkup()}
              {withCharValidator && this.getCharValidatorMarkup()}
            </InputUI>
            {this.getHintTextMarkup()}
          </InputWrapperUI>
        )}
      </FormLabelContext.Consumer>
    )
  }
}

export default Input
