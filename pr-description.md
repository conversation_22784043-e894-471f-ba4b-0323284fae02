# PR Description Sections

## Problem | Feature

Currently, the attachment command appears in the editor command menu (`/`) for all scenarios, including when editing published replies. This creates a confusing user experience because:

1. **Published replies are emails that have already been sent** - adding attachments to them doesn't make logical sense since the email cannot be modified after sending
2. **Users can attempt to add attachments** to published replies, leading to confusion about whether the attachment will be included in the already-sent email
3. **No distinction is made** between different editor contexts (new replies, drafts, published content, channels, notes)

The attachment command should only be available when it makes business sense to add attachments.

## Solution

Implemented conditional logic in `ConversationEditor.js` to hide the attachment command in specific scenarios:

### Logic Implementation
```javascript
// Hide attachment command for channels or when editing published replies (not notes)
const shouldHideAttachmentCommand =
  isChannels || (threadState === STATE_PUBLISHED && !isNote)
```

### Behavior Changes
**✅ Show Attachment Command:**
- New replies (creating fresh emails)
- Editing draft replies (emails not yet sent)
- New notes (internal documentation)
- Editing published notes (internal content can be modified)

**❌ Hide Attachment Command:**
- Editing published replies (emails already sent)
- All channels conversations (channels don't support attachments)

### Technical Details
- Uses `getThreadBeingEdited` selector to determine the current thread state
- Leverages existing `isChannels` and `isNote` flags for context awareness
- Conditionally excludes `addAnAttachment` command from the `sharedCommands` array
- Added `threadState` to `useMemo` dependencies for proper reactivity

## Testing

### Manual Testing Scenarios

To manually verify the attachment command behavior:

#### ✅ Scenarios Where Attachment Command Should Appear

1. **New Email Reply**
   - Open any email conversation
   - Click "Reply" button
   - In the editor, type `/`
   - **Expected:** Attachment command appears in the dropdown

2. **Edit Draft Reply**
   - Create a draft reply (start typing and save as draft)
   - Open the draft for editing
   - Type `/` in the editor
   - **Expected:** Attachment command appears in the dropdown

3. **New Note**
   - Open any conversation
   - Click "Note" button
   - In the editor, type `/`
   - **Expected:** Attachment command appears in the dropdown

4. **Edit Published Note**
   - Find a conversation with existing published notes
   - Click to edit an existing note
   - Type `/` in the editor
   - **Expected:** Attachment command appears in the dropdown

#### ❌ Scenarios Where Attachment Command Should NOT Appear

5. **Edit Published Reply**
   - Find a conversation with sent email replies
   - Click to edit a published/sent reply
   - Type `/` in the editor
   - **Expected:** Attachment command does NOT appear in the dropdown

6. **Channels Conversations (Any State)**
   - Open any channels conversation (Instagram, Facebook, etc.)
   - Try creating new replies, editing drafts, or editing published messages
   - Type `/` in any editor
   - **Expected:** Attachment command does NOT appear in any scenario

### Test Coverage
- **Email conversations:** New replies ✅, Draft editing ✅, Published reply editing ❌
- **Notes:** New notes ✅, Published note editing ✅  
- **Channels:** All scenarios ❌ (channels don't support attachments)

### Edge Cases to Verify
- Switching between reply and note modes in the same conversation
- Opening multiple conversations with different states
- Refreshing the page while editing different content types
